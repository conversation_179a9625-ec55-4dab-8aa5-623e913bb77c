<template>
    <!-- 文书打印 -->
    <div class="conPrint">
        <div style="margin: 16px;">
            <personnel-selector :value="jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
        </div>
        <div class="ws-show">
            <wsdy :formId='formId' :businessId="businessId" :formName="formName" />
        </div>
    </div>
</template>

<script>
import personnelSelector from "@/components/personnel-selector"
import wsdy from './wsdyyl/index.vue'
export default {
    components: { personnelSelector, wsdy },
    data() {
        return {
            jgrybm: this.$route.query.jgrybm ? this.$route.query.jgrybm : '',
            formId: '1948633832811008000',
            businessId: this.$route.query.businessId ? this.$route.query.businessId : '',
            formName: '律师会见登记表',
        }
    }
}
</script>
<style scoped lang="less">
.conPrint {
    display: flex;
    .ws-show {
        width: calc(~'100vw - 220px');
        height: calc(~'100vh - 105px');

    }
}
</style>
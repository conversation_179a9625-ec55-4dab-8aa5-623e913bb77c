/*
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-07-04 16:26:44
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-07-10 17:24:52
 * @FilePath: \rs-acp-web\src\router\ihc\router-health-care.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
let menuMode = localStorage.getItem('menuMode')
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/health-care',
    name: 'health-care',
    meta: {
      title: '卫生防疫'
    },
    redirect: '/health-care/list',
    component: menuMode=='side'?mainNew:main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'list',
        name: 'health-care-list',
        meta: {
          title: '卫生防疫',
          menu: true,
          bread: true
        },
        component: () => import('@/view/health-care/index.vue')
      },
      {
        path: 'sqdj',
        name: 'health-care-sqdj',
        meta: {
          title: '伤情登记',
          menu: true,
          bread: true
        },
        component: () => import('@/view/health-care/sqdj/index.vue')
      }
    ]
  }
]

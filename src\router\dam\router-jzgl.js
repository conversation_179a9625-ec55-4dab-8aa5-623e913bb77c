let menuMode = serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [{
  path: '/jzgl',
  name: 'jzgl',
  meta: {
    title: '卷宗管理'
  },
  redirect: '/jzgl/home',
  component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
  children: [{
      path: 'home',
      name: 'home',
      meta: {
        title: '首页',
        menu: true,
        bread: true
      },
      component: () => import('@/view/jzgl/home.vue')
    },
    {
      path: "message",
      name: "message",
      meta: {
        title: "消息中心",
        menu: false,
        bread: true,
      },
      component: () => import("@/view/jzgl/message/index.vue"),
    },
    {
      path: "wdjy",
      name: "wdjy",
      meta: {
        title: '我的借阅',
        menu: true,
        bread: true
      },
      redirect: "/jzgl/wdjy/index",
      component: () => import("@/components/app-main/subpage.vue"),
      children: [{
          path: "index",
          name: "wdjyIndex",
          meta: {
            menu: true,
            bread: true,
            title: '我的借阅',

          },
          component: () => import("@/view/jzgl/wdjy/index.vue"),
        },
        {
          path: "detail",
          name: "wdjyDetail",
          meta: {
            title: "查看详情",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/jzgl/wdjy/detail.vue"),
        },
        {
          path: "apply",
          name: "wdjyApply",
          meta: {
            title: "申请借阅",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/jzgl/wdjy/apply.vue"),
        }
      ],
    },
    {
      path: "jysp",
      name: "jysp",
      meta: {
        title: "借阅审批",
        menu: true,
        bread: true,
      },
      redirect: "/jzgl/jysp/index",
      component: () => import("@/components/app-main/subpage.vue"),
      children: [{
          path: "index",
          name: "jyspIndex",
          meta: {
            menu: true,
            bread: true,
            title: "借阅审批",
          },
          component: () => import("@/view/jzgl/jysp/index.vue"),
        },
        {
          path: "detail",
          name: "jyspDetail",
          meta: {
            title: "查看详情",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/jzgl/jysp/detail.vue"),
        },
        {
          path: "sp",
          name: "jyspSp",
          meta: {
            title: "审批",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/jzgl/jysp/sp.vue"),
        }
      ],
    },
    {
      path: "jytz",
      name: "jytz",
      meta: {
        title: "卷宗借阅台账",
        menu: true,
        bread: true,
      },
      redirect: "/jzgl/jytz/index",
      component: () => import("@/components/app-main/subpage.vue"),
      children: [{
          path: "index",
          name: "jytzIndex",
          meta: {
            menu: true,
            bread: true,
            title: "卷宗借阅台账",

          },
          component: () => import("@/view/jzgl/jytz/index.vue"),
        },
        {
          path: "detail",
          name: "jytzDetail",
          meta: {
            title: "查看详情",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/jzgl/jytz/detail.vue"),
        },
      ],
    },
  ]
}]

<template>
    <div class="content-defaulet">

        <div class="content-defaulet-main">
            <topStat :list="statData" @statClick="statClick"></topStat>
            <rs-DataGrid ref="grid" funcMark="ylycwz" :customFunc="true" v-if="showFormCompnent">
                <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index }">
                    <Button type="primary" v-if="hasPermission('xzdj')" style="margin-right: 5px;"
                        @click="changeAction('kjcf', row)">远程开具处方</Button>
                </template>
            </rs-DataGrid>
        </div>

        <div v-if="!showFormCompnent">
            <component v-bind:is='component' @on_show_table="on_show_table" :modalTitle="modalTitle" :ryId="ryId">
            </component>
        </div>
<!--

        <Modal v-model="addModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1100"
            title="新增现场巡诊">
            <ryxx ref="rxyy" @openRY="openModal = true" :ryxx="ryxx"></ryxx>
            <div slot="footer">
                <Button type="primary" @click="addSubmit" class="save">提交</Button>
                <Button @click="addModal = false" class="save">关 闭</Button>
            </div>
        </Modal>

        <Modal  v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
            title="人员列表">
            <div class="select-use">
                <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple="false"
                    :selectUseIds="selectUseIds" />
            </div>
            <div slot="footer">
                <Button type="primary" @click="useSelect" class="save">确 定</Button>
                <Button @click="openModal = false" class="save">关 闭</Button>
            </div>
        </Modal> -->

    </div>
</template>

<script>

import { mapActions } from 'vuex'
import { sDataGrid } from 'sd-data-grid'
import topStat from '@/components/top-stat'
import kjcf from './kjcf.vue'
import kjjcd from './kjjcd.vue'
import fileImg from '@/assets/icon/template-file.svg'
import addImg from '@/assets/icon/common-add.svg'
import { prisonSelect } from 'sd-prison-select'
import ryxx from '@/components/bl-form/ryxx.vue'
export default {
  components: {
    sDataGrid,
    topStat,
    kjcf,
    kjjcd,
    prisonSelect,
    ryxx
  },
  data () {
    return {
      statData: [
        {
          img: fileImg,
          title: '今日待处理',
          value: ''
        },
        {
          img: fileImg,
          title: '今日已处理',
          value: ''
        }
      ],
      showFormCompnent: true,
      component: null,
      modalTitle: '',
      ryId: null,
      addModal: false,
      openModal: false,
      selectUseIds: '',
      ryxx: {}
    }
  },

  mounted () {
    this.getGridData()
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    getGridData () {
      this.postRequest({
        url: this.$path.get_query_grid,
        params: { modelId: 'visit:count:list' }
      }
      ).then(res => {
        if (res.success) {
          let data = res.rows[0]
          this.statData[0].value = data.todo_num
          this.statData[1].value = data.done_num
        }
      })
    },
    statClick (e) {
      if (e.title == '新增') {
        this.ryxx = {}
        this.modalTitle = '新增现场巡诊'
        this.ryId = ''
        this.showFormCompnent = false
        this.component = 'kjcf'
      }
    },
    changeAction (type, row) {
      if (type == 'kjcf') {
        this.modalTitle = '巡诊登记'
        this.ryId = row.id
        this.showFormCompnent = false
        this.component = 'kjcf'
      } else if (type == 'kjjcd') {
        this.modalTitle = '巡诊登记'
        this.ryId = row.id
        this.showFormCompnent = false
        this.component = 'kjjcd'
      }
    },
    on_refresh_table () {
      this.getGridData()
      this.$refs.grid.query_grid_data(1)
    },
    on_show_table () {
      this.showFormCompnent = true
      this.component = null
      this.getGridData()
    },
    useSelect () {
      this.ryxx = { ...this.ryxx, ...this.$refs.prisonSelect.checkedUse[0] }
      this.selectUseIds = this.ryxx.jgrybm
      this.openModal = false
    }
    // addSubmit() {

    //     this.$refs.rxyy.$refs.formData.validate((valid) => {
    //         if (valid) {
    //             console.log(this.ryxx, 999)
    //             // this.authPostRequest({
    //             //     url:this.$path.create_snmz,
    //             //     params:{

    //             //     }
    //             // })
    //         }
    //     })

    // }
  }
}
</script>

<style lang="less" scoped></style>

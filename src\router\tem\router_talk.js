import mainNew from "@/components/main-menu-new/main.vue";
import main from '@/components/main/main.vue'
let menuMode = localStorage.getItem('menuMode')
export default [{
  path: "/intalk",
  name: "intalk",
  redirect: "/intalk/talkeducation",
  meta: {
    title: "个别谈话教育",
  },
  component: menuMode === "side" ? mainNew : () => import("@/components/app-main/index.vue"), //main,
  // component: () => import("@/components/app-main/index.vue"),
  // component: ,
  children: [{
      path: "talkeducation",
      name: "talkeducation",
      meta: {
        title: "个别谈话教育",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/intalk/index.vue"),
    },
    {
      path: "business",
      name: "business",
      meta: {
        title: "业务台账",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/intalk/business.vue"),
    },
    {
      path: "thjymb",
      name: "thjymb",
      meta: {
        title: "基础配置-谈话教育模板",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/jcpz/index.vue"),
    },
    {
      path: "thtwzs",
      name: "thtwzs",
      meta: {
        title: "基础配置-谈话提问助手",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/jcpz/thtwzs.vue"),
    },
    {
      path: "thrybq",
      name: "thrybq",
      meta: {
        title: "基础配置-谈话人员标签",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/jcpz/thrybq.vue"),
    },
    {
      path: "zdpz",
      name: "zdpz",
      meta: {
        title: "基础配置-谈话终端配置",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/jcpz/zdpz.vue"),
    },
    {
      path: "jtjy",
      name: "jtjy",
      meta: {
        title: "集体谈话教育",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/groupTalk/index.vue"),
    },
    {
      path: "jtjyjls",
      name: "jtjyjls",
      meta: {
        title: "集体教育-拘留所",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/groupTalk/jls.vue"),
    },
    {
      path: "jtjyywtz",
      name: "jtjyywtz",
      meta: {
        title: "集体教育-业务台账",
        menu: true,
        bread: true,
      },
      component: () => import("@/view/groupTalk/ywtz.vue"),
    },
  ],
}, ];

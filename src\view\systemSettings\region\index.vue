<template>
	<div>
    <div class="bsp-base-form">
      <!-- <div class="bsp-base-content"> -->
       
        <div class="wrap-cont">
          <div style="width: 280px;margin-right: 10px;">
            <areaTree  @selectChange="handleXzChange" :treeData="treeData"></areaTree>
          </div>
          <s-DataGrid ref="grid" funcMark="szptqygllb" :customFunc="true" :params="params" v-if="!showData" style="width: calc(100% - 280px); padding: 10px 10px 0px 10px">
            <template slot="customHeadFunc" slot-scope="{ func }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptqygllb:add')" @click.native="addRegion('add')">新增</Button>
              <Button type="default" v-if="func.includes(globalAppCode + ':szptqygllb:import')" style="border: 1px solid #57a3f3; color: #57a3f3;" @click.native="triggerFileInput">导入</Button>
                <input
                  type="file"
                  ref="fileInput"
                  style="display: none"
                  accept=".xls,.xlsx"
                  @change="handleFileChange"
                >
                <Button type="default" v-if="func.includes(globalAppCode + ':szptqygllb:tem')" style="border: 1px solid #57a3f3; color: #57a3f3;" @click.native="temEvent">模板</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptqygllb:edit')" @click.native="addRegion('edit',row)" >编辑</Button>
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptqygllb:detail')" @click.native="addRegion('detail',row)">详情</Button>
              <Button type="error" v-if="func.includes(globalAppCode + ':szptqygllb:delete')" @click.native="deleleChange(row)">删除</Button>
              </template>
          </s-DataGrid>
        </div>
      <!-- </div> -->

      <Modal v-model="regionModal"  width="70%" :closable="false">
        <div class="flow-modal-title" slot="header">
          <span style="font-size: 17px;">{{ regionTitle }}</span>
          <span @click.stop="cancelRegion" style="position: absolute; right: 6px;cursor: pointer;">
              <i class="ivu-icon ivu-icon-ios-close"></i>
          </span>
        </div>
       <div>
        <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="130" label-colon>
          <p class="title">区域信息</p>
          <Row>
            <Col span="8">
              <FormItem label="区域类型" prop="areaType" :rules="[{ trigger: 'change', message: '区域类型为必填', required: true }]">
                <!-- <Input type="text" v-model="formData.areaType"/> -->
                <s-dicgrid v-model="formData.areaType" @values="getqylxName" :disabled="regionType == 'edit'" :isSearch="true" dicName="ZD_QYLXDM" />
              </FormItem>
            </Col>
            <Col span="8" v-if="!isRoomCode">
              <FormItem label="所属机构" prop="areaCode"  :rules="[{ trigger: 'change', message: '必选', required: true }]">
                <s-dicgrid v-model="formData.areaCode" @values="getqyName" :disabled="regionType == 'edit'" :isSearch="true" dicName="ZD_ORG_ID" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="区域名称" prop="areaName"  :rules="[{ trigger: 'blur', message: '必选', required: true }]">
                <Input type="text" v-model="formData.areaName" placeholder="请填写"/>
              </FormItem>
              <!-- <FormItem label="区域名称" prop="areaName" v-else-if="regionType == 'edit'">
                <Input type="text" v-model="formData.areaName" placeholder="请填写"/>
              </FormItem> -->
              <!-- <FormItem label="区域名称" prop="areaNames" v-if="isShow">
                <s-dicgrid v-model="areaNames" @values="getqyName" :disabled="regionType == 'edit'" :isSearch="true" dicName="ZD_ORG_ID" />
              </FormItem> -->
            </Col>
            <Col span="8" v-if="isRoomCode">
              <FormItem label="父节点名称" prop="parentName" style="margin-left: 20px;">
                <!-- <Input type="text" v-model="formData.parentName"/> -->
                <p style="line-height: 35px;">{{ currentData.parentName }}</p>
              </FormItem>
            </Col>
          </Row>
          <div v-if="monitoringRoomForm">
            <div class="jsform">
              <p class="title">监室属性</p>
            <Row>
              <Col span="8">
                <FormItem label="关押量" prop="imprisonmentAmount">
                  <Input type="number" v-model="formData.imprisonmentAmount" placeholder="请填写"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="是否启用" prop="status" :rules="[{ trigger: 'blur,change', message: '必选', required: true }]">
                  <RadioGroup v-model="formData.status">
                    <Radio label="1">启用</Radio>
                    <Radio label="0">停用</Radio>
                </RadioGroup>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="性别类型" prop="roomSex">
                  <s-dicgrid v-model="formData.roomSex" @values="getXbName" :isSearch="true" dicName="ZD_XB" />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="监室类型" prop="roomType"  :rules="[{ trigger: 'blur,change', message: '必填', required: true }]">
                  <s-dicgrid v-model="formData.roomType" @values="getJslxName" :isSearch="true" dicName="ZD_ZXGJSLX" />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="监室面积" prop="roomArea" style="width: 100%;" class="input-container">
                  <Input type="number" v-model="formData.roomArea" placeholder="请填写" />
                  <span class="unit">㎡</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="人均铺位面积" prop="c" style="width: 100%;" class="input-container">
                  <Input type="number" v-model="formData.avgBedsArea" placeholder="请填写" />
                  <span class="unit">㎡</span>
                </FormItem>
              </Col>
            </Row>
            </div>
            <div class="gjform">
              <p class="title">主协管民警</p>
              <Row>
                <Col span="8">
                  <FormItem label="主管民警" prop="userIdZb">
                    <user-selector style="width: 500px" v-model="formData.userIdZb" tit="用户选择" @onSelect="onSelectZb"
                      :text.sync="formData.userNameZb" returnField="id" numExp='num==1' msg="至少选中1人">
                      </user-selector>
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="8">
                  <FormItem label="协管民警" prop="userIdXb">
                    <user-selector style="width: 500px" v-model="formData.userIdXb" tit="用户选择" @onSelect="onSelectXb"
                      :text.sync="formData.userNameXb" returnField="id" numExp='num>=1' msg="至少选中1人">
                      </user-selector>
                  </FormItem>
                </Col>
              </Row>
            </div>
          </div>
        </Form>
       </div>
        <div slot="footer">
          <Button @click="handleCancel" class="cancle_btn" style="pointer-events: auto;">取消</Button>
          <Button type="primary" @click.stop="okRegion" class="sure_btn">提交</Button>
        </div>
      </Modal>

      <Modal v-model="regionDetailModal" width="70%" :closable="false">
        <div class="flow-modal-title" slot="header">
          <span style="font-size: 17px;">区域详情</span>
          <span @click="cancelRegionDetail" style="position: absolute; right: 6px;cursor: pointer;">
              <i class="ivu-icon ivu-icon-ios-close"></i>
          </span>
        </div>
        <div>
            <div class="djxx-wrap" style="margin-bottom: 20px; border-botton: 1px solid #000">
              <p class="djxx-title">区域信息</p>
              <Row>
                <Col span="2">区域名称</Col>
                <Col span="6">{{detailData.areaName ? detailData.areaName : '-'}}</Col>
                <Col span="2">区域编码</Col>
                <Col span="6">{{detailData.areaCode ? detailData.areaCode : '-'}}</Col>
                <Col span="2">父节点名称</Col>
                <Col span="6">{{detailData.parentName || rowData.parentName}}</Col>
              </Row>
              <Row>
                <Col span="2">区域类型</Col>
                <Col span="22">
                    {{detailData.areaTypeName ? detailData.areaTypeName : '-'}}
                </Col>
              </Row>
            </div>
            <div class="djxx-wrap" style="margin-top: 20px;" v-if="monitoringRoomForm">
              <p class="djxx-title">监室属性</p>
              <Row>
                  <Col span="2">关押量</Col>
                  <Col span="6">{{detailData.gyl ? detailData.gyl : '-'}}</Col>
                  <Col span="2">是否启用</Col>
                  <Col span="6">{{detailData.status == '1' ? '启用' : '停用'}}</Col>
                  <Col span="2">性别类型</Col>
                  <Col span="6">{{detailData.areaPrisonRoomRespVO.roomSexName ? detailData.areaPrisonRoomRespVO.roomSexName : '-'}}</Col>
              </Row>
              <Row>
                  <Col span="2">监室类型</Col>
                  <Col span="6">{{detailData.areaPrisonRoomRespVO.roomTypeName ? detailData.areaPrisonRoomRespVO.roomTypeName : '-'}}</Col>
                  <Col span="2">监室面积</Col>
                  <Col span="6">{{detailData.roomArea ? detailData.roomArea : '0'}}㎡</Col>
                  <Col span="2">人均铺位面积</Col>
                  <Col span="6">{{detailData.avgBedsArea ? detailData.avgBedsArea : '0'}}㎡</Col>
              </Row>
              <p class="djxx-title">主协管民警</p>
              <Row>
                  <Col span="2">主管民警</Col>
                  <Col span="6">{{detailData.userNameZb ? detailData.userNameZb : '-'}}</Col>
              </Row>
              <Row>
                  <Col span="2">协管民警</Col>
                  <Col span="6">{{detailData.userNameXb ? detailData.userNameXb : '-'}}</Col>
              </Row>
            </div>
        </div>
        <div slot="footer" style="text-align: center;">
          <Button @click="cancelRegionDetail" class="cancle_btn">取消</Button>
          <!-- <Button type="primary" @click="okRegion" class="save">提交</Button> -->
        </div>
      </Modal>
    </div>
  </div>
  </template>

<script>
import axios from 'axios';
import { fileUpload } from 'sd-minio-upfile'
import { userSelector } from 'sd-user-selector'
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import areaTree from '../components/area-tree.vue'
import {getToken} from '@/libs/util'
export default {
  components: {
    sDataGrid,
    areaTree,
    userSelector,
    fileUpload
  },
  data () {
    return {
      params: {
        area_code: '',
        area_name: '',
        parent_id: '',
        area_type: '',
        org_code: ''
      },
      regionType: 'add',
      originTreeData: [],
      treeData: [],
      showData: false,
      columns: [
        {
          name: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        { title: '区域名称', key: 'areaName', align: 'center', tooltip: true },
        { title: '区域编码', key: 'areaCode', align: 'center', tooltip: true },
        { title: '父节点名称', key: 'orgName', align: 'center', tooltip: true },
        { title: '区域类型', align: 'center', tooltip: true, key: 'areaType' },
        { title: '创建时间', align: 'center', tooltip: true, key: 'addTime' },
        { title: '更新时间', align: 'center', tooltip: true, key: 'updateTime' },
        { title: '操作', slot: 'action', width: 200, align: 'center' }
      ],
      dataTable: [],
      page: {
        pageNo: 1,
        pageSize: 10,
        msgType: '01',
        orgCode: this.$store.state.common.orgCode
      },
      total: 0,
      regionModal: false,
      regionTitle: '区域新增',
      ruleValidate: {},
      formData: {
        areaName: '',
        parentName: '',
        parentId: '',
        areaType: '',
        imprisonmentAmount: 0,
        status: '',
        roomSex: '',
        roomType: '',
        roomArea: 0,
        avgBedsArea: 0,
        areaRelatedWarderReqVOList: [],
        // areaPrisonRoomSaveDto: {
        //   imprisonmentAmount: 0,
        //   status: '',
        //   roomSex: '',
        //   roomType: '',
        //   roomArea: 0,
        //   avgBedsArea:0,
        // },
        userIdZb: '',
        userNameZb: '',
        userIdXb: '',
        userNameXb: ''
      },
      defaultFill: true,
      currentData: {
        orgCode: '',
        currentID: ''
      },
      regionDetailModal: false,
      detailData: {},
      defaultTree: {
        defaultId: '',
        defaultOrgCode: ''
      },
      monitoringRoomForm: false, // 区域类型选择‘监室’后显示监室属性标签
      allowAdding: false,
      currentItem: {},
      isRoomCode:true,
      isShow: false,
      areaNames: '',
      isAreaShow: false,
      rowData: {}
    }
  },
  watch:{
    'formData.areaType':{
      handler(n,o) {
        // console.log(n,o);
        if(n !== '0003') {
          this.monitoringRoomForm = false
        }
      },
      formData:{
        handler(value,n) {
          console.log('value',value,n);
        },
        deep: true,
        immediate: true
      }
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest','authGetRequestBlob']),
    // 获取列表
    getList () {
      let orgCode = this.$store.state.common.orgCode
      this.authGetRequest({ url: this.$path.bsp_system_getAreaTree, params: { orgCode: orgCode } }).then(resp => {
        if (resp.success && resp.data) {
          // if(resp.data.length) {
          //   this.isRoomCode = true
          // }
          console.log(resp)
          this.treeData = resp.data
          this.defaultTree.defaultId = this.treeData[0].id
          this.defaultTree.defaultOrgCode = this.treeData[0].orgCode
          this.defaultTree.data = this.treeData[0]
          console.log(this.defaultTree)

          if (this.defaultFill) {
            // console.log(this.treeData[0],'this.treeData[0]');
            this.formData.parentName = this.treeData[0].name
            this.formData.parentId = this.treeData[0].id
            // this.getdefaultTableData(this.defaultTree.defaultId,this.defaultTree.defaultOrgCode)
            this.defaultFill = false
          }
          // // 过滤父节点为行政，且将其直系子节点sjflId置空，便于组成树结构
          // let Id = resp.data.filter(item => !item.sjflId)
          // let treeList = resp.data.filter(item => item.sjflId)
          // treeList.forEach(item => {
          //   if (item.sjflId === Id[0].id) {
          //     item.sjflId = ''
          //   }
          // })
          // this.originTreeData = treeList
          // this.treeData = this.createTree(treeList, '', '1')
        } else {
          // this.errorModal({ content: res.msg })
        }
      })
    },
    getdefaultTableData (id, orgCode) {
      this.postRequest({ url: this.$path.bsp_system_page,
        params: {
          orgCode: orgCode,
          id: id,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize
        }
      }).then(resp => {
        if (resp.success && resp.data) {
          this.dataTable = resp.data.list
          this.total = resp.data.list.length
        }
      })
    },
    getNo (pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getTableData()
    },
    getSize (pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getTableData()
    },
    search () {
      // console.log(this.page,'search')
      this.getTableData()
    },
    getTableData () {
      console.log(this.currentData, 'currentData')
      this.postRequest({
        url: this.$path.bsp_system_page,
        params: {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          id: this.currentData.currentID,
          orgCode: this.currentData.orgCode
          // areaName: this.currentData.areaName,
          // areaCode: this.currentData.areaCode,
          // areaType: this.currentData.areaType
        }
      }).then(res => {
        console.log(res)
      })
    },
    addYp (row) {
      console.log(row)
    },
    handleXzChange (item, data) {
      console.log(item, data)
      this.currentItem = data[0]
      // this.formData.parentId = data[0].id
      this.showData = true
      this.$set(this.params, 'parent_id', data[0].id)
      this.$set(this.formData, 'parentId', data[0].id)
      setTimeout(() => {
        this.showData = false
        this.allowAdding = true
        // this.formData.parentId = data[0].id
        // if(item && !item.isParent) { // 如果点击的当前节点有子节点 就传当前节点自己的id
        //   this.formData.parentId = data[0].parentId
        // } else {
        //   this.allowAdding = true
        //   this.formData.parentId = data[0].id
        // }
        this.currentData.orgCode = data[0].orgCode
        this.currentData.currentID = data[0].id
        this.currentData.parentName = data[0].name
        this.currentData.areaName = data[0].areaName
        this.currentData.areaCode = data[0].areaCode
        this.currentData.areaType = data[0].areaType
        let orgCode = data[0].orgCode
        let id = data[0].id
      }, 100)

      // this.postRequest({ url: this.$path.bsp_system_page, params: {
      //   orgCode: orgCode,
      //   id: id,
      //   pageNo: this.page.pageNo,
      //   pageSize: this.page.pageSize
      //  }
      // }).then(resp => {
      //   if(resp.success && resp.data) {
      //     this.dataTable = resp.data.list
      //     this.total = resp.data.list.length
      //   }
      // })
      console.log(this.params, 'params')
    },
    addRegion (type, row) {
      console.log(type, row)
      this.regionType = type
      if (row && row.area_type == '0003') {
        this.monitoringRoomForm = true
      }
      if (type == 'add') {
        this.formData = {}
        this.$forceUpdate()
        console.log(this.currentItem, 'currentItem')
        console.log(this.formData, 'add')
        this.formData.parentId = this.currentItem.id
        // this.formData.parentName = this.defaultTree.data.areaName
        this.regionTitle = '区域新增'
        this.regionModal = true
      } else if (type == 'edit') {
        this.formData.parentId = row.parent_id
        this.regionTitle = '区域编辑'
        this.formData.parentName = row.parent_name
        this.formData.id = row.id
        this.getRegionDetail(row)
      } else if (type == 'detail') {
        this.detailData.parentName = row.parent_name
        this.getRegionDetail(row)
      }
    },
    deleleChange (row) {
      this.$Modal.confirm({
				title: '温馨提示',
				content: '请确认是否删除?',
				onOk: () => {
          this.authGetRequest({
            url: this.$path.bsp_system_region_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            console.log(res, '删除')
            if (res.success) {
              this.$nextTick(() => {
                this.on_refresh_table()
                this.getList()
              })
            } else {
              this.$Modal.error({
                title: '温馨提示',
                content: res.msg || '该区域节点下有子节点，不允许删除!'
              })
            }
          })
        }
      })
    },
    getRegionDetail (row) {
      console.log(row)
      this.authGetRequest({
        url: this.$path.bsp_system_region_get,
        params: {
          id: row.id
        }
      }).then(res => {
        console.log(res, 'res')
        if (res.success && res.data) {
          if (this.regionType == 'edit') {
            this.formData.areaName = res.data.areaName
            this.formData.parentName = res.data.parentName
            this.formData.areaType = res.data.areaType

            if (res.data.areaPrisonRoomRespVO) {
              this.formData.imprisonmentAmount = res.data.areaPrisonRoomRespVO.imprisonmentAmount
              this.formData.status = res.data.areaPrisonRoomRespVO.status
              this.formData.roomSex = res.data.areaPrisonRoomRespVO.roomSex
              this.formData.roomType = res.data.areaPrisonRoomRespVO.roomType
              this.formData.roomArea = res.data.areaPrisonRoomRespVO.roomArea
              this.formData.avgBedsArea = res.data.areaPrisonRoomRespVO.avgBedsArea
              console.log('1')
              console.log(this.formData)
            }
            if (res.data.areaRelatedWarderReqVOList) {
              console.log(res.data.areaRelatedWarderReqVOList,'res.data.areaRelatedWarderReqVOList')
              res.data.areaRelatedWarderReqVOList.forEach((item) => {
                console.log(item,'item')
                if (item.userType == 'w' && item.policeId !== '') {
                  this.formData.userIdZb = item.policeId
                  this.formData.userNameZb = item.policeName
                } else if (item.userType == 'a' && item.policeId !== '') {
                  this.formData.userIdXb = this.formData.userIdXb || ''
                  this.formData.userNameXb = this.formData.userNameXb || ''

                  // 判断是否需要添加逗号
                  if (this.formData.userNameXb) {
                    this.formData.userNameXb += ',' // 如果不为空，先加逗号
                    this.formData.userIdXb += ','
                  }

                  this.formData.userIdXb += item.policeId
                  this.formData.userNameXb += item.policeName
                }
              })
              console.log(this.formData,'formData')
            }
            this.regionModal = true
          } else if (this.regionType == 'detail') {
            console.log(res.data, 'res.data.parent_name')
            this.detailData = res.data
            console.log(row,'row')
            console.log(this.detailData,'detailData')
            if(row && row.parent_name) {
              this.rowData.parentName = row.parent_name
            }
            this.detailData.areaName = res.data.areaName
            // this.detailData.parentName = res.data.parent_name
            this.detailData.areaCode = res.data.areaCode
            this.detailData.areaType = res.data.areaType
            if (res.data.areaPrisonRoomRespVO) {
              this.detailData.gyl = res.data.areaPrisonRoomRespVO.imprisonmentAmount
              this.detailData.status = res.data.areaPrisonRoomRespVO.areaType
              this.detailData.sex = res.data.areaPrisonRoomRespVO.roomSex
              this.detailData.roomType = res.data.areaPrisonRoomRespVO.roomType
              this.detailData.roomArea = res.data.areaPrisonRoomRespVO.roomArea
              this.detailData.avgBedsArea = res.data.areaPrisonRoomRespVO.avgBedsArea
            }

            if (res.data.areaRelatedWarderReqVOList) {
              console.log(res.data.areaRelatedWarderReqVOList)
              res.data.areaRelatedWarderReqVOList.forEach((item) => {
                console.log(item,'item-res')
                if (item.userType == 'w') {
                  this.detailData.userIdZb = item.policeId
                  this.detailData.userNameZb = item.policeName
                } else if (item.userType == 'a') {
                  this.detailData.userIdXb = this.detailData.userIdXb || ''
                  this.detailData.userNameXb = this.detailData.userNameXb || ''

                  // 判断是否需要添加逗号
                  if (this.detailData.userNameXb) {
                    this.detailData.userNameXb += ',' // 如果不为空，先加逗号
                    this.detailData.userIdXb += ','
                  }

                  this.detailData.userIdXb += item.policeId
                  this.detailData.userNameXb += item.policeName
                }
              })
            }

            this.regionDetailModal = true
          }
        }
      })
    },
    okRegion () {
      console.log(this.formData, '1')
      // return;
      // this.$refs.formData.
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          if (this.regionType == 'add') {
            this.saveData()
          } else {
            this.updateEvent()
          }
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    saveData () {
      console.log(this.formData, '2')
      this.authPostRequest({
        url: this.$path.bsp_system_region_add,
        params: {
          areaName: this.formData.areaName,
          areaType: this.formData.areaType,
          parentId: this.formData.parentId,
          areaCode: this.formData.areaCode,
          areaPrisonRoomSaveDto: {
            imprisonmentAmount: this.formData.imprisonmentAmount,
            roomArea: this.formData.roomArea,
            roomSex: this.formData.roomSex,
            roomType: this.formData.roomType,
            status: this.formData.status,
            avgBedsArea: this.formData.avgBedsArea
          },
          areaRelatedWarderReqVOList: this.formData.areaRelatedWarderReqVOList
        }
      }).then(res => {
        console.log(res, '新增')
        if (res.success) {
          console.log(res, 'res')
          this.regionModal = false
          this.$nextTick(() => {
            this.getList()
          })
          // this.formData = {}
          // // this.getList()
          this.on_refresh_table()
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '接口操作失败!'
          })
        }
      })
    },
    updateEvent () {
      // console.log(this.formData, '2')
      var users = []
      // console.log(this.formData.userIdXb, 'this.formData.userIdXb')
      // console.log(this.formData.userIdZb,'this.formData.userIdZb')
      if (this.formData.userIdZb && this.formData.userIdZb != '') {
        // console.log('1',users)
        // 主办用户
        users.push({
          policeId: this.formData.userIdZb,
          policeName: this.formData.userNameZb,
          userType: 'w'
        })
        // console.log(users, 'users')
      } 
      if(this.formData.userIdXb && this.formData.userIdXb != ''){
        // 参评人用户
        var userIdsXb = this.formData.userIdXb.split(',')
        var userNamesXb = this.formData.userNameXb.split(',')
        // console.log(userIdsXb,'userIdsXb')

        userIdsXb.forEach((id, index) => {
          users.push({
            policeId: id,
            policeName: userNamesXb[index],
            userType: 'a'
          })
        })

        // console.log(users, 'users')
      }

      this.authPostRequest({
        url: this.$path.bsp_system_region_update,
        params: {
          areaName: this.formData.areaName,
          areaType: this.formData.areaType,
          parentId: this.formData.parentId,
          id: this.formData.id,
          areaPrisonRoomSaveDto: {
            imprisonmentAmount: this.formData.imprisonmentAmount,
            roomArea: this.formData.roomArea,
            roomSex: this.formData.roomSex,
            roomType: this.formData.roomType,
            status: this.formData.status,
            avgBedsArea: this.formData.avgBedsArea
          },
          areaRelatedWarderReqVOList: users || []
        }
      }).then(res => {
        console.log(res, '编辑')
        if (res.success) {
          this.regionModal = false
          // this.formData = {}
          // this.getList()
          this.$nextTick(() => {
            this.getList()
          })
          this.on_refresh_table()
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '接口操作失败!'
          })
        }
      })
    },
    cancelRegion (event) {
      console.log('cancelRegion 方法被调用', event)

      // 阻止事件冒泡和默认行为
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }

      try {
        // 先关闭模态框
        this.regionModal = false
        console.log('模态框已关闭，regionModal:', this.regionModal)

        // 使用 nextTick 确保 DOM 更新后再重置数据
        this.$nextTick(() => {
          try {
            // 重置表单数据
            this.formData = {
              areaName: '',
              parentName: '',
              parentId: '',
              areaType: '',
              imprisonmentAmount: 0,
              status: '',
              roomSex: '',
              roomType: '',
              roomArea: 0,
              avgBedsArea: 0,
              areaRelatedWarderReqVOList: [],
              userIdZb: '',
              userNameZb: '',
              userIdXb: '',
              userNameXb: ''
            }

            // 重置监室表单显示状态
            this.monitoringRoomForm = false

            // 清除表单验证状态
            if (this.$refs.formData) {
              this.$refs.formData.resetFields()
            }

            console.log('表单数据已重置')
          } catch (resetError) {
            console.error('重置表单数据时出错:', resetError)
          }
        })

      } catch (error) {
        console.error('cancelRegion 方法执行出错:', error)
        // 即使出错也要确保模态框关闭
        this.regionModal = false
      }
    },
    cancelRegionDetail () {
      this.regionDetailModal = false
    },
    searchBtn () {
      let params = {
        orgCode: this.currentData.orgCode || this.defaultTree.defaultId,
        id: this.currentData.currentID || this.defaultTree.defaultOrgCode,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        areaCode: this.page.bar ? this.page.bar : '',
        areaName: this.page.title ? this.page.title : ''
      }
      console.log(this.page)
      // if(this.page.title) {
      //   console.log('if');
      //   params = {
      //     orgCode: this.currentData.orgCode || this.defaultTree.defaultId,
      //     id: this.currentData.currentID || this.defaultTree.defaultOrgCode,
      //     pageNo: this.page.pageNo,
      //     pageSize: this.page.pageSize,
      //     areaName: this.page.title
      //   }
      //   console.log(params);
      //   this.postRequest({ url: this.$path.bsp_system_page, params: params}).then(res => {
      //     console.log(res);
      //   })
      // } else if(this.page.bar) {
      //   console.log('elseif');

      //   console.log(params);
      this.postRequest({ url: this.$path.bsp_system_page, params: params }).then(resp => {
        // console.log(res);
        if (resp.success && resp.data.list) {
          this.dataTable = resp.data.list
          this.total = resp.data.list.length
        }
      })
      // } else {
      //   console.log('else');
      //   this.$Modal.error({
      //     title: '温馨提示',
      //     content: '请输入查询条件',
      //     onOk: () => {
      //       console.log('1');
      //       return;
      //     }
      //   })
      // }
    },
    // getqylxName (data) {
    //   console.log(data,'data');
    //   let isAdmin = this.$store.state.common.isAdmin
    //   if (data) {
    //     // this.formData.areaName = data[0].name
    //     data.forEach(item => {
    //       console.log(item, 'item')
    //       if (item.code == '0003') {
    //         this.monitoringRoomForm = true
    //         console.log(this.formData, '0003')
    //       }
    //       if(item.code == '0001' && isAdmin) {
    //         console.log('1');
    //         this.isShow = true
    //         this.isRoomCode = false
    //         this.formData.areaName = this.$store.state.common.orgCode
    //         this.formData.areaCode = item.code
    //       } else if(item.code == '0001' && !isAdmin) {
    //         console.log('2');
    //         this.isShow = false
    //         this.isRoomCode = false
    //         this.formData.areaName = this.$store.state.common.orgName
    //         this.formData.areaCode = item.code
    //       } else {
    //         console.log('3');
    //         this.isRoomCode = true
    //         this.isShow = false
    //       }
    //       console.log(this.formData,'this.formData');
    //     })
    //   }
    // },
    getqylxName(data) {
      console.log(data,'data');
      let isAdmin = this.$store.state.common.isAdmin
      if(data) {
        data.forEach(item => {
          if(item.code == '0003') {
            this.monitoringRoomForm = true
          } else {
            this.monitoringRoomForm = false
          }
          if(item.code == '0001') {
            this.formData.areaCode = this.$store.state.common.orgCode
            this.isRoomCode = false
            if(isAdmin) {
              console.log(1);
              this.isShow = true
              // this.areaNames = this.$store.state.common.orgName
              this.formData.areaName = this.$store.state.common.orgName
              console.log(this.formData.areaName);
            } else {
              console.log(2);
              this.isShow = false
              // this.areaNames = this.$store.state.common.orgName
            }
            console.log(this.formData);
          } else {
            this.isRoomCode = true
          }
        })
      }
    },
    getqyName(data) {
      console.log(data,'data123');
      if(data){
        this.formData.areaCode = data[0].code
        // this.formData.areaName = data[0].name
        this.$set(this.formData,'areaName',data[0].name)
        console.log(this.formData);
        this.$forceUpdate() //强制刷新
      }
    },
    getXbName (data) {
      if (data && data != []) {
        console.log(data, '性别')
        this.formData.roomSex = data[0].code
      }
    },
    getJslxName (data) {
      if (data && data.length > 0) {
        console.log(data, '监室类型')
        this.formData.roomType = data[0].code
      }
    },
    onSelectZb (data) {
      console.log(data, '主办---------')
      if (data) {
        if (!this.formData.areaRelatedWarderReqVOList) {
          this.formData.areaRelatedWarderReqVOList = []
        }
        data.forEach(item => {
          this.formData.areaRelatedWarderReqVOList.push({
            policeId: item.id,
            policeName: item.name,
            userType: 'w'
          })
        })
        console.log(this.formData)
      }
    },
    onSelectXb (data) {
      console.log(data, '协办------------')
      if (data) {
        if (!this.formData.areaRelatedWarderReqVOList) {
          this.formData.areaRelatedWarderReqVOList = []
        }
        data.forEach(item => {
          this.formData.areaRelatedWarderReqVOList.push({
            policeId: item.id,
            policeName: item.name,
            userType: 'a'
          })
        })
        console.log(this.formData)
      }
    },
    exportEvent(){

    },
    temEvent() {
      this.$store.dispatch('authGetRequestBlob',{
        url: '/acp-com/base/area/exportModel',
        //  responseType: 'blob'
      }).then(data => {
        let blobURL =  URL.createObjectURL(data);
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = blobURL;
        // if (name) {
          link.setAttribute("download", '区域模板.xlsx');
        // }
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobURL);
      })
    },
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      const allowedExtensions = ['xls', 'xlsx'];
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        this.$message.error('只能上传 .xls 或 .xlsx 文件');
        return;
      }

      this.uploadFile(file);
    },
  
    async uploadFile(file) {
      console.log(file,'file');
      const formData = new FormData();
      formData.append('file', file); 
      console.log(formData,'formData');
      
      try {
        const res = await axios.post(
          '/acp-com/base/area/importAreaData',
          formData,
          {
            headers: {
              'Authorization': `Bearer ${getToken()}`,
            },
          }
        );
        console.log(res,'res222');
        this.on_refresh_table()
        this.$Message.success(res.data.data)
        
      } catch (error) {
        console.error('上传失败:', error);
        this.$Message.error(error || '文件上传失败');
      } finally {
        this.$refs.fileInput.value = '';
      }
    },
    on_refresh_table () {
      this.$refs.grid.query_grid_data(1)
    }
  },
  mounted () {
    this.getList()
    console.log(this.$store.state.common.orgName,'1111');
    // this.formData.areaName = this.$store.state.common.orgName
    console.log(this.formData.areaName,'this.formData.areaName');
  }
}
</script>

  <style lang="less" scoped>
  .ivu-btn:nth-of-type(n+1){
    margin-left: 10px;
  }
  .dbxx-wrap-search{
    background: rgba(247, 249, 252, .9);
    padding: 10px 15px;
    /* margin-bottom: 10px; */
    overflow: hidden;
    border: 1px solid #cee0f0;
    margin: 16px 10px;
  }
  .pageWrap{
    display: flex;
    justify-content: flex-end;
    padding: 0 16px 6px 0;
    margin-top: 6px;
    // border-top: 1px solid #f1f9fa;
    width: 100%;
  }
  .addBtn{
    margin-bottom: 10px;
  }
  .wrap-cont{
    display: flex;
    // width: 70px;
  }

  // /deep/.ivu-modal-header-inner{
  //   // background: none !important;
  //   // color: #444 !important
  // }
  /deep/.ivu-modal-body{
    min-height: 540px !important;
  }
  .title{
    position: relative;
    font-size: 16px;
    color: #000;
    // font-weight: 600;
    line-height: 40px;
    height: 40px;
    padding-left: 15px;
    margin-left: 10px;
  }
  .title::before {
    position: absolute;
    content: "";
    background-color: #087eff;
    width: 4px;
    height: 20px;
    left: 0;
    top: 50%;
    margin-top: -10px;
    // -webkit-border-radius: 3px;
    // -moz-border-radius: 3px;
    // border-radius: 3px;
  }
  .djxx-title{
    position: relative;
    font-size: 16px;
    color: #000;
    // font-weight: 600;
    line-height: 50px;
    padding-left: 10px;
    // margin-left: 10px;
  }
  .djxx-title::before{
    position: absolute;
    content: "";
    background-color: #087eff;
    width: 4px;
    height: 20px;
    left: 0;
    top: 50%;
    margin-top: -10px;
  }
.djxx-wrap{
  // margin:20px 10%;
  // border:1px solid #eff6ff;
  .ivu-col{
    border-right:1px solid #eff6ff;
    border-bottom:1px solid #eff6ff;
    line-height: 36px;
  }
  .ivu-col:nth-of-type(2n+1){
    font-weight: 700;
    text-align: right;
    padding-right: 16px;
    background: #e4eefc;
    // border-right: 2px solid #fff;
    border: 2px solid #fff;
    border-bottom: none;
  }
  .ivu-col:nth-of-type(2n){
    padding-left: 16px;
    background: #f5f7fa;
    height: 100%;
    border-top: 2px solid #fff;
    // border-bottom: 2px solid #fff;
    // border-left: none;
  }
}
// /deep/.ivu-btn-primary{
//   background: none !important;
// }
.input-container {
  position: relative;
  display: inline-block;
}
.unit {
  position: absolute;
  right: 1px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
  text-align: center;
  width: 25px;
  height: 90%;
  border-radius: 0px 4px 4px 0px;
  background: #e6f0ff;
}
  // .cancle_btn {
  //     min-width: 60px;
  //     height: 30px;
  //     background: #ffffff;
  //     border: 1px solid #2b5fd9;
  //     color: #2b5fd9;
  //     border-radius: 2px;
  // }
  // .sure_btn {
  //     min-width: 60px;
  //     height: 30px;
  //     background: #2b5fd9;
  //     border-radius: 2px;
  // }
  </style>

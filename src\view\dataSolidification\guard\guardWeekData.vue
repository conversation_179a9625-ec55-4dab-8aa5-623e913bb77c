<template>
    <div class="bsp-base-form-guard">
        <div class="top">
            <RadioGroup v-model="type" type="button" button-style="solid" @on-change="changeType">
                <Radio label="0">当前报送</Radio>
                <Radio label="1">历史报送</Radio>
            </RadioGroup>

            <!-- <Alert class="tip" show-icon v-if="type == '0'">统计周期：{{ yesterdayRange }}</Alert> -->
            <div class="tip" v-if="type == '0'">统计周期：{{ yesterdayRange }}</div>
        </div>
        <div style="height: 73vh;overflow: auto;">
            <comWeek v-if="type == '0'" :msgData="msgData" :lawyerData="lawyerData" :superData="superData"></comWeek>
            <div v-else>
                <s-DataGrid ref="grid" funcMark="mzsjbs-kss" :customFunc="true">
                    <template slot="customRowFunc" slot-scope="{ func,row,index }">
                        <Button type="primary" size="small" v-if="func.includes(globalAppCode + ':mzsjbskss:xq')"
                            @click="handleDetails(row)">详情</Button>
                    </template>
                </s-DataGrid>
            </div>
        </div>
        <Modal v-model="modalVisible" width="80%" :title="`详情（${msgData?.solidificationDate }）`" :footer-hide="true">
            <comWeek :msgData="msgData" :lawyerData="lawyerData" :superData="superData"> </comWeek>
        </Modal>


    </div>
</template>



<script>
import comWeek from "./comWeek.vue";
import { mapActions } from "vuex";
export default {
    name: 'guardDayData',
    components: {
        comWeek
    },
    data() {
        return {
            // dataList: []
            type: '0',
            msgData: {
                aa: '11'
            },
            yesterdayRange: this.getYesterdayRange(),
            lawyerData: [],

            superData: [],
            modalVisible: false,

        }
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        // 获取数据
        getData() {
            this.authGetRequest({ url: this.$path.ds_weeklgetByDate, params: {} }).then(res => {
                if (res.success) {
                    this.msgData = res.data[0];
                    this.getlawyerData(this.msgData.id)
                    this.getsuperData(this.msgData.id)
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        getWeekData(id) {
            this.authGetRequest({ url: this.$path.ds_weeklgetByDateId, params: { id } }).then(res => {
                if (res.success) {
                    this.msgData = res.data;
                    this.getlawyerData(id)
                    this.getsuperData(id)
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        changeType(type) {
            console.log(type);
            if (type == '0') {
                this.getData();
            }
        },
        getYesterdayRange() {
            const lastWeekEnd = this.dayjs().subtract(1, "day"); // 昨天（前一周的最后一天）
            const lastWeekStart = lastWeekEnd.subtract(6, "day"); // 前一周的第一天
            return `${lastWeekStart.format("YYYY年M月D日")} 0:00 至 ${lastWeekEnd.format("YYYY年M月D日")} 24:00`;
        },
        getlawyerData(id) {
            this.authGetRequest({ url: this.$path.ds_getweeklyLawyerTop, params: { weeklyDataSubmitId: id } }).then(res => {
                if (res.success) {
                    this.lawyerData = res.data;
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        getsuperData(id) {
            this.authGetRequest({ url: this.$path.ds_getweeklyPrisonerTop, params: { weeklyDataSubmitId: id } }).then(res => {
                if (res.success) {
                    this.superData = res.data;
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        handleDetails(row) {
            console.log(row.id);
            this.modalVisible = true
            this.getWeekData(row.id)
        }
    },
    mounted() {
        this.getData();
    }
}



</script>


<style scoped lang="less">
.bsp-base-form-guard {
    // padding: 15px;

    .top {
        display: flex;
        margin-bottom: 15px;
        align-items: center;

        .tip {
            margin-left: 100px;
             font-size: 16px;
            font-weight: bold;
        }
    }


}

/deep/.ivu-modal-body {
    max-height: 750px;
    overflow: auto;
    padding-bottom: 16px !important;
}
</style>
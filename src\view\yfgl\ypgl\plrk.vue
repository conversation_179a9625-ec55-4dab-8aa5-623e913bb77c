<template>
    <div>
        <div class="bsp-base-form">
            <div class="bsp-base-tit">{{ modalTitle }}</div>
            <div class="bsp-base-content">
                <Table :columns="columns" :data="dataTable" tooltip-theme="light" :tooltip-max-width="300" border>
                    <template slot-scope="{ row,index }" slot="cz">
                        <Button type="primary"  @click="dhdrk">到货单入库</Button>
                    </template>
                </Table>
                <div class="bottom-page">
                    <Page :total="this.total" :page-size="this.pageSize" show-total @on-change="changePage" />
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="on_return_table">返 回</Button>
            </div>
        </div>


        <Modal v-model="showDHD" title="到货单入库" footer-hide>
            <div class="modal-content">
                <Upload :action="importUrl" accept=".xlsx,.xls" :format="['xlsx', 'xls']" :data="importParam"
                    :before-upload="handleUpload" :on-success="handleSuccess" :show-upload-list="false"
                    :loading="importLoading">
                    <Button type="primary" @click="getResetMethod(resetMethod)">导入Excel文件</Button>
                </Upload>
                <div class="modal-bottom" style="margin-bottom: 16px">通过<span class="modal-bottom-dr" >标准模版导入</span></div>
            </div>
        </Modal>

    </div>
</template>

<script>
import { Modal } from "view-design";
import { mapActions } from "vuex";
import { getToken,formatDateparseTime } from "@/libs/util";

export default {
    components: {
    },
    props: {
        modalTitle: String,
    },
    data() {
        return {
            columns: [{
                type: "index",
                width: 80,
                align: "center",
                title: "序号",
            },
            {
                title: "申请单编号",
                key: "applyNum",
                align: "center",
                tooltip: true
            }, {
                title: "申请时间",
                key: "addTime",
                align: "center",
                tooltip: true
            }, {
                title: "提交人",
                key: "addUser",
                align: "center",
                tooltip: true
            }, {
                title: "操作",
                align: "center",
                slot: "cz"
            }],
            dataTable: [],
            pageNo: 1,
            pageSize: 10,
            total: null,
            showDHD: false,
            importUrl: this.$path.get_ypcgsq_importExcel,
            importParam: {},
            importLoading: false,
        };
    },
    mounted() {
        this.getDateTable()
    },
    methods: {
        ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
        on_return_table() {
            this.$emit("on_show_table");
        },
        getDateTable() {
            this.$store
                .dispatch("authGetRequest", {
                    url: this.$path.get_ypcgsq_page,
                    params: {
                        pageNo: this.pageNo,
                        pageSize: this.pageSize
                    }
                }).then(res => {
                    if (res.success) {
                        res.data.list.forEach(i=>{
                            i.addTime = formatDateparseTime(i.addTime)
                        })
                        this.dataTable = res.data.list
                        this.total = res.data.total
                    }
                })

        },
        changePage(e) {
            this.pageNo = e
            this.getDateTable()
        },
        dhdrk() {
            this.showDHD = true
        },
        // 药品更新
        handleUpload() {
            let that = this
            that.importLoading = true
            return new Promise((resolve, reject) => {
                that.$Modal.confirm({
                    title: '温馨提示',
                    content: '是否确认导入数据？',
                    loading: true,
                    onOk: async () => {
                        that.importParam.access_token = getToken()
                        resolve(true)
                    },
                    onCancel: async () => {
                        that.importLoading = false
                        reject(false)
                    }
                })
            })
        },
        handleSuccess(res) {
            this.$Modal.remove()
            if (res.code == 0) {
                this.$Notice.success({
                    title: '成功提示',
                    desc: res.data
                })
            } else {
                this.$Notice.error({
                    title: '错误提示',
                    desc: '数据导入失败'
                })
                window.location.href = res.data.url
            }
            this.importLoading = false
        },
    },
};
</script>

<style lang="less" scoped>
.bottom-page {
    margin: 10px 20px 0 0;
    text-align: right;
}


.modal-content {
    font-size: 16px;
    text-align: center;

    .modal-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px
    }

    .modal-bottom {
        margin-top: 10px;

        .modal-bottom-dr {
            color: rgb(63, 118, 219);
            cursor: pointer;
            margin-left: 10px;
        }
    }
}
</style>

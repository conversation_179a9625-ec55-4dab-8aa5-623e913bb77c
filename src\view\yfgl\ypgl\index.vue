<template>
  <div class="content-defaulet">
    <div class="content-defaulet-main">
      <tabs ref="tabs" v-show="showFormCompnent" mark="ykgl" @changeTabsMark="changeTabsMark" @saveForm="saveForm"
            @cancal="cancal" :params="{}">
        <template slot="customHeadFunc" slot-scope="{ func,hasPermission, resetMethod, funcMark ,appendEqualFuncMark}">
          <Button v-if="
            (appendEqualFuncMark('cgypxxlb') && hasPermission('cgypxxlb:add') ||
              appendEqualFuncMark('gsyxxlb') && hasPermission('gsyxxlb:add') ||
              appendEqualFuncMark('jsypxxlb')&& hasPermission('jsypxxlb:add'))
          " type="primary" @click="showFm(funcMark, resetMethod, 'add', '新增药品')">
            新增药品
          </Button>
          <Button style="margin-left: 5px;" type="primary" @click="showPlrk(resetMethod)">
            批量入库
          </Button>
          <Upload style="display: inline-block;margin-left: 5px;" :action="importUrl" accept=".xlsx,.xls"
                  :format="['xlsx', 'xls']" :data="importParam" :before-upload="handleUpload"
                  :on-success="handleSuccess"
                  :show-upload-list="false" :loading="importLoading">
            <Button type="primary" @click="getResetMethod(resetMethod)">数据导入</Button>
          </Upload>

        </template>

        <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index, resetMethod, funcMark }">
          <Button type="primary" @click="showFm(funcMark, resetMethod, 'detail', '药品详情', row)">
            详情
          </Button>
          <Button type="primary" v-if="hasPermission('cgypxxlb:edit')" style="margin-left: 5px;"
                  @click="showFm(funcMark, resetMethod, 'edit', '编辑', row)">
            编辑
          </Button>
          <Button type="primary" v-if="hasPermission('cgypxxlb:in')" style="margin-left: 5px;"
                  @click="showComponent(funcMark, resetMethod, 'rkdj', '入库登记', row)">
            入库登记
          </Button>
          <Button type="primary" v-if="hasPermission('cgypxxlb:out')" style="margin-left: 5px;"
                  @click="showComponent(funcMark, resetMethod, 'ckdj', '出库登记', row)">
            出库登记
          </Button>
          <Button type="primary" v-if="hasPermission('cgypxxlb:loss') && row.reportable_loss == 1"
                  style="margin-left: 5px;" @click="showComponent(funcMark, resetMethod, 'ypbs', '药品报损', row)">
            药品报损
          </Button>
          <Button type="error" v-if="hasPermission('cgypxxlb:edit')" style="margin-left: 5px;"
                  @click="deleteItem(funcMark, resetMethod, row)">
            删除
          </Button>
        </template>
      </tabs>

      <!-- 新增、编辑、详情 -->
      <fmDeatil v-if="(!showFormCompnent && type == 'add' || type == 'detail' || type == 'edit')" :title="title"
                :parameter="parameter" :showSave="showSave" :showCancel="showCancel" @saveForm="saveForm"
                @cancal="cancal">
      </fmDeatil>

      <!-- 入库登记 -->
      <rkdj v-if="!showFormCompnent && type == 'rkdj'" :modalTitle="title" :detailId="rowData.id"
            @on_show_table="cancal">
      </rkdj>

      <!-- 出库登记 -->
      <ckdj v-if="!showFormCompnent && type == 'ckdj'" :modalTitle="title" :detailId="rowData.id"
            @on_show_table="cancal">
      </ckdj>


      <!-- 药品报损 -->
      <ypbs v-if="!showFormCompnent && type == 'ypbs'" :modalTitle="title" :detailId="rowData.id"
            @on_show_table="cancal">
      </ypbs>


      <Modal v-model="showPldr" title="批量导入" footer-hide>
        <div class="modal-content" v-if="importPLStatus == 1">
          <Upload :action="importPLUrl" accept=".xlsx,.xls" :format="['xlsx', 'xls']" :data="importPLParam"
                  :before-upload="handlePLUpload" :on-success="handlePLSuccess" :show-upload-list="false"
                  :loading="importPLLoading">
            <Button type="primary" @click="getResetMethod(resetMethod)">导入Excel文件</Button>
          </Upload>
          <div class="modal-bottom" style="margin-bottom: 16px">通过<span class="modal-bottom-dr" @click="dowmLoadModal">标准模版导入</span></div>
        </div>

        <div class="modal-contents" v-if="importPLStatus == 2">
          <Spin>
            <Icon type="ios-loading" size=24 style="font-weight: bold;" class="demo-spin-icon-load"></Icon>
            <div class="modal-contents-title">正在解析表格数据</div>
          </Spin>
        </div>
      </Modal>

      <Modal v-model="showYclb" title="异常列表" footer-hide width="1500">
        <yclb :yclbData="yclbData" @on_show_table="cancalYclb"></yclb>
      </Modal>

    </div>
  </div>
</template>

<script>
import tabs from "@/components/tabs/index.vue";
import fmDeatil from "@/components/fm/component/fmDeatil.vue";
import rkdj from "./rkdj.vue"
import plrk from "./plrk.vue"
import yclb from "./yclb.vue"
import ypbs from "./ypbs.vue";
import ckdj from "./ckdj.vue";
import {mapActions} from "vuex";
import {getTabsParams, getToken} from "@/libs/util";


export default {
  components: {
    tabs,
    fmDeatil,
    rkdj,
    plrk,
    yclb,
    ypbs,
    ckdj
  },
  data() {
    return {
      type: null,
      tabsMark: null,
      tabsName: null,
      showSave: true,
      showCancel: false,
      showFormCompnent: true,
      title: "",
      parameter: {
        formId: "",
        operType: "0",
      },
      resetMethod: null,
      importUrl: this.$path.ykgl_importExcel,
      importParam: {},
      importLoading: false,
      rowData: {},
      //批量导入
      showPldr: false,
      importPLUrl: this.$path.get_ypcgsq_importExcel,
      importPLParam: {},
      importPLLoading: false,
      importPLStatus: 1,
      //异常列表
      showYclb: false,
      yclbData: {}
    };
  },
  methods: {
    ...mapActions(["authGetRequest"]),
    changeTabsMark(e, name) {
      this.tabsMark = e;
      this.tabsName = name;
    },
    getDataOper(e) {
      this.operData = e
    },
    showFm(e, resetMethod, type, title, row) {
      // 添加空值检查，防止运行时错误
      if (!this.$refs.tabs || !this.$refs.tabs.$refs || !this.$refs.tabs.$refs[this.tabsMark + '-grid'] || !this.$refs.tabs.$refs[this.tabsMark + '-grid'][0]) {
        console.error('无法获取表格引用，请检查组件是否正确加载');
        return;
      }

      let headButtonList = this.$refs.tabs.$refs[this.tabsMark + '-grid'][0].headOper
      let rowButtonList = this.$refs.tabs.$refs[this.tabsMark + '-grid'][0].rowOper
      console.log(type, 'this.parameter.businessId')
      this.parameter.businessId = ''

      if (type == 'add') {
        if (!headButtonList || !Array.isArray(headButtonList)) {
          console.error('headButtonList 未定义或不是数组');
          return;
        }
        let buttonData = headButtonList.filter(d => d.name == '新增')[0];
        if (!buttonData) {
          console.error('未找到"新增"按钮配置');
          return;
        }
        let data = buttonData.url;
        this.parameter.formId = getTabsParams(data).mark;
        this.parameter.operType = 0;
        this.showSave = true;
        this.showCancel = false;
      } else if (type == "edit") {
        if (!rowButtonList || !Array.isArray(rowButtonList)) {
          console.error('rowButtonList 未定义或不是数组');
          return;
        }
        let buttonData = rowButtonList.filter(d => d.name == '编辑')[0];
        if (!buttonData) {
          console.error('未找到"编辑"按钮配置');
          return;
        }
        let data = buttonData.url;
        this.parameter.formId = getTabsParams(data).mark;
        this.parameter.operType = 1;
        this.parameter.businessId = row.id
        this.showSave = true;
        this.showCancel = false;
      } else {
        if (!rowButtonList || !Array.isArray(rowButtonList)) {
          console.error('rowButtonList 未定义或不是数组');
          return;
        }
        let buttonData = rowButtonList.filter(d => d.name == '详情')[0];
        if (!buttonData) {
          console.error('未找到"详情"按钮配置');
          return;
        }
        let data = buttonData.url;
        this.parameter.formId = getTabsParams(data).mark;
        this.parameter.businessId = row.id
        this.parameter.operType = 2;
        this.showSave = false;
        this.showCancel = true;
      }

      this.showFormCompnent = false
      this.type = type;
      this.title = this.tabsName + "-" + title;
      this.resetMethod = resetMethod;

    },
    showComponent(e, resetMethod, type, title, row) {
      this.showFormCompnent = false
      this.type = type;
      this.title = this.tabsName + "-" + title;
      this.resetMethod = resetMethod;
      this.rowData = row
    },

    // 表单保存的回调
    saveForm(params, data) {
      console.log(111, params, data);
      this.cancal()
    },
    // 关闭的回调   返回接口参数   formData数据
    cancal(params, data) {
      this.type = null;
      this.showFormCompnent = true
      this.resetMethod();
    },
    getResetMethod(resetMethod) {
      this.resetMethod = resetMethod
    },
    // 药品更新
    handleUpload() {
      let that = this
      that.importLoading = true
      return new Promise((resolve, reject) => {
        that.$Modal.confirm({
          title: '温馨提示',
          content: '是否确认导入药品数据？',
          loading: true,
          onOk: async () => {
            that.importParam.access_token = getToken()
            resolve(true)
          },
          onCancel: async () => {
            that.importLoading = false
            reject(false)
          }
        })
      })
    },
    // 回调方法
    handleSuccess(res) {
      this.$Modal.remove()
      if (res.code == 0) {
        this.$Notice.success({
          title: '成功提示',
          desc: res.data
        })
      } else {
        this.$Notice.error({
          title: '错误提示',
          desc: '数据导入失败'
        })
        window.location.href = res.data.url
      }
      this.importLoading = false
      this.resetMethod()
    },

    // 标准模版下载
    dowmLoadModal() {
      window.location.href = "/word/药库入库模版.xlsx"
    },

    showPlrk(resetMethod) {
      this.importPLStatus = 1
      this.showPldr = true
      this.getResetMethod(resetMethod)
    },

    // 批量导入上传
    handlePLUpload() {
      let that = this
      that.importPLLoading = true
      return new Promise((resolve, reject) => {
        that.$Modal.confirm({
          title: '温馨提示',
          content: '是否确认导入数据？',
          onOk: async () => {
            that.importPLStatus = 2
            that.importPLParam.access_token = getToken()
            resolve(true)
          },
          onCancel: async () => {
            that.importPLLoading = false
            reject(false)
          }
        })
      })
    },
    // 批量导入上传回调
    handlePLSuccess(res) {
      this.showPldr = false
      if (res.code == 0) {
        this.$Notice.success({
          title: '成功提示',
          desc: "导入成功"
        })
      } else if (res.code == 501) {
        this.yclbData = res.data
        this.showYclb = true
      } else {
        this.$Notice.error({
          title: '错误提示',
          desc: '数据导入失败'
        })
      }
      this.importPLLoading = false
    },

    cancalYclb() {
      this.showYclb = false
      this.resetMethod()
    },

    // 删除
    deleteItem(funcMark, resetMethod, row) {
      console.log(row)
      // 删除
      let _this = this
      this.$Modal.confirm({
        title: '提示',
        render: (h, params) => {
          return h("div", [
            h("p", {style: {marginLeft: "10px"},}, '是否确认删除【' + row.medicine_name + '】?')
          ]);
        },
        loading: true,
        onOk: async () => {
          this.submitDel(row.id, resetMethod)
        }
      })
    },
    submitDel(id, resetMethod) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.delete_medicine,
        params: {
          ids: id
        }
      }).then(data => {
        if (data.data) {
          this.$Notice.success({
            title: '成功提示',
            desc: "删除成功"
          })
          this.$Modal.remove()
          resetMethod()
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: "删除失败"
          })
        }
      })
    },
  },
};
</script>


<style lang="less" scoped>
.modal-content {
  font-size: 16px;
  text-align: center;

  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px
  }

  .modal-bottom {
    margin-top: 10px;

    .modal-bottom-dr {
      color: rgb(63, 118, 219);
      cursor: pointer;
      margin-left: 10px;
    }
    margin-bottom: 16px;
    line-height: 50px;
  }
}

.modal-contents {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-contents-title {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>

<template>
    <div>
        <s-DataGrid ref="grid" funcMark="jcxxgl-jqgl" :customFunc="true" v-if="!showEdit">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':jqgl:add')"
                    @click.native="handleEdit">编辑</Button>
            </template>
            <template slot="slot_toilet_renovation" slot-scope="{ row }">
                <Tag :color="row.toilet_renovation == '01' ? 'green' : 'volcano'" v-if="row.toilet_renovation">{{
                    row.toilet_renovationName }}</Tag>
            </template>
            <template slot="slot_zysgz" slot-scope="{ row }">
                <Tag :color="row.zysgz == '01' ? 'green' : 'volcano'" v-if="row.zysgz">{{ row.zysgzName }}</Tag>
            </template>
            <template slot="slot_cwz" slot-scope="{ row }">
                <Tag :color="row.cwz == '01' ? 'green' : 'volcano'" v-if="row.cwz">{{ row.cwzName }}</Tag>
            </template>
            <template slot="slot_heating_methods" slot-scope="{ row }">
                {{ row.heating_methodsName }}
            </template>
            <template slot="slot_fsjw" slot-scope="{ row }">
                {{ row.fsjwName }}
            </template>
        </s-DataGrid>

        <s-DataGrid ref="gridShowEdit" funcMark="jcxxgl-jqgl" :customFunc="true" v-if="showEdit">
            <template slot="slot_toilet_renovation" slot-scope="{ row }">
                <Select v-model="row.toilet_renovation" @on-open-change="changeWsjgz($event, row)">
                    <Option v-for="item in wsjgzList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                </Select>
            </template>
            <template slot="slot_zysgz" slot-scope="{ row }">
                <Select v-model="row.zysgz" @on-open-change="changeZysgz($event, row)">
                    <Option v-for="item in zysgzList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                </Select>
            </template>
            <template slot="slot_cwz" slot-scope="{ row }">
                <Select v-model="row.cwz" @on-open-change="changeCwz($event, row)">
                    <Option v-for="item in cwzList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                </Select>
            </template>

            <template slot="slot_heating_methods" slot-scope="{ row }">
                <!-- <s-dicgrid v-model="row.heating_methods" dicName="ZD_QNFS" @values="changeHeating($event, row)" /> -->
                <Select v-model="row.heating_methods" @on-open-change="changeHeating($event, row)">
                    <Option v-for="item in heatingList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                </Select>
            </template>
            <template slot="slot_fsjw" slot-scope="{ row }">
                <!-- <s-dicgrid v-model="row.fsjw" dicName="ZD_JSJW" @values="changeFsjw($event, row)" /> -->
                <Select v-model="row.fsjw" @on-open-change="changeFsjw($event, row)">
                    <Option v-for="item in fsjwList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                </Select>
            </template>
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':jqgl:plywc')"
                    @click.native="handleAll(true)" :disabled="$refs.gridShowEdit.batch_select.length < 1"
                    :loading="btnLoindingTrue">批量已完成</Button>
                <Button type="primary" v-if="func.includes(globalAppCode + ':jqgl:plwwc')"
                    @click.native="handleAll(false)" style="margin-left: 15px;"
                    :disabled="$refs.gridShowEdit.batch_select.length < 1" :loading="btnLoindingFalse">批量未完成</Button>

                <Button @click.native="back" style="margin-left: 15px;">返回</Button>
            </template>

        </s-DataGrid>
    </div>
</template>
<script>
import { init } from 'echarts'
import { Loading } from 'element-ui'

export default {
    name: 'resideinfor',
    data() {
        return {
            showEdit: false,
            wsjgzList: [],
            zysgzList: [],
            cwzList: [],
            heatingList:[],
            fsjwList:[],
            btnLoindingTrue: false,
            btnLoindingFalse: false,
            oldtoiletRenovation: '',
            oldzysgz: '',
            oldwcws: '',
            oldheating: '',
            oldwfsjw: '',

        }
    },
    mounted() {
        this.init()
    },

    methods: {
        handleEdit() {
            this.showEdit = true
            this.$nextTick(() => {
                this.$refs.gridShowEdit.query_grid_data(1)
            })
            // this.$refs.gridShowEdit.query_grid_data(1)
        },
        handleAll(tag) {
            if (tag) {
                let selectList = this.$refs.gridShowEdit.batch_select.map(item => {
                    return {
                        id: item.id,
                        toiletRenovation: '01',
                        zysgz: '01',
                        cwz: '01'
                    }
                })
                this.btnLoindingTrue = true
                this.batchSave(selectList)
            } else {
                this.btnLoindingFalse = true
                let selectList = this.$refs.gridShowEdit.batch_select.map(item => {
                    return {
                        id: item.id,
                        toiletRenovation: '02',
                        zysgz: '02',
                        cwz: '02'
                    }
                })
                this.batchSave(selectList)
            }
        },
        back() {
            this.showEdit = false
            this.$nextTick(() => {
                this.$refs.grid.query_grid_data(1)
            })
        },
        changeWsjgz(e, row) {
            if (e) {
                this.oldtoiletRenovation = row.toilet_renovation
            } else {
                if (this.oldtoiletRenovation == row.toilet_renovation) {
                    return
                }
                let params = {
                    id: row.id,
                    toiletRenovation: row.toilet_renovation
                }
                this.save(params)
            }
        },
        changeZysgz(e, row) {
            if (e) {
                this.oldzysgz = row.zysgz
            } else {
                if (this.oldzysgz == row.zysgz) {
                    return
                }
                let params = {
                    id: row.id,
                    zysgz: row.zysgz
                }
                this.save(params)
            }
        },
        changeCwz(e, row) {
            if (e) {
                this.oldcwz = row.cwz
            } else {
                if (this.oldcwz == row.cwz) {
                    return
                }
                let params = {
                    id: row.id,
                    cwz: row.cwz
                }
                this.save(params)
            }
        },

        changeHeating(e, row) {
             if (e) {
                this.oldheating = row.heating_methods
            } else {
                if (this.oldheating == row.heating_methods) {
                    return
                }
                let params = {
                    id: row.id,
                    heatingMethods: row.heating_methods
                }
                this.save(params)
            }


        },
        changeFsjw(e, row) {
            if (e) {
                this.oldwfsjw = row.fsjw
            } else {
                if (this.oldwfsjw == row.fsjw) {
                    return
                }
                let params = {
                    id: row.id,
                    fsjw: row.fsjw
                }
                this.save(params)
            }


        },
        save(params) {
            this.$store.dispatch('authPostRequest', { url: this.$path.api_qjtjSave, params: params }).then(res => {
                if (res.success) {
                    this.$Message.success('修改成功')
                    this.$refs.gridShowEdit.query_grid_data()
                } else {
                    this.$Message.error(res.message)
                }
            })
        },
        batchSave(params) {
            this.$store.dispatch('authPostRequest', { url: this.$path.api_qjtjBatchSave, params: params }).then(res => {
                this.btnLoindingTrue = false
                this.btnLoindingFalse = false
                if (res.success) {
                    this.$Message.success('批量修改成功')
                    this.$refs.gridShowEdit.query_grid_data()
                } else {
                    this.$Message.error(res.message)
                }
            })
        },
        handleGetZD_WSJGZ() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_WSJGZ.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.wsjgzList = scales();
            });
        },
        handleGetZD_ZYSGZ() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_ZYSGZ.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.zysgzList = scales();
            });
        },
        handleGetZD_CWZ() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_CWZ.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.cwzList = scales();
            });
        },
        handleGetZD_QNFS() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_QNFS.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.heatingList = scales();
            });
        },
        handleGetZD_JSJW() {
            this.$store.dispatch("authGetRequest", { url: "/bsp-com/static/dic/acp/ZD_JSJW.js", }).then((res) => {
                let scales = eval("(" + res + ")");
                this.fsjwList = scales();
            });
        },
        init() {
            this.handleGetZD_WSJGZ();
            this.handleGetZD_CWZ();
            this.handleGetZD_ZYSGZ();
            this.handleGetZD_QNFS();
            this.handleGetZD_JSJW();
        }
    }
}


</script>
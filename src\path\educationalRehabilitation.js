import { acpCom } from './base'
// edas
export default {
  // ----------------------教育康复 start -------------------------
  edurehabCourses_create: acpCom + '/acp/gj/edurehabCourses/create',
  edurehabCourses_update: acpCom + '/acp/gj/edurehabCourses/update',
  edurehabCourses_delete: acpCom + '/acp/gj/edurehabCourses/delete',
  edurehabCourses_get: acpCom + '/acp/gj/edurehabCourses/get',
  edurehabCourses_list: acpCom + '/acp/gj/edurehabCourses/list', 
  edurehabTimeSlot_batchCreate: acpCom + '/acp/gj/edurehabTimeSlot/batchCreate',
  edurehabCoursesPlan_getPlanTime: acpCom + '/acp/gj/edurehabCoursesPlan/getPlanTime',
  edurehabTimeSlot_list: acpCom + '/acp/gj/edurehabTimeSlot/list',
  edurehabCoursesPlan_getPlanArea: acpCom + '/acp/gj/edurehabCoursesPlan/getPlanArea',
  edurehabCoursesPlan_get: acpCom + '/acp/gj/edurehabCoursesPlan/get',
  // ----------------------教育康复 end ------------------------- 

  // ---------------------- 教育康复活动 start -------------------------
  edurehabActivity_createa: acpCom + '/acp/gj/edurehabActivity/create',
  edurehabActivity_get: acpCom + '/acp/gj/edurehabActivity/get',
  edurehabActivity_update: acpCom + '/acp/gj/edurehabActivity/update',
    // ----------------------教育康复活动 end -------------------------
}

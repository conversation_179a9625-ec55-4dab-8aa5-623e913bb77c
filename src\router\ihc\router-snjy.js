let menuMode = localStorage.getItem('menuMode')
// import main from '@/components/app-main/index.vue'
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'

export default [
  {
    path: '/snjy',
    name: 'snjy',
    meta: {
      title: '所内就医'
    },
    redirect: '/snjy/snmz',
    component: menuMode == 'side' ? mainNew : main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'snmz',
        name: 'snmz',
        meta: {
          title: '所内门诊',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/snmz/snmz/index.vue')
      },
      {
        path: 'cfjg',
        name: 'cfjg',
        meta: {
          title: '所内门诊处方结果',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/snmz/cfjg/index.vue')
      },
      {
        path: 'jcdjg',
        name: 'jcdjg',
        meta: {
          title: '所内门诊检查单结果',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/snmz/jcdjg/index.vue')
      },
      {
        path: 'mbgl',
        name: 'mbgl',
        meta: {
          title: '模板管理',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/mbgl/index.vue')
      },
      {
        path: 'bbzd',
        name: 'bbzd',
        meta: {
          title: '报病字典管理',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/bbzd/index.vue')
      },
      {
        path: 'onSite',
        name: 'onSite',
        meta: {
          title: '现场巡诊',
          menu: true,
          bread: true
        },
        component: () => import('@/view/onSite/inspection/index.vue')
      },
      {
        path: 'result',
        name: 'result',
        meta: {
          title: '现场巡诊结果',
          menu: true,
          bread: true
        },
        component: () => import('@/view/onSite/result/index.vue')
      },
      {
        path: 'plan',
        name: 'plan',
        meta: {
          title: '现场巡诊计划',
          menu: true,
          bread: true
        },
        component: () => import('@/view/onSite/plan/index.vue')
      },
      {
        path: 'remoteConsultation',
        name: 'remoteConsultation',
        meta: {
          title: '远程问诊',
          menu: true,
          bread: true
        },
        component: () => import('@/view/remoteConsultation/inspection/index.vue')
      },
      {
        path: 'remoteConsultationRecord',
        name: 'remoteConsultationRecord',
        meta: {
          title: '远程问诊结果',
          menu: true,
          bread: true
        },
        component: () => import('@/view/remoteConsultation/result/index.vue')
      },
    ]
  }
]

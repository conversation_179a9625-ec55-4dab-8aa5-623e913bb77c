@import url('~@/assets/style/var.less');

/* CSS Document */
*{box-sizing:border-box; margin:0;padding:0;}
html,body { color:#2B3646; width:100%; height:100%};
h1,h2,h3,h4,h5,h6,button,input,textarea,select{font-size:100%;}
fieldset,img{border:0;}
table{border-collapse:collapse;border-spacing:0;width:100%;}
caption,th{text-align:left;}
li{list-style:none;}
address,cite,code,dfn,var,em,th,i{font-style:normal;font-weight:normal;}
button,input,textarea{outline:none;font-family:inherit;}
h1,h2,h3,h4,h5,h6 {color:#2B3646;}
a {text-decoration:none;color:#2B3646;}
a:hover {text-decoration:none;color:#087EFF;}
select, dl {word-wrap:normal;}
input,button{border:0px;}按钮
.clearfix:after {content:"";display:block;height:0;clear:both;}
.clearfix {zoom:1;}
.fl {float:left;}
.fr {float:right;}
.my-box{height: 100vh;font-family: "微软雅黑";font-size:14px;color: #2B3646;
	/* background-color: #1a44ba;background: url("@{assets}/images/dzjz/bg-big2.png")no-repeat; */
	background-size: 100% 100%;overflow: hidden;}
body{font-family: "微软雅黑";overflow: hidden;font-size: 0.16rem;}
input { color: #3E4E66; }
input::placeholder { color: #7A8699 };

/* 公共的form/table等样式 */
@font-face{
    font-family: "Source Han Sans CN-Regular";
    // src: url('../font/SourceHanSansCNRegular.ttf');
}
@font-face{
    font-family: "Source Han Sans CN-Medium";
    // src: url('../font/SourceHanSansCN-Medium.ttf');
}

/* 滚动条样式 */
*::-webkit-scrollbar {/*滚动条整体样式*/
    width: 10px !important; /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px !important;
}
*::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
    border-radius: 0.1rem;
    background: #BCD1EB;
}
*::-webkit-scrollbar-thumb:hover {
    background-color: #99C5E7;
}
*::-webkit-scrollbar-track {/*滚动条里面轨道*/
    border-radius: 0.1rem;
    background: #E1EAF5;
}


/* 小标题start */
.sys-sub-title {
    display: flex;
    align-items: center;
    color: #2B3646;
    font-weight: 600;
    font-size: 0.16rem;
    margin: 0.2rem 0;
    &:before {
        display: block;
        content: "";
        width: 0.06rem;
        height: 0.22rem;
        margin-right: 0.12rem;
        background: #087eff;
    }
}
/* 小标题end */

/* 底部悬浮按钮栏start */
.bottom_btn_box{
    // position: fixed;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    // height: 52px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border-top: solid 1px #CEE0F0;
    // background-color: #F7FAFF;
	// z-index: 8;
    .my-btn-container:not(:last-child) {
        margin-right: 0.13rem;
    }
}
/* 底部悬浮按钮栏end */

/* 表格上方操作按钮栏 */
.table-btn-bar-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .ivu-radio-wrapper {
    //     margin-left: 0.2rem;
    // }
}
.table-btn-bar {
    display: flex;
    margin: 0.16rem 0;
    align-items: center;
    .my-btn-container:not(:last-child) {
        margin-right: 0.2rem;
    }
}

/* 空数据start */
.empty-data-box {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	.empty-tips {
		.empty-icon {
			display: block;
			width: 1.58rem;
			height: 0.58rem;
			background: url('~@/assets/images/default_bg/no_data.png') no-repeat;
		}
		.txt {
			text-align: center;
			margin-top: 0.2rem;
		}
	}


}
/* 空数据end */

.filterBtn {
    font-size: 0.2rem;
    cursor: pointer;
}



.ztree li a {
    display: inline-block;
    width: 90%;
    height: 0.34rem !important;
    line-height: 0.2rem !important;
    padding-top:-6px;
}

.ztree a .node_name {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ztree li a .button {
    /* vertical-align: super; */
    float: left;
}

.ztree .button.noline_close:before {
    display: none;
}

.ztree .button.noline_open:before {
    display: none;
}

.ztree li span.button.noline_open {
    background: url('../images/ztree/img/ztree-sq.png') no-repeat center;
    background-size: 0.1rem;
    margin-top: 5px;
    background-position: center 0.05rem !important;
    /* background-position: unset !important; */
}

.ztree li span.button.noline_close {
    background: url('../images/ztree/img/ztree-zk.png') no-repeat center;
    background-size: 0.07rem;
    margin-top: 5px;
    background-position: center 0.03rem !important;
    /* background-position: unset !important; */
}

.ztree li span.button.ico_close {
    background: url('../images/ztree/img/ztree-clous-wjj.png') no-repeat center;
    background-size: 0.2rem;
    width: 0.25rem;
    margin-top: 5px;
    background-position: unset !important;
}

.ztree li span.button.isDoc_ico_close {
    background: url('../images/ztree/img/ztree-clous-wjj-isDoc.png') no-repeat center;
    background-size: 0.2rem;
    width: 0.25rem;
    margin-top: 5px;
    background-position: unset !important;
}

.ztree li span.button.ico_open {
    background: url('../images/ztree/img/ztree-open-wjj.png') no-repeat center;
    background-size: 0.2rem;
    width: 0.25rem !important;
    margin-top: 5px;
    background-position: unset !important;
}

.ztree li span.button.isDoc_ico_open {
    background: url('../images/ztree/img/ztree-open-wjj-isDoc.png') no-repeat center;
    background-size: 0.2rem;
    width: 0.25rem;
    margin-top: 5px;
    background-position: unset !important;
}

.ztree li span.button.ico_docu {
    background: url('../images/ztree/img/ztree-wdh.png') no-repeat center;
    background-size: 0.15rem;
    width: 0.2rem;
    background-position: unset !important;
    margin-top: 0.05rem;
}

.ztree li span.button.isDoc_ico_docu {
    background: url('../images/ztree/img/ztree-wdh-isDoc.png') no-repeat center;
    background-size: 0.15rem;
    width: 0.2rem;
    /* margin-left: 0.025rem; */
    /* margin-right: 0.05rem; */
    background-position: unset !important;
    margin-top: 0.05rem;
}



#treeDemo li {
    position: relative;
}

.fileServePages {
    position: absolute;
    top: 0;
    right: 0;
}

.allJzcl_tree_box {
    width: 10rem;
}

.checkedJzcl_tree_box {
    width: calc(100% - 9rem);
}

#qrcode img {
    width: 100%;
}

.flex-container {
    display: flex;
    align-items: center;
}

.yqsqBtn {
    width: 1rem;
}

.btnBox .yqsqBtn {
    margin-left: 0.2rem;
}

// .btnBox {
//     display: flex;
//     justify-content: flex-end;
//     align-items: center;
//     margin-top: 0.2rem;
// }

.sp-iconfont {
    float: left;
}

.el-input--prefix .el-input__inner {
    font-size: 0.12rem;
    padding-left: 0.2rem !important;
    padding-right: 0 !important;
}

.el-input--small .el-input__inner {
    height: 0.35rem !important;
    line-height: 0.35rem !important;
}

.el-input--small .el-input__icon {
    line-height: 0.35rem !important;
}

.el-input__prefix {
    left: unset !important;
    right: 0;
}

.bsp-user-center-in tr td ul {
    height: 4.5rem !important;
}

.bsp_org_sel_box .left-org-tree,
.bsp_org_sel_box .content-ht {
    height: 5rem !important;
    overflow: auto !important;
}

.bsp-org-input .ivu-input {
    font-size: 16px !important;
}


.manageBox .ivu-icon {
    font-size: 0.2rem;
    /* line-height: 0.2rem; */
    /* vertical-align: -webkit-baseline-middle; */
}

.yjxx_icon {
    display: inline-block;
    width: 0.22rem;
    height: 0.22rem;
    margin-right: 0.1rem;
}

.yjxx_icon.bg01 {
    background-image: url('../images/dzjz/ico_update.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg02 {
    background-image: url('../images/dzjz/ico_archive.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg03 {
    background-image: url('../images/dzjz/ico_overdue.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg04 {
    background-image: url('../images/dzjz/ico_violation.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg05 {
    background-image: url('../images/dzjz/ico_notstorage.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg06 {
    background-image: url('../images/dzjz/ico_ghcq.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg07 {
    background-image: url('../images/dzjz/ico_abnormal.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg08 {
    background-image: url('../images/dzjz/ico_jjcq.png');
    background-size: 100% 100%;
}

.yjxx_icon.bg13 {
    background-image: url('../images/dzjz/ico_ghcq.png');
    background-size: 100% 100%;
}

.uploader-btn {
    display: block;
    width: 100%;
    height: 100%;
}

.progressModalWrap .ivu-progress-success .ivu-progress-bg {
    height: 0.32rem !important;
    border-radius: 0.04rem !important;
}

.progressModalWrap .ivu-progress-success-bg,
.progressModalWrap .ivu-progress-bg {
    border-radius: 0.04rem !important;
}

.progressModalWrap .ivu-progress-inner {
    border-radius: 0.04rem !important;
}

.progressModalWrap .ivu-progress-inner-text {
    height: 0.32rem;
    line-height: 0.32rem;
    text-align: right;
    color: #fff;
    font-size: 0.16rem;
    padding-right: 0.1rem;
}

.progressModal {
    padding: 0 0.2rem;
    min-height: 1.5rem;
}

.progressModal span,
.progressModal p {
    font-size: 0.16rem;
}

.progressModal p {
    margin: 0.15rem 0;
}

.progressModal .title {
    color: #2CCD7A;
}

.progressModal .count {
    color: #2CCD7A;
    font-size: 20px;
}

.progressModal .inTime,
.progressModal .inTime span {
    font-weight: bold;
}

.upload_Btn {
    display: inline-block;
    min-width: 0.8rem;
    height: 0.35rem;
    line-height: 0.35rem;
    text-align: center;
    color: #fff;
    text-align: center;
    background: #2d8cf0;
    border-radius: 3px;
    font-size: 0.14rem;
}

.uploadErrorBox h3 {
    font-weight: bold;
}

.uploadErrorBox h3 i {
    font-weight: bold;
    color: #ff0000;
}

.uploadErrorName {
    width: 100%;
}

.uploadErrorName li {
    width: 100%;
    line-height: 0.3rem;
    font-size: 0.16rem;
}

.sortBtnImg {
    display: inline-block;
    width: 0.3rem;
    height: 0.3rem;
    background: url('../images/dzjz/dzjz_jzgl.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0.8rem;
    top: 0.05rem;
    cursor: pointer;
}

.showMoreBtn {
    height: 0.3rem;
    position: absolute;
    right: 0.1rem;
    top: 0;
    cursor: pointer;
}

.itemSortUl {
    position: absolute;
    right: 1rem;
    top: 0.4rem;
    background: #fff;
    border-radius: 0.04rem;
    z-index: 2;
    box-shadow: 0 1px 7px #999;
}

.itemSortUl li {
    color: #333;
}

.chooseImgSortType {
    padding: 0.1rem 0;
}

.chooseImgSortType li {
    line-height: 0.35rem;
    padding: 0 0.1rem;
    cursor: pointer;
}

.chooseImgSortType li:hover {
    background: #ebf7ff
}

.big_toast_dyts_countdowm {
    color: #fff;
    font-size: 0.2rem;
    position: absolute;
    left: 0.2rem;
    top: 0.2rem;
}

.qcyyDICBox {
    z-index: 99999 !important;
}



.cancelBtn {
    height: 0.32rem;
    line-height: 0.32rem;
}

.view-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #2e6d36;
    opacity: 0.2;
    pointer-events: none;
    z-index: 99999;
}

.clearfix::after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.clearfix {
    *zoom: 1;
}


/* .widthMatch{
    width: 100% !important;
}
.widthMatch .showView{
    width: 100% !important;
    height: auto !important;
} */


/* .heightMatch{
    height: 100% !important;
} */

.progressBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uploadFileList {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uploadFileList p {
    width: 56%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.allUploadMsgBox {
    width: 100%;
    height: 6rem;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    position: relative;
}

.allUploadMsgBox>div:first-child {
    width: 56%;
}

.allUploadMsgBox>div:last-child {
    width: 40%;
}

.uploadFileListBox {
    height: 100%;
}

.uploadFileListsmallBox {
    height: 90%;
    overflow: auto;
    padding-right: 0.2rem;
    box-sizing: border-box;
}

.uploadFileListBox::after {
    content: '';
    width: 2px;
    height: 90%;
    position: absolute;
    left: 58%;
    top: 0.5rem;
    background: #ddd;
}

.statusName {
    text-align: left;
    display: inline-block;
    width: 1rem;
}

.yscwcl {
    color: #f76f14;
}

.yxzclz {
    color: #1E9FFF;
}

.ycfwc {
    color: #2CCD7A;
}

.xzsb {
    color: #f32906;
}

.deleteCurrentDom {
    display: inline-block;
    width: 0.3rem;
    height: 0.3rem;
    background: url('../images/znaj/delete-red2.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 1.3rem;
    top: 0.05rem;
    cursor: pointer;
}

.OperatingProcedures {
    display: inline-block;
    width: 0.3rem;
    height: 0.3rem;
    background: url('../images/znaj/OperatingProcedures.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: sub;
    cursor: pointer;
    margin-left: 0.2rem;
}

.OperatingProceduresBox {
    width: 6.6rem;
    height: 3.8rem;
    display: flex;
    justify-content: center;
    background: #F0F4FA;
    flex-direction: column;
    padding: 0.4rem 0.8rem;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}

.OperatingProceduresBox2 {
    width: 100%;
    position: unset;
}

.OperatingProceduresBox h3 {
    font-size: 0.24rem;
    color: #087eff;
}

.OperatingProceduresBox p {
    font-size: 0.18rem;
    color: #3D5366;
    line-height: 0.4rem;
}

.tips {
    color: #ff0000;
    margin-bottom: 0.3rem;
}

.openToClose {
    height: 0.35rem;
    padding: 0.05rem;
    background: #33a7ff;
    border-radius: 0.04rem;
    color: #fff;
    font-size: 0.14rem;
    position: fixed;
    right: 0.1rem;
    bottom: 1rem;
    cursor: pointer;
}

.isOpenCheckModel_false {
    background: #c7cacd;
    padding: 0 0.2rem;
    margin: 0 0.08rem;
    color: #fff;
}

.isOpenCheckModel_true {
    background: #2CCD7A;
    padding: 0 0.2rem;
    margin: 0 0.08rem;
    color: #fff;
}

.BatchOperation {
    display: inline-block;
    width: 0.3rem;
    height: 0.3rem;
    // background: url('../images/dzjz/bgcqIcon.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 1.8rem;
    top: 0.05rem;
    cursor: pointer;
}

.BatchOperation2 {
    display: inline-block;
    width: 0.3rem;
    height: 0.3rem;
    // background: url('../images/dzjz/btn_hover_delete.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 2.3rem;
    top: 0.05rem;
    cursor: pointer;
}

.ydfwBox {
    padding: 0.2rem 0.4rem;
    box-sizing: border-box;
}

.ydfwItem {
    display: flex;
    align-items: center;
    margin-bottom: 0.1rem;
}

.ydfwItem .ivu-input-wrapper {
    width: 70%;
    margin-left: 0.1rem;
}

.ydfwBox h3 {
    font-weight: bold;
    margin-top: 0.2rem;
    margin-bottom: 0.1rem;
}

.main-content-box {
    flex: 1;
    height: 100%;
    background-color: #fff;
  }

.mbxdh {
    padding: 0.15rem 0;
}

.head_ICP {
    width: 100%;
    height: 0.64rem;
    background: linear-gradient( to right, rgba(13, 106, 223, 0.8), rgba(8, 168, 237, 0.8));
    display: flex;
    align-items: center;
}

.logo_ICP {
    display: inline-block;
    width: 0.4rem;
    height: 0.4rem;
    // background: url('../images/jing_Hui.png') no-repeat;
    background-size: 100% 100%;
    margin: 0 0.1rem 0 0.4rem;
}

.title_ICP {
    font-size: 0.3rem;
    color: #fff;
    font-weight: bold;
    letter-spacing: 0.05rem;
}

div.v-dropdown-container {
    z-index: 9000 !important;
}

.panel-title {
    display: flex;
    align-items: center;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
    display: flex;
    align-items: center;
    height: 70px !important;
    line-height: 70px !important;
}

.vmr-ai-panel {
    height: 100% !important;
    position: absolute;
    top: 0;
    left: 0;
}

.vmr-ai-panel,
.vmr-g-image,
.vmr-ai-raw-image,
.picDiv,
.vmr-ai-raw-image-mask {
    width: 100%;
    height: 100% !important;
}

.annotation {
    z-index: 3;
    /* display: none; */
    border: 2px dashed #fff !important;
}

.progress .ivu-tooltip-rel {
    display: inherit;
}

.bookMark {
    display: inline-block;
    width: 0.2rem;
    height: 0.21rem !important;
    // background-image: url('../images/Bookmark.png') !important;
    background-size: 100% 100%;
    position: absolute;
    right: 0.45rem;
    top: 0.05rem;
}

/* .ztree li a {
    display: inline-block;
    width: 82%;
    position: relative;
} */

.bookmark-red {
    background-color: rgba(255, 11, 11, 0.3) !important;
}

.bookmark-yellow {
    background-color: rgba(255, 206, 11, 0.3) !important;
}

.bookmark-blue {
    background-color: rgba(11, 212, 255, 0.3) !important;
}

.g-image-op-content {
    color: #fff;
}

.g-image-op {
    right: 0;
}
.g-image-op-del{
  display: none !important;
}

.icon-mark {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-mark-checked {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark-checked.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-mark-kx {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark-kx.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-mark-kx2 {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark-kx2.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-mark-dj {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark-dj.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-mark-dj2 {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-mark-dj2.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-jcjl {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-jcjl.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-yjjc {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-yjjc.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-qz {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-qz.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-qm {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-qm.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.icon-ny {
    display: inline-block;
    width: 0.16rem;
    height: 0.18rem;
    // background: url('../images/icon-ny.png') no-repeat;
    background-size: 100% 100%;
    vertical-align: text-bottom;
    margin-right: 0.05rem;
}

.nobutton input::-webkit-outer-spin-button,
.nobutton input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.magnify-modal {
    z-index: 99999999;
}
.textareaBox {
    position: relative;
}
.textareaBox  .cost_tpl_title_length {
    position: absolute;
    bottom: 0.07rem;
    right: 0.07rem;
    color: #999;
}


// tab切换公共样式--------------------
.top_tab_box {
    width: 100%;
    height: 0.48rem;
    padding-left: 0.2rem;
    background-color: #fff;
    border-bottom: 1px solid #CEE0F0;

    &.filter-tab {
        width: auto;
        border: none;
    }

    .tab_item {
        position: relative;
        display: inline-block;
        min-width: 1rem;
        height: 0.48rem;
        line-height: 0.48rem;
        font-size: 0.16rem;
        text-align: center;
        color: #3D4E66;
        cursor: pointer;

    }
    .tab_item_active {
        color: #087EFF;
        font-weight: bold;
        border-bottom: 3px solid #087eff;
    }
}
//------------------------------------

//椭圆tab样式-------------------------
.oval_tab {
    display: flex;
    align-items: center;
    height: 0.48rem;

    .oval_btn_item {
        cursor: pointer;
        min-width: 0.54rem;
        height: 0.28rem;
        line-height: 0.28rem;
        padding: 0 0.13rem;
        margin-right: 0.1rem;
        text-align: center;
        background-color: #E5EDF6;
        color: #3E4E66;
        font-size: 0.14rem;
        border-radius: 14px;
    }

    .oval_btn_item_active {
        background-color: #087EFF;
        color: #fff;
        border-radius: 14px;
    }
}

//------------------------------------

/* 徽标数start */
.badge-box {
    position: absolute;
    top: -10px;
    right: -18px;
    min-width: 25px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    border-radius: 8px 8px 8px 1px;
    background-color: #FF4156;
}
/* 徽标数end */


.login-con /deep/ .ivu-btn-primary{
    margin-top:0 !important;
}

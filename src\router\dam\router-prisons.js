let menuMode = serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [{
    path: '/prisons',
    name: 'prisons',
    meta: {
      title: '一所一档'
    },
    redirect: '/prisons/files',
    component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
    children: [{
        path: 'files',
        name: 'prisonsFiles',
        meta: {
          title: '一所一档',
          menu: true,
          bread: true
        },
        component: () => import('@/view/prisons/index.vue')
      },
      {
        path: '/report',
        name: 'report',
        meta: {
          title: '宣传报道',
          menu: true,
          bread: true
        },
        component: () => import('@/view/prisons/report/index.vue')
      },
      {
        path: '/highlights',
        name: 'highlights',
        meta: {
          title: '特色亮点',
          menu: true,
          bread: true
        },
        component: () => import('@/view/prisons/highlights/index.vue')
      },
      {
        path: '/eidtForm',
        name: 'eidtForm',
        meta: {
          title: '编辑',
          menu: true,
          bread: true
        },
        component: () => import('@/view/prisons/edit/eidtForm.vue')
      },

    ]
  },
  {
    path: '/person',
    name: 'person',
    meta: {
      title: '一人一档'
    },
    redirect: '/person/files',
    component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
    children: [{
        path: 'files',
        name: 'personFiles',
        meta: {
          title: '一人一档',
          menu: true,
          bread: true
        },
        component: () => import('@/view/person/index.vue')
      },
      {
        path: 'detail',
        name: 'detail',
        meta: {
          title: '一人一档-详情',
          menu: true,
          bread: true
        },
        component: () => import('@/view/person/info.vue')
      },

    ]
  },
  {
    path: '/monitor',
    name: 'monitor',
    meta: {
      title: '一室一档'
    },
    redirect: '/monitor/files',
    component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
    children: [{
        path: 'files',
        name: 'monitorFiles',
        meta: {
          title: '一室一档',
          menu: true,
          bread: true
        },
        component: () => import('@/view/roomArchive/index.vue')
      },
      {
        path: 'roomArchiveDetail',
        name: 'roomArchiveDetail',
        meta: {
          title: '一室一档-详情',
          menu: true,
          bread: true
        },
        component: () => import('@/view/roomArchive/detail.vue')
      },
    ]
  },
  {
    path: '/police',
    name: 'police',
    meta: {
      title: '一警一档'
    },
    redirect: '/police/files',
    component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
    children: [{
        path: 'files',
        name: 'policeFiles',
        meta: {
          title: '一警一档',
          menu: true,
          bread: true
        },
        component: () => import('@/view/police/index.vue')
      },
      {
        path: 'detail',
        name: 'detail',
        meta: {
          title: '一警一档-详情',
          menu: true,
          bread: true
        },
        component: () => import('@/view/police/detail.vue')
      },


    ]
  },
]

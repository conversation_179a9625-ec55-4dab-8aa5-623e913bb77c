<template>
  <div>
    <div class="table-container" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="kfjykcmclb" :customFunc="true" :params="params">
        <!-- 设备报修登记 -->
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':kfjykcmclb:add')"
            @click.native="handleAddKc('add')">新增课程</Button>
        </template>
        <template slot="slot_is_enabled" slot-scope="{ func, row, index }">
          <i-switch v-model="row.is_enabled" size="large" :true-value="1" :false-value="0"
            @on-change="changeSwitch(row)">
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary" style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':kfjykcmclb:edit')"
            @click.native="handleEditKc(index, row)">编辑</Button>
          <Button style="margin-left: 10px;" type="error" v-if="func.includes(globalAppCode + ':kfjykcmclb:del')"
            @click.native="handleDelKc(index, row)">删除</Button>
        </template>
      </s-DataGrid>
    </div>
    <addCourseModal :isShowhandleModal="isOpen" :form="form" :row-id="rowId" @updateTable="updateTable"/>
  </div>

</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import addCourseModal from './addCourseModal.vue'
export default {
  name: "courseTypeManagement",
  data() {

    return {
      tableContainer: true,
      params: {},
      form: {
        coursesColor: "",
        coursesName: "",
        isEnabled: 1
      },
      isOpen: false,
      rowId: "",
    }

  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAddKc(row) {
      this.isOpen = true
    },
    handleEditKc(idx, { id }) {
      this.authGetRequest({ url: this.$path.edurehabCourses_get, params: { id } }).then(res => {
        if (res.success) {
          this.isOpen = true
          this.form = res.data
          this.rowId = res.data.id
        }
      })
    },
    handleDelKc(idx, { id }) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.edurehabCourses_delete,
            params: {
              ids: id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        }
      })
    },
    updateTable() {      
      this.isOpen = false
      this.$refs.grid.query_grid_data(1)
    },
    changeSwitch(row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否更新？',
        onOk: () => {
          this.$store.dispatch('authPostRequest', {
            url: this.$path.edurehabCourses_update,
            params: {
              coursesName: row.courses_name,
              coursesColor: row.courses_color,
              isEnabled: row.is_enabled,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        },
        onCancel: () => {
          let num = row.is_enabled == 1 ? 0 : 1
          this.$store.dispatch('authPostRequest', {
            url: this.$path.edurehabCourses_update,
            params: {
              coursesName: row.courses_name,
              coursesColor: row.courses_color,
              isEnabled: num,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        }
      })
    },
  },

  components: {
    sDataGrid,
    addCourseModal
  },

  created() { },

  computed: {},

}

</script>

<style scoped lang="less">

</style>

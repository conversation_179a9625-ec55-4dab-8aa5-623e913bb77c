<!-- 会见 -->
<template>
  <div>
    <div class="">
      <div class="bsp-base-content" v-if="!showData">
        <s-DataGrid ref="grid" funcMark="ckywlshjdjlb" :customFunc="true" :params="params">
          <template v-slot:query:area_id="{ condi, row, index }">
            <Row>
              <Col span="24">
              <s-dicgrid v-model="condi.area_id" @values="getArea" requestType="loopData" v-if="showJqArray"
                showField="areaName" keyField="areaCode" :loopData="jqArray" popper-class="DataGrid-BOX"
                style="width: 100%" />
              </Col>
            </Row>
          </template>
          <template v-slot:query:jsh="{ condi, row, index }">
            <Row>
              <Col span="24">
              <s-dicgrid v-model="condi.jsh" requestType="loopData" v-if="showJshArray" showField="roomName"
                keyField="roomCode" :loopData="jshArray" popper-class="DataGrid-BOX" style="width: 100%" />
              </Col>
            </Row>
          </template>
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:add')"
              @click.native="addEvent('add')">律师会见申请</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:dj')"
              @click.native="addEvent('add')">律师会见登记</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }" class="btnList">
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:info')"
              @click.native="editEvent(index, row, 'info')">详情</Button>&nbsp;&nbsp;
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:supplemental') && row.status == 99"
              @click.native="editEvent(index, row, 'supplementary')">补录</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:qd') && row.status == 0"
              @click.native="signIn(row)">签到</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:Room') && row.status == 1"
              @click.native="openRoom(row)">分配会见室</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:Check') && row.status == 2"
              @click.native="openCheck(row)">带出安检</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:security') && row.status == 3"
              @click.native="securityCheck(row)">会毕安检</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':ckywlshjdjlb:wsdy')"
              @click.native="wsdy(row)">文书打印</Button>
          </template>
          <!-- <template slot="slot_status" slot-scope="{ row, index }">
				  </template> -->
        </s-DataGrid>
      </div>
      <!-- 登记 -->
      <div v-if='showData' class='InquiryTitle'>{{ modalTitle }}</div>
      <addForm v-if='saveType == "add" && showData' @toback='toback' />
      <detail v-if='saveType == "info" && showData' @toback='toback' :curId='curData.id' />
      <supplementaryRecording v-if='saveType == "supplementary" && showData' @toback='toback' :curId='curData.id' />
    </div>

    <!-- 签到 -->
    <Modal v-model="openModalQd" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="30%"
      title="签到">
      <div
        style='text-align:center;font-family: Source Han Sans CN, Source Han Sans CN;font-weight: 400;font-size: 16px;padding-bottom:16px;'>
        <p style='line-height:60px'>预约律师已到达现场，确认签到</p>
        <div>签到时间： <el-date-picker format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss'
            v-model="curData.checkInTime" type="datetime" size='small' placeholder="选择日期时间">
          </el-date-picker>
        </div>
      </div>
      <div slot="footer">
        <Button @click="openModalQd = false">取消</Button>
        <Button type="primary" @click="submitQd" :loading="loadingsignIn">提交</Button>
      </div>
    </Modal>
    <!-- 选择会见室 -->
    <Modal v-model="openModalRoom" :mask-closable="false" :closable="true" class-name="select-room-modal" width="50%"
      title="选择会见室">
      <interrogationRoom v-if='openModalRoom' :curId='curData.id' @selectRoom='selectRoom' />
      <div slot="footer">
        <Button @click="openModalRoom = false">取消</Button>
        <Button type="primary" @click="submitRoom" :loading="loadingRoom">提交</Button>
      </div>
    </Modal>
    <!-- 带出安检登记 -->
    <Modal v-model="openModalCheck" :mask-closable="false" :closable="true" class-name="select-room-modal" width="40%"
      title="带出安检登记">
      <checkRegistration ref="checkRegistration" v-if='openModalCheck' :curId='curData.id'
        @escortingInspect='escortingInspect' />
      <div slot="footer">
        <Button @click="openModalCheck = false">取消</Button>
        <Button type="primary" @click="submitCheck" :loading="loadingCheck">提交</Button>
      </div>
    </Modal>
    <!--会毕安检登记 -->
    <Modal v-model="openModalSecurity" :mask-closable="false" :closable="true" class-name="select-room-modal"
      width="40%" title="会毕安检登记">
      <!-- <securityCheck ref="securityCheck" v-if='openModalSecurity' :curId='curData.id' @returnInspect='returnInspect' /> -->
      <huibiCheck ref="huibiCheck" v-if='openModalSecurity' :curId='curData.id' @returnInspect='returnInspect' />

      <div slot="footer">
        <Button @click="openModalSecurity = false">取消</Button>
        <Button type="primary" @click="submitSecurity" :loading="loadingSecurity">提交</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import addForm from "./add.vue"
import { getUuid, removeNullFields, getUserCache, formatDateparseTime } from "@/libs/util.js";
import interrogationRoom from "./interrogationRoom"
import checkRegistration from './checkRegistration.vue';
import securityCheck from './securityCheck.vue';
import huibiCheck from './HuibiCheck.vue'
import detail from './detail.vue';
import supplementaryRecording from './supplementaryRecording.vue';
export default {
  components: {
    sDataGrid, addForm, interrogationRoom, checkRegistration, securityCheck, huibiCheck, detail, supplementaryRecording
  },
  data() {
    return {
      openModalQd: false,
      loadingsignIn: false,
      openModalRoom: false,
      loadingRoom: false,
      openModalCheck: false,
      loadingCheck: false,
      openModalSecurity: false,
      loadingSecurity: false,
      curData: {},
      showFile: false,
      showImg: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      importUrl: this.$path.upload_fj,
      custom_loading: false,
      defaultList: [],
      fileList: [],
      buttenloading: false,
      stream: null,
      uploadForm: {},
      params: {},
      showData: false,
      modalTitle: '提讯登记',
      openModal: false,
      formData: {
        isDisabled: 0
      },
      loading: false,
      saveType: 'list',
      loadingStates: [],
      curRoomId: '',
      jqArray: [],
      showJqArray: false,
      jshArray: [],
      showJshArray: true,
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    toback() {
      this.showData = false
    },
    // 文书打印
    wsdy(row) {
      window.open('/#/counselPrinting?jgrybm=' + row.jgrybm+'&businessId='+ row.id)
    },
    //签到
    signIn(row) {
      this.curData = row
      if (!this.curData.checkInTime) {
        this.$set(this.curData, 'checkInTime', formatDateparseTime(new Date()))
      }
      this.openModalQd = true
    },
    submitQd() {
      this.loadingsignIn = true
      let params = {
        checkInTime: this.curData.checkInTime,
        id: this.curData.id
      }
      this.$store.dispatch('authGetRequest', { url: this.$path.acp_lawyerMeeting_signIn, params: params }).then(resp => {
        if (resp.success) {
          this.loadingsignIn = false
          this.$Message.success('签到成功')
          this.on_refresh_table()
          this.openModalQd = false
        } else {
          this.loadingsignIn = false
          this.$Message.error(resp.msg || '签到失败')
        }
      })
    },
    //会见室
    openRoom(row) {
      this.curData = row
      this.openModalRoom = true
    },
    selectRoom(roomId) {
      this.curRoomId = roomId
    },
    submitRoom() {
      this.loadingRoom = true
      let params = {
        roomId: this.curRoomId,
        id: this.curData.id
      }
      this.$store.dispatch('authGetRequest', { url: this.$path.acp_lawyerMeeting_allocationRoom, params: params }).then(resp => {
        if (resp.success) {
          this.loadingRoom = false
          this.$Message.success('分配成功')
          this.openModalRoom = false
          this.on_refresh_table()

        } else {
          this.loadingRoom = false
          this.$Message.error(resp.msg || '分配失败')
        }
      })
    },
    // 带出安检
    openCheck(row) {
      this.curData = row
      this.openModalCheck = true
    },
    submitCheck() {
      this.$refs.checkRegistration.submitClick()
    },
    escortingInspect(tag) {
      if (tag) {
        this.on_refresh_table()
        this.openModalCheck = false
      } else {
      }
    },
    //会毕安检
    securityCheck(row) {
      this.curData = row
      this.openModalSecurity = true
    },
    submitSecurity() {
      this.$refs.huibiCheck.submitClick()
    },
    returnInspect(tag) {
      if (tag) {
        this.on_refresh_table()
        this.openModalSecurity = false
      } else {
      }
    },
    // 上传组件回调方法
    handleSuccessImg(res, index) {
      console.log(res, '1212')
      this.defaultList.push(res.data);
      //   this.formData.fileList=this.defaultList
      this.defaultList.forEach(ele => {
        ele.src = ele.url
        ele.imgSrc = ele.url
      })
      //   this.imgArr=this.imgArr.concat(this.uploadList);
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.defaultList.splice(e.index, 1);
          this.imgArr.forEach((ele, i) => {
            if (ele.url == e.url) {
              this.defaultList.splice(i, 1);
            }
          })
        },
      });
    },
    onSelect(data) {
      console.info(data, this.formData)
      let arr = []
      data.forEach(item => {
        arr.push(item.orgName)
      })
      this.$set(this.formData, 'badwmc', arr.join(','))
    },
    onClear(data) {
      console.info(data, this.formData, 'onClear')
      //    this.$set(this.formData,'badwmc','')
    },
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFile(data) {
      console.log(data, '1212')
      this.fileList = data
    },
    removeFile(file) {
      console.log(file, 'removeFile', file.length == 0)
      if (file.length == 0) {
        this.$set(this.formData, 'zpUrl', '')
        this.defaultList = []
      }
    },
    remove() {
      this.$set(this.formData, 'zpUrl', '')
      this.defaultList = []
    },
    addEvent(row) {
      this.showFile = true
      this.showImg = true
      this.saveType = 'add'
      this.formData = { isDisabled: 0, zpUrl: '' },
        this.fileList = []
      this.showData = true
      this.modalTitle = '律师会见登记'
      let faceImageUrl = this.formData.zpUrl
      if (faceImageUrl) {
        let urlObj = { url: faceImageUrl, name: '' }
        this.defaultList.push(urlObj)
      }
    },
    editEvent(index, row, tag) {
      if (tag == 'supplementary') {
        this.modalTitle = '律师会见补录'
        this.saveType = 'supplementary'
        this.showData = true
        this.curData = row
      } else {
        this.modalTitle = '律师会见登记'
        this.saveType = 'info'
        this.showData = true
        this.curData = row
      }
    },
    getArea(data) {
      this.showJshArray = false
      setTimeout(() => {
        this.jshArray = data && data.length > 0 && data[0].children ? data[0].children : []
        this.showJshArray = true
      }, 500)
    },
    getJqData() {
      let params = {
        orgCode: this.$store.state.common.orgCode
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/area/getAreaListByOrgCode',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.jqArray = resp.data
          this.showJqArray = true
        } else {
          this.showJqArray = true
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    deleleChange(row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.acp_casePersonnel_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            console.log(res);
            if (res.success) {
              this.on_refresh_table()
            }
          })
        }
      })
    },
    onCancel() {
      this.showFile = false
      this.showImg = false
      this.openModal = false
      this.stopCamera()
    },
    submitClick() {
      this.$refs['releaseForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.saveForm()
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    saveForm() {
      console.log(this.formData, 'this.formData');
      if (this.fileList && this.fileList.length > 0) {
        this.$set(this.formData, 'gzzjUrl', JSON.stringify(this.fileList))
      } else {
        this.$set(this.formData, 'gzzjUrl', '')
      }
      // return
      let url = ''
      if (this.formData.id) {
        url = this.$path.acp_casePersonnel_update
      } else {
        url = this.$path.acp_casePersonnel_create
      }
      this.$store
        .dispatch("authPostRequest", {
          url: url,
          params: this.formData
        }).then(res => {
          console.log(res, 'res');
          if (res.success) {
            this.loading = false
            this.openModal = false
            this.on_refresh_table()
          } else {
            this.$Message.error(res.msg || '保存失败！')
            this.loading = false
          }
        })
    },
    changeStatus(row, val) {
      console.log(row, val);
      this.$store.dispatch('postRequest', {
        url: this.$path.bsp_pam_reportItem_updateStatus,
        params: {
          id: row.id,
          status: row.status,
        }
      }).then(res => {
        if (res.success) {
          this.$Message.success('修改成功!!')

          //   this.on_refresh_table()
        }
      })
    },
    handleOrgIChange(newVal, oldVal) {
      //   console.log(newVal, oldVal,'newVal, oldVal');
      if (!newVal) {
        this.params.orgCode = this.$store.state.common.orgCode
        this.showData = false
      } else {
        this.showData = false
        this.params.orgCode = newVal
      }
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1);
      this.showJshArray = true
    },
  },
  mounted() {
    this.getJqData()
  }
}
</script>

<style scoped lang="less">
.ivu-form-item {
  margin-bottom: 10px !important;
}

.InquiryTitle {
  border-bottom: 1px solid #dcdee2;
  padding: 16px;
}

.ivu-table-cell-slot button {
  margin: 5px 0;
}
</style>

let menuMode = localStorage.getItem('menuMode')
// import main from '@/components/app-main/index.vue'
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'

export default [
  {
    path: '/tfjbcz',
    name: 'tfjbcz',
    meta: {
      title: '突发疾病处置'
    },
    redirect: '/tfjbcz/list',
    component: menuMode == 'side' ? mainNew : main,
    children: [
      {
        path: 'list',
        name: 'list',
        meta: {
          title: '突发疾病处置',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/tfjb/index.vue')
      }
    ]
  }
]

<template>
  <div class="tabs_main">
    <div class="tabs_top">
      <div @click="changeTabs(item.mark, item.name)" v-for="item, index in tabsData" :key="item.id"
        class="tabs_top_item" :class="[
          tabsMark == item.mark ? 'tabs_top_item_active' : 'tabs_top_item',
        ]">
        {{ item.name }}
        <span v-if="isShowNum && numList[index]">（{{ numList[index] }}）</span>
        <Icon v-if="item.autoAdd" type="md-close-circle" style="margin-left: 10px;" @click="deteleItem(index)" />
      </div>
    </div>

    <div v-for="item in tabsData" style="flex: 1" v-if="tabsMark == item.mark" class="tabs_content">
      <div v-for="(e, d) in item.query.type" class="common">
        <!-- 表单 -->
        <div v-if="e == 'fm'" class="common">
          <fm @saveForm="saveForm" @cancal="cancal" :showSave="item.query.showSave" :parameter="item.query.param" />
        </div>
        <!-- 列表 -->
        <div class="common" v-else-if="e == 'table'">
          <!-- 列表 resetMethod -  列表刷新 -->
          <rs-DataGrid :ref="item.mark + '-grid'" :funcMark="item.query.mark[d]" :customFunc="true" :params="params">
            <!-- 头部操作插槽 -->
            <template slot="customHeadFunc"
              slot-scope="{ func, hasPermission, resetMethod, funcMark, appendEqualFuncMark }">
              <slot name="customHeadFunc" :func="func" :resetMethod="tabResetMethod" :funcMark="item.mark"
                :hasPermission="hasPermission" :appendEqualFuncMark="appendEqualFuncMark"></slot>
            </template>
            <!-- 行操作插槽 -->
            <template slot="customRowFunc"
              slot-scope="{ func, hasPermission,resetMethod, funcMark, appendEqualFuncMark,row, index }">
              <slot name="customRowFunc" :func="func" :row="row" :index="index" :resetMethod="tabResetMethod"
                :funcMark="item.mark" :hasPermission="hasPermission" :appendEqualFuncMark="appendEqualFuncMark"></slot>
            </template>
            <!-- 动态传递所有插槽，排除已处理的系统插槽 -->
            <template v-for="(_, slotName) in filteredScopedSlots" #[slotName]="scope">
              <slot :name="slotName" v-bind="scope"></slot>
            </template>

          </rs-DataGrid>
        </div>
        <!-- 流程 -->
        <div class="common" v-else-if="e == 'flow'">
          <comViewGraph ref="comViewGraphRef" :disabledNodesId="disabledNodesId" :renderJson="renderJson"
            @node:click="nodeClick"></comViewGraph>
        </div>
        <!-- 自定义组件 -->
        <div class="common" v-else-if="e == 'compontent'" style="padding-top: 16px;">
          <component v-bind:is='item.compontent'>
          </component>
        </div>
        <!-- 其他 -->
        <div v-else class="common">
          <slot :name="(item.query.mark || item.mark) + '-common'"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import vueComs from "./common"
import { mapActions } from "vuex";
import fm from "@/components/fm/component/fm.vue";
import { sDataGrid } from "sd-data-grid";
import comViewGraph from "@/components/comAntvX6Graph/comViewGraph";
import { getGraphNodeName } from "@/util";
import { getUuid } from "@/components/form/libs/util.js";
export default {
  components: {
    fm,
    sDataGrid,
    comViewGraph,
  },
  props: {
    mark: {
      type: String,
      default: null,
    },
    params: {
      type: Object,
    },
    curmark: {
      type: String,
      default: null,
    },
    curname: {
      type: String,
      default: null,
    },
    isShowNum: {
      type: Boolean,
      default: false,
    },
    numList: {
      type: Array,
      default: () => [],
    },
    isSlot: {
      type: Boolean,
      default: null
    }
  },
  data() {
    return {
      tabsMark: null,
      tabsData: [],
      operData: null,
      routerQuery: this.$route.query ? this.$route.query : {},
      disabledNodesId: [],
      renderJson: {},
      nodeClickParams: null,
    };
  },
  created() {
    console.log(this.params, 'params123')
    vueComs()
    if (this.mark) {
      this.getTabsData();
    }
    if (this.params && this.params.renderJson) {
      this.renderJson = this.params.renderJson;
    }
    if (this.params && this.params.disabledNodesId) {
      this.disabledNodesId = this.params.disabledNodesId;
    }
    if (this.params && this.params.nodeClick) {
      this.nodeClickParams = this.params.nodeClick;
      console.log(this.nodeClick, 'this.nodeClickthis.nodeClick')
    }
  },
  computed: {
    // 安全的重置表格方法
    tabResetMethod() {
      return () => {
        if (!this.tabsMark) {
          console.warn('tabsMark 未初始化，无法刷新表格');
          return;
        }

        const gridRef = this.$refs[this.tabsMark + '-grid'];
        if (!gridRef || !gridRef[0]) {
          console.warn('表格引用不存在，无法刷新表格');
          return;
        }

        gridRef[0].query_grid_data(1);
      };
    },
    // 过滤已处理的系统插槽，避免重复处理
    filteredScopedSlots() {
      const systemSlots = ['customRowFunc', 'customHeadFunc'];
      const filtered = {};

      Object.keys(this.$scopedSlots || {}).forEach(slotName => {
        if (!systemSlots.includes(slotName)) {
          filtered[slotName] = this.$scopedSlots[slotName];
        }
      });

      return filtered;
    }
  },
  methods: {
    ...mapActions(["authPostRequest"]),
    // 动态添加节点tab
    nodeClick(data, value) {
      let xx = getGraphNodeName(data)
      let obj = {
        "id": getUuid(),
        "name": xx.text,
        "mark": this.globalAppcode + ":" + xx.compontent,
        "compontent": xx.compontent,
        "query": {
          "type": ['compontent']
        },
        "orderId": this.tabsData.length,
        "autoAdd": true,
        "url": "type=compontent",
        "tabsId": this.tabsData[0].tabsId,//"1939907060087132162",
        "isControl": "1",
        "urlType": "0",
        "appId": this.tabsData[0].appId,//"1934985431515009024"
      }
      this.tabsMark = obj.mark
      let flag = false; //判断是否存在数组中
      for (let i = this.tabsData.length - 1; i >= 0; i--) {
        if (this.tabsData[i].mark == obj.mark) {
          // this.tabsData.splice(i, 1);
          flag = true
          break;
        }
      }
      if (!flag) {
        this.tabsData.push(obj)
      }

      this.$forceUpdate()
      // console.log(data, xx, this.tabsData, '121221')
    },
    // 删除动态节点
    deteleItem(index) {
      this.tabsData.splice(index, 1);
      this.tabsMark = this.tabsData[0].mark
    },
    // 根据mark值获取标签页数据
    getTabsData() {
      this.authPostRequest({
        url: this.$path.get_tabs_data + "/" + serverConfig.APP_CODE + ":" + this.mark,
        params: {
          appId: serverConfig.APP_ID,
        },
      }).then((res) => {
        if (res.code == 200 && res.success) {
          res.xxk.forEach((e) => {
            e.query = {};
            let data = e.url.split("&");
            data.forEach((d) => {
              if (d.includes("type")) {
                e.query.type = d.split("=")[1].split(",");
              }
              if (d.includes("mark")) {
                e.query.mark = d.split("=")[1].split(",");
              }
              if (d.includes("operType")) {
                e.query.operType = d.split("=")[1].split(",");
              }
              if (d.includes("templateId")) {
                e.query.templateId = d.split("=")[1].split(",");
              }
              if (d.includes("pkField")) {
                e.query.businessId = this.routerQuery.jgrybm ? this.routerQuery.jgrybm : d.split("=")[1].split(",");
                e.query.pkField = 'jgrybm'
              }
            });

            if (e.query.type == "fm") {
              // console.log(e.query, 'e.query')
              if (e.query.operType && e.query.operType != '2') {
                e.query.param = {
                  formId: e.query.mark[0],
                  operType: e.query.operType[0], //0为新增 1为修改
                  templateId: e.query.templateId[0],
                  businessId: e.query.businessId,
                  pkField: e.query.pkField
                };
                e.query.showSave = true;
              } else {
                e.query.param = {
                  formId: e.query.mark[0],
                  operType: 2, //2 详情
                  templateId: e.query.templateId[0],
                  businessId: e.query.businessId,
                  pkField: e.query.pkField
                };
                e.query.showSave = false;
              }
            }
          });
          this.tabsData = res.xxk;
          this.tabsMark = this.tabsData[0].mark;
          this.$emit(
            "changeTabsMark",
            this.tabsData[0].mark,
            this.tabsData[0].name
          );

        }
      });
    },
    // 切换tabs 给父组件返回对应的标识值
    changeTabs(e, name) {
      this.tabsMark = e;
      this.$emit("changeTabsMark", e, name);
    },
    // 保存的毁掉
    saveForm(params, data) {
      this.$emit("saveForm", params, data, this.tabsMark);
    },
    // 关闭的回调   返回接口参数   formData数据
    cancal(params, data) {
      this.$emit("cancal", params, data, this.tabsMark);
    },
    // 刷新列表
    resetTable() {
      this.$refs[this.tabsMark + '-grid'][0].query_grid_data(1)
    },
    getCurrentDataGridRef() {
      if (this.tabsMark && this.$refs[this.tabsMark + '-grid']) {
        return this.$refs[this.tabsMark + '-grid'][0].batch_select; // 返回第一个匹配的 DataGrid 实例
      }
      return null;
    },
  },
};
</script>
<style lang="less" scoped>
.tabs_main {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .tabs_top {
    background: #fafbff;
    display: flex;
    margin-bottom: 10px;
    overflow-x: auto;

    .tabs_top_item {
      padding: 8px 16px;
      font-size: 18px;
      color: #5f709a;
      border-left: 1px solid transparent;
      border-right: 1px solid transparent;
    }

    .tabs_top_item:hover {
      cursor: pointer;
      font-size: 18px;
      color: #2b3346;
      font-weight: bold;
    }

    .tabs_top_item_active {
      font-size: 18px;
      color: #2b3346;
      font-weight: bold;
      background: #ffffff;
      position: relative;
      border-left: 1px solid #e9edf5;
      border-right: 1px solid #e9edf5;
    }

    .tabs_top_item_active:before {
      content: "";
      display: block;
      width: 100%;
      height: 3px;
      background: #3399ff;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  /deep/ .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab {
    border-radius: 0;
    background: #fafbff;
  }

  /deep/ .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active {
    border-color: transparent;
    background: #fff;
  }

  /deep/ .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active {
    border-color: transparent;
    background: #fff;
  }

  /deep/ .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active:before {
    content: "";
    display: block;
    width: 100%;
    height: 3px;
    background: #3491fa;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.tabs_content {
  // flex: 1;
  width: 99%;
  overflow: auto;
  // display: none !important;
}

.tabs_content::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 10px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;

}

.tabs_content::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 3px;
  background: #b7c7dd;

}

.tabs_content::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 3px;
  background: #EDEDED;
}

.common {
  height: 100%;
}
</style>

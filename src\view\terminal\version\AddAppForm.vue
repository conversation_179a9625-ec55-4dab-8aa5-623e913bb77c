<template>
  <div class="health-education">
    <FormLayout
      ref="formLayout"
      :header-config="headerConfig"
      :show-header="false"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :form-data="formData"
      :responsive="true"
      :loading="loading"
      @header-action="handleHeaderAction"
      @bottom-action="handleBottomAction"
      @update:form-data="updateFormData"
    >
      <!-- 健康教育表单 -->
      <template #form>
        <DynamicForm
          ref="businessForm"
          v-model="formData"
          :config="formConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleFieldChange"
          @validate="handleValidate"
        />
      </template>
    </FormLayout>
  </div>
</template>

<script>
import { FIELD_TYPES, FORM_MODES } from '@/components/dynamic-form/types'
import { mapActions } from 'vuex'
import FormLayout from '@/components/bsp-layout/layouts/FormLayout.vue'
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'
import { ihcRequestUrl } from '@/path/ihc/path'
import { getToken} from '@/libs/util'


export default {
  name: 'AddWebForm',
  components: {
    FormLayout,
    DynamicForm
  },
  props: {
    pk: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      formMode: FORM_MODES.CREATE,
      uploadMode: FORM_MODES.EDIT,

      // 表单数据
      formData: {
        attachments: [], // 用于存储文件列表
      },

      // 头部配置
      headerConfig: {
        title: '版本管理',
        icon: 'ios-medical',
        actions: []
      },

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ]
    }
  },

  computed: {
    // 表单配置
    formConfig() {
      return [
        {
          title: '基本信息',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showHeader: true,
          fields: [
            {
              key: "attachments",
              label: "上传附件",
              type: FIELD_TYPES.BSP_FILE_UPLOAD, // 使用我们自定义的类型
              span: 24,
              required: true,
              props: {
                uploadUrl: this.$path.terminal_version_management_file_upload+'?access_token='+getToken(),
                mode: this.uploadMode, // 动态绑定模式
                width: 100,
                height: 100,
                autoUpload: true,
                format: ["apk"],
                maxCount: 1,
              },
            },{
              key: 'releaseNotes',
              label: '发布说明',
              type: FIELD_TYPES.TEXTAREA,
              required: false, // 保持与新增表单一致的必填状态
              span: 24,
              props: {
                placeholder: '发布说明',
                rows: 3
              }
            }
          ]
        }
      ]
    }
  },

  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest']),

    // 初始化数据
    initializeData() {
      // 如果有pk，则加载数据进行编辑
      if (this.pk) {
        this.formMode = FORM_MODES.EDIT
        this.loadRecordData(this.pk)
      } else {
        this.formMode = FORM_MODES.CREATE
        this.initializeFormData()
      }
    },

    // 初始化表单数据
    initializeFormData() {
      // 设置默认值
      this.formData = {
        ...this.formData,
        operateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: ihcRequestUrl.health_edu_get,
        params: { id: recordId }
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.formData = { ...this.formData, ...res.data }
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },

    // 处理表单数据更新
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 处理字段变化
    handleFieldChange(key, value, field) {

    },



    // 处理表单验证
    handleValidate(valid, errors) {
      console.log('表单验证结果:', valid, errors)
    },

    // 处理头部操作
    handleHeaderAction(action) {
      console.log('头部操作:', action)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSubmit()
          break
      }
    },

    // 处理返回
    handleBack() {
      this.$emit('toback')
    },

    // 处理提交
    handleSubmit() {
      console.log('开始表单验证...')
      // 验证表单
      this.$refs.businessForm.$refs.dynamicForm.validate((valid) => {
        if (valid) {
          // 验证时间段
          this.saveData()
        } else {
          this.$Message.error('请检查表单填写是否正确')
        }
      })
    },

    // 保存数据
    saveData() {
      this.loading = true
      // 准备提交数据
      const submitData = {
        id: this.formData.attachments[0].id,
        releaseNotes: this.formData.releaseNotes,
        packageName:  this.formData.attachments[0].name,
      }



      this.authPostRequest({
        url: this.$path.terminal_version_management_save_by_package_name,
        params: submitData
      }).then(res => {
        this.loading = false
        if (res.success) {
          this.$Message.success(this.formMode === FORM_MODES.EDIT ? '更新成功' : '保存成功')
          this.$emit('toback')
        } else {
          this.$Message.error(res.msg || '保存失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('保存失败：', error)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.health-education {
  height: 100%;

  /deep/ .form-layout {
    height: 100%;
  }

  /deep/ .dynamic-form-content {
    padding: 0;
  }

  /deep/ .ivu-date-picker {
    width: 100%;
  }
}
</style>

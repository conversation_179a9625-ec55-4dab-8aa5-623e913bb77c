<template>
  <div>
    <div class="my-header">
      <div class="log" style="z-index:99999;margin-right: 80px;min-width: 320px;cursor: pointer;" @click="$router.push('/homePage')">
        <img src="@/assets/images/u5.png" alt="" style="width:70px;"/>
        <h1>{{ appName }}</h1>
      </div>
      <div class="nav">
        <ul v-show="listTitle.length > 1" style="margin-left: 50%;">
          <li v-for="(item,index) in listTitle" :key="item.name" v-if="index<7"
              :class="{ active: $store.state.common.activeRootRouterName === item.name }" class="nav_box"
              @click="handleClick(item)">
            <span>{{ item.meta.title }}</span>
          </li>

          <!-- <Dropdown placement="bottom-start" style="position: relative;">
            <li >
                更多<Icon type="ios-arrow-down"></Icon>
            </li>
            <DropdownMenu slot="list">
                <DropdownItem :class="{ active: $store.state.common.activeRootRouterName === item.name }" class="nav_box" v-for="(item,index) in listTitle" v-if="index>6"  @click="handleClick(item)" :key="index+'listTitle'">{{ item.meta.title }}</DropdownItem>
            </DropdownMenu>
          </Dropdown> -->
          
        </ul>
        <!-- <ul style="position: relative;">
          <li v-if="listTitle && listTitle.length>6" @click="cardSelect=!cardSelect" >更多模块<Icon  :type="!cardSelect?'ios-arrow-down':'ios-arrow-up'" /></li>
              <Card style="width:200px;top: 65px;position: absolute;z-index: 99999;" class="cardSelect" v-if="listTitle && listTitle.length>6 && cardSelect" @mouseover="cardSelect=true" @mouseleave="cardSelect=false">
                <ul class="card-ul">
                    <li v-if="i>6" v-for="(item, i) in listTitle" @click="handleClick(item)" :class="{ active: $store.state.common.activeRootRouterName === item.name }" class="nav_box">{{ item.meta.title }}</li>
                </ul>
            </Card>
        </ul>     -->
      </div>
      <!-- <div class="app-config-wrapper">
        <Tooltip content="平台管理" style="position: absolute;right: 200px; top: 0px;">
          <div 
            class="app-config" :class="{
              'app-config-active':$store.state.common.activeRootRouterName===appConfigRoute.name
            }" @click="goToAppConfig"
          ><img  style="margin-top: 16px;" src='@/assets/images/common/option.png' /></div>
        </Tooltip>
      </div> -->
    </div>
  </div>
</template>

<script type="text/javascript">
import {getMenuByRouter, getPermMenu} from '@/libs/util'

export default {
  name: 'Header',
  components: {},
  props: {
    msg: {
      type: String,
      default: 'test msg'
    }
  },
  methods: {
    handleClick: function (item) {
      console.log(item,'handleClick12')
      this.cardSelect=false
      if (this.$store.state.common.activeRootRouterName === item.name) return
      this.activeName = item.name
      this.$store.commit('setActiveRootRouterName', item.name)
      if (item.path && item.path.startsWith('http')) {
        this.iframeState.showIframe = true
        this.iframeState.iframeSrc = item.path
      } else {
        this.iframeState.showIframe = false
        if (item.children.length === 1 && !item.children[0].children) {
          this.iframeState.showMenu = false
        } else {
          this.iframeState.showMenu = true
        }
        this.commitIframeState()

        if (item.name === "bsp:com") {
          this.$parent.$parent.$parent.turnToPage('/com/engineCenter')
        } else {
          this.$parent.$parent.$parent.turnToPage(this.getRouterNameByTrue(item.children[0]))
        }
        // if(item && item.name=='bsp:com'){
        //   this.$router.push('/com/engine')
        // }else{
        // this.$parent.$parent.$parent.turnToPage(this.getRouterNameByTrue(item.children[0]))

        // }
      }
    },
    goToAppConfig() {
      this.handleClick(this.appConfigRoute)
    },
    // 递归获取有实质组件的第一个路由名称
    getRouterNameByTrue(item) {
      const getRouterName = item => {
        if (item.children && item.children.length) {
          return getRouterName(item.children[0])
        } else {
          return item.path
        }
      }
      return getRouterName(item)
    },
    commitIframeState() {
      this.$emit('getIframeState', this.iframeState)
    }
  },
  data() {
    return {
           cardSelect:false,
      appName: serverConfig.APP_NAME,
      isActive: null,
      activeName: this.$store.state.common.activeRootRouterName,
      iframeState: {
        showIframe: false,
        showMenu: true,
        iframeSrc: ''
      }
    }
  },
  computed: {
    listTitle() {
      // //console.log(getMenuByRouter(getPermMenu(), []).filter(item => item.name !== 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).filter(item => item.name !== )')
      return getMenuByRouter(getPermMenu(), [])
    },
    appConfigRoute() {
      // //console.log(getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')')
      return getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')
    }
  }
}
</script>
<style lang="less" scoped>
.my-header {
  height: 60px;
  background:#2b5fda;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  padding-left: 8px;
  display: flex;
  position: relative;
}

.log {
  font-size: 30px;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.log img {
  display: inline-block;
  /* width: 40px; */
  vertical-align: middle;
}

.log h1 {
  display: inline-block;
  margin: 0 0 0 0px;
  color: #fff;
  font-weight: 600;
  font-size: 30px;
  line-height: 30px;
  vertical-align: middle;
}

.nav {
  display: flex;
  white-space: nowrap;
  /* margin-left: 480px; */
  height: 100%;
  transform: translateX(-50%);
}

.nav > div {
  cursor: pointer;
}

.title_nav {
  background: #1c324e;
  text-align: right;
  color: #fff;
  padding: 5px 0;
  display: flex;
  justify-content: flex-end;
}

.title_nav > div {
  margin: 0 10px;
  cursor: pointer;
}

.nav_list {
  position: absolute;
  width: 100%;
  top: 87px;
  z-index: 10000;
  left: 0;
  background: #fff;
  color: #666;
  padding: 0 8%;
  min-height: 250px;
  display: none;
}

.nav_list > span {
  list-style: none;
  margin: 3%;
}

.nav_box > span {
  padding: 5px 10px;
  font-size: 20px;
}

.nav_box.active {
  background:rgba(36, 79, 179, 1);
  /* // linear-gradient( 180deg, rgba(36, 79, 179, 1) 0%, rgba(2, 187, 230, 0.6) 100%); */
}

.nav_box.active .nav_list {
  display: block;
}

.nav ul {
  display: flex;
}

.nav ul li {
  list-style-type: none;
  padding: 0 30px;
  cursor: pointer;
}

.nav ul li.active span {
  font-size: 20px;
  font-weight: 700;
}

.app-config-wrapper {
  position: absolute;
  /* right: 202px; */
  height: 60px;
  /* margin-top: 15px; */
  width: 700px;
  height: 60px;
  background: url("~@/assets/images/lightBg.png");
  position: relative;
  right: 0;
}

.app-config {
  height: 60px;
  width: 60px;
  /* background-image: url("~@/assets/images/common/option.png"); */
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: center center;
  text-align: center;
  &:hover {
    cursor: pointer;
    background-color:rgba(255, 255, 255, 0.15);
  }
}

.app-config-active {
  /* background-color:rgba(255, 255, 255, 0.6); */
  background: linear-gradient( 180deg, rgba(153, 235, 255, 0.6) 0%, rgba(2, 187, 230, 0.6) 100%);
}
.card-ul{
  display: flex;
  flex-wrap: wrap;
}
.card-ul li.active{
  color: #fff !important;
  background: #2b5fda !important;
  width: 100%;
}
.card-ul li{
  list-style:none;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #3E4E66;
  width: 100%;
  border-radius: 6px;
  margin-top: 5px;
  &:hover{
    background: #EBF1FF;
  }
}
</style>

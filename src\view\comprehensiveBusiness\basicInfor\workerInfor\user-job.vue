<template>
<span>
    <Modal v-model="value" :mask-closable="false" :footer-hide="true" :closable="false" class-name="bsp-job-assign" :width="1024">
        <div class="flow-modal-title" slot="header">
            <span style="font-size: 17px !important;">岗位分配</span>
            <span @click="cancel" style="position: absolute; right: 6px;font-size: 32px;cursor: pointer;">
                <i class="ivu-icon ivu-icon-ios-close"></i>
            </span>
        </div>
        <div class="bsp-job-assign" style="overflow:hidden;">
            <Row style="border:1px solid #CEE0F0;">
                <Col span="12" style="border-right:1px solid #CEE0F0;">
                <div>
                    <div class="tit-box">
                        已分配岗位
                        <div style="float:right">
                            <Button type="primary" class="main-button" @click="handleAdd" >
                                <Icon type="md-add" style="font-size:18px !important;margin-left: -3px; "/>新增</Button>
                        </div>
                    </div>
                    <div>
                        <div style="height:540px;padding: 8px 0;font-size:16px;" class="bsp-scroll">
                            <table class="assign-table">
                                <tr v-for="(item, i) in userJobList" :key="i" :class="{'bc':i%2===0, 'active': item.id === currJobUserId}" @click="jobClick(item, i)">
                                    <td style="width:28%" class="td-box">{{item.jobName}}</td>
                                    <td class="td-box">{{item.orgSname}}</td>
                                    <td class="job-del" style="width:10%" @click="delJobUser(item)">
                                        <!-- <img src="@/view/static/delorg.png" class="img-box" /> -->
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                </Col>
                <Col span="12">
                <div>
                    <div class="tit-box">
                        {{operName}}岗位信息
                    </div>
                    <div style="height:540px;padding: 8px 0;font-size:16px;background:#FAFBFF" class="bsp-scroll">
                        <div class="bsp-base-form" style="position:unset;">
                            <Form :label-width="120" :model="formValidate" ref="baseForm" label-colon>
                                <Row>
                                    <Col span="22">
                                    <FormItem label="选择岗位" prop="jobName" :rules="{required: true, message: '岗位不能为空',trigger: 'change' }">
                                        <s-dialog v-model="formValidate.jobName" return="jobId:id|jobName:name"  v-bind.sync="formValidate" dialogMark='bsp:app:job:dialog'></s-dialog>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="22">
                                    <FormItem label="所在单位">
                                        <Input v-model="userOrgName" readonly />
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="22">
                                    <FormItem label="岗位单位" prop="orgId" :rules="{required: true, message: '岗位单位不能为空',trigger: 'change' }">
                                        <org-selector ref="org" v-model="formValidate.orgId" @on-select="onOrgSelect" :text="formValidate.orgSname" return="orgSname:orgname" v-bind.sync="formValidate" :multiple="false">
                                        </org-selector>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="22">
                                    <FormItem label="是否默认">
                                        <RadioGroup v-model="formValidate.isDefault" size="large">
                                            <Radio label="0">否</Radio>
                                            <Radio label="1">是</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <!-- 去掉时效类型，设置为长期有效 -->
                                <!-- 
                                <Row>
                                    <Col span="22">
                                    <FormItem label="时效类型">
                                        <RadioGroup v-model="formValidate.validType" size="large">
                                            <Radio label="01">长期有效</Radio>
                                            <Radio label="02">临时生效</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row v-show="formValidate.validType==='02'">
                                    <Col span="22">
                                    <FormItem label="生效日期">
                                        <Row>
                                            <Col span="11">
                                            <el-date-picker style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" size="small" v-model="formValidate.beginTime" type="date" placeholder="开始日期"></el-date-picker>
                                            </Col>
                                            <Col span="2" style="text-align: center">至</Col>
                                            <Col span="11">
                                            <el-date-picker style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" size="small" v-model="formValidate.endTime" type="date" placeholder="结束日期"></el-date-picker>
                                            </Col>
                                        </Row>
                                    </FormItem>
                                    </Col>
                                </Row>
                                -->
                                <Row>
                                    <Col span="22">
                                    <FormItem label="岗位角色" class="job-role">
                                        <Tag v-for="(item, i) in roleList" :key="item.id">{{item.roleName}}</Tag>
                                        &nbsp;
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="22">
                                    <FormItem label="分配角色" class="assign-role">
                                        <Button icon="md-add" type="dashed" style="height: 28px;padding: 0px 8px 0px 8px;margin-right: 5px;" @click="handleRole()">选择角色</Button>
                                        <Tag class="role-tag" closable v-for="(item, index) in selectedUnSubRoleList" :key="index" :name="item.id" @on-close="removeRole">{{item.name}}</Tag>
                                        <Tag class="role-tag" closable v-for="(item, i) in selectedUnSubPublicList" :key="i" :name="item.id" @on-close="removePublic">{{item.name}}</Tag>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="24" style="text-align:center;">
                                        <Button style="width: 60px;" class="cancel-btn" @click="job_cancel(false)">关 闭</Button>&nbsp;&nbsp;
                                        <Button type="primary" style="width: 60px;margin-left: 5px" class="main-button" @click="handleSubmit">保 存</Button>
                                    </Col>
                                </Row>

                            </Form>
                        </div>
                    </div>
                </div>
                </Col>
            </Row>
        </div>
        <role-assign v-if="cascadeComp" :return-val="type" :app-id="appId" :has-selected-system="roleIdArr"
                     :has-selected-public="rolePublicIdArr" @on_cancel="role_cancel" @choice_confirm="choice_confirm"></role-assign>
    </Modal>

</span>
</template>

<script>
import {
    sDialog
} from 'sd-custom-dialog'
import {
    orgSelector
} from 'sd-org-selector'
import roleAssign from '@/components/role-assign/role-assign.vue'
export default {
    components: {
        sDialog,
        orgSelector,
        roleAssign
    },
    name: 'sUserJob',
    props: {
        user: {
            type: Object,
            default: () => {}
        },
        value: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            roleList: [],
            formValidate: {
                jobName: '',
                jobId: '',
                orgId: '',
                validType: '01',
                userId: this.user.id,
                isDefault: '0',
                orgCode: this.user.org_code,
                orgName: this.user.org_name,
                orgSname: ''
            },
            operName: '新增',
            userJobList: [],
            currJobUserId: '',
            userRoles: [],
            currOrgCode: this.$store.state.common.orgCode,
            userId: this.user.id,
            userOrgName: this.user.org_name,
            showDialog: false,
            curMark: 'bsp:app:job:dialog',
            cascadeComp:false,
            type:"1",
            appId: this.$route.query.appId ? this.$route.query.appId: 'admin',
            roleIdArr:[],
            rolePublicIdArr:[],
            selectedUnSubRoleList:[],
            selectedUnSubPublicList:[],
            choiceUserJob:{},
            index: 0
        }
    },
    mounted() {
        this.getJobUserList()
    },
    watch: {
        'formValidate.jobId'(newValue, oldValue) {
            if (newValue) {
                this.getJobRoles(newValue)
            }
        },
        'formValidate.orgId'(newValue, oldValue) {
            if (newValue) {
                this.getUserRoles(this.userId, newValue)
            }
        }
    },
    methods: {
        cancel() {
            this.$emit('input', false)
        },
        warnMsg(msg) {
            this.$Modal.warning({
                title: '温馨提示',
                content: msg
            })
        },
        // 获取用户岗位
        getJobUserList() {
            this.$store.dispatch('postRequest', {
                url: this.$path.get_user_job_url,
                params: {
                    userId: this.userId
                }
            }).then(data => {
                if (data.success) {
                    this.userJobList = data.data
                    if (this.userJobList.length > 0) {
                        this.jobClick(this.userJobList[this.index], this.index)
                    } else {
                        this.handleAdd()
                    }
                } else {
                    this.warnMsg(data.msg)
                }
            })
        },
        jobClick(jobUser, index) {
            if (this.currJobUserId !== jobUser.id) {
                this.currJobUserId = jobUser.id
                this.formValidate = Object.assign({}, jobUser)
            }
            this.choiceUserJob = {
                userId:this.userId,
                orgId:this.formValidate.orgId
            }
            this.index = index
            this.operName = '编辑'
        },
        delJobUser(jobUser) {
            let _this = this
            this.$Modal.confirm({
                title: '是否确认删除',
                loading: true,
                onOk: async () => {
                    _this.submitDel(jobUser.id)
                }
            })
        },
        submitDel(id) {
            this.$store.dispatch('postRequest', {
                url: this.$path.job_user_delete_url,
                params: {
                    id: id
                }
            }).then(data => {
                if (data.success) {
                    this.$Notice.success({
                        title: '成功提示',
                        desc: "删除成功"
                    })
                    this.$Modal.remove()
                    this.index = 0
                    this.getJobUserList()
                } else {
                    this.warnMsg(data.msg)
                }
            })
        },
        getJobRoles(jobId) {
            this.$store.dispatch('postRequest', {
                url: this.$path.job_role_all_url,
                params: {
                    jobId: jobId
                }
            }).then(data => {
                if (data.success) {
                    this.roleList = data.data
                } else {
                    this.warnMsg(data.msg)
                }
            })
        },
        getUserRoles(userId, orgId) {
            this.$store.dispatch('postRequest', {
                url: this.$path.user_org_role_all_url,
                params: {
                    userId: userId,
                    orgId: orgId,
                }
            }).then(data => {
                this.selectedUnSubRoleList = []
                this.selectedUnSubPublicList = []
                this.roleIdArr = []
                this.rolePublicIdArr = []
                if (data.success) {
                    let dataArr = data.data
                    if (dataArr && dataArr.length > 0) {
                        dataArr.forEach(e=>{
                            let obj = {
                                id:e.roleId,
                                name:e.roleName
                            }
                            if (e.type === '01') {
                                this.roleIdArr.push(e.roleId)
                                this.selectedUnSubRoleList.push(obj)
                            } else {
                                this.rolePublicIdArr.push(e.roleId)
                                this.selectedUnSubPublicList.push(obj)
                            }
                        })
                    }
                } else {
                    this.warnMsg(data.msg)
                }
            })
        },
        roleTagClose(event, name) {
            for (let i in this.userRoles) {
                if (this.userRoles[i]['id'] === name) {
                    this.userRoles.splice(i, 1)
                    return
                }
            }
        },
        getUserById(id) {
            this.$store.dispatch('postRequest', {
                url: this.$path.uac_user_get_by_id_url,
                params: {
                    id: id,
                }
            }).then(data => {
                if (data.success && data.data) {
                    this.userOrgName = data.data.orgName
                } else {
                    this.warnMsg(data.msg)
                }
            })
        },
        handleSubmit() {
            this.roleIdArr = []
            this.rolePublicIdArr = []
            if (this.selectedUnSubRoleList && this.selectedUnSubRoleList.length > 0) {
                this.selectedUnSubRoleList.forEach(e=>{
                    this.roleIdArr.push(e.id)
                })
            }
            if (this.selectedUnSubPublicList && this.selectedUnSubPublicList.length > 0) {
                this.selectedUnSubPublicList.forEach(e=>{
                    this.rolePublicIdArr.push(e.id)
                })
            }
            this.$refs['baseForm'].validate(valid => {
                if (valid) {
                    if (this.formValidate.validType === '02' && !this.formValidate.beginTime && !this.formValidate.endTime) {
                        this.warnMsg("生效日期不能为空")
                        return
                    }
                    let param = Object.assign({}, this.formValidate)
                    let roleSystemIds = []
                    let rolePublicIds = []
                    this.selectedUnSubRoleList.forEach(e => {
                        roleSystemIds.push(e.id)
                    })
                    this.selectedUnSubPublicList.forEach(e => {
                        rolePublicIds.push(e.id)
                    })
                    param.roleSystemIds = roleSystemIds.join(",")
                    param.rolePublicIds = rolePublicIds.join(",")
                    this.$store.dispatch('postRequest', {
                        url: this.$path.job_user_save_url,
                        params: param
                    }).then(data => {
                        if (data.success) {
                            this.$Modal.success({
                                title: '温馨提示',
                                content: "保存成功"
                            })
                            this.getJobUserList()
                            this.formValidate.id = data.id
                            this.currJobUserId = data.id
                        } else {
                            if("01" === data.errCode){
                                this.warnMsg('该用户在【' + this.formValidate.orgSname + '】中已分配岗位【' + data.jobName + '】')
                            } else {
                                 this.warnMsg(data.msg)
                            }
                           
                        }
                    })
                } else {
                    this.$Modal.error({
                        title: '错误提示',
                        content: '表单验证不通过'
                    })
                }
            })
        },
        handleAdd() {
            this.$refs['baseForm'].resetFields()
            this.formValidate = {
                jobId: '',
                jobName: '',
                orgId: '',
                validType: '01',
                userId: this.userId,
                isDefault:'0',
                orgCode: this.user.orgCode,
                orgName: this.user.orgName,
                orgSname: ''
            }
            this.$refs['org'].$refs['input'].handleClear()
            this.currJobUserId = ''
            this.userRoles = []
            this.selectedUnSubRoleList = []
            this.selectedUnSubPublicList = []
            this.roleList = []
            this.roleIdArr = []
            this.rolePublicIdArr = []
            this.index = this.userJobList.length + 1
            this.operName = '新增'
        },
        openJobDialog() {
            this.curMark = 'bsp:app:job:dialog'
            this.showDialog = true
        },
        handleRole(){
            if (!this.formValidate.userId || !this.formValidate.orgId) {
                this.$Modal.error({
                    title:"错误提示",
                    content:"请先选择用户和岗位后再分配角色"
                })
                return;
            }
            let param = {
                userId:this.formValidate.userId,
                orgId:this.formValidate.orgId,
            }
            this.choiceRoleInfo = param
            this.cascadeComp = false
            this.$nextTick(()=>{
                this.cascadeComp = true
            })
        },
        removeRole(event, name){
            for (var x = 0; x < this.selectedUnSubRoleList.length; x++) {
                let temp = this.selectedUnSubRoleList[x]
                if (name === temp.id) {
                    this.selectedUnSubRoleList.splice(x,1)
                    this.roleIdArr.splice(x,1)
                    return;
                }
            }
        },
        removePublic(event, name){
            for (var x = 0; x < this.selectedUnSubPublicList.length; x++) {
                let temp = this.selectedUnSubPublicList[x]
                if (name === temp.id) {
                    this.selectedUnSubPublicList.splice(x,1)
                    this.rolePublicIdArr.splice(x,1)
                    return;
                }
            }
        },
        role_cancel(){
            this.cascadeComp = false
        },
        choice_confirm(data){
            this.selectedUnSubRoleList = data.selectedRoleList
            if (data.selectedRoleList.length > 0) {
                data.selectedRoleList.forEach(item => {
                    this.roleIdArr.push(item.id)
                })
            } else {
                this.roleIdArr = []
            }
            this.selectedUnSubPublicList = data.selectedPublicList
            if (data.selectedPublicList.length > 0) {
                data.selectedPublicList.forEach(item => {
                    this.rolePublicIdArr.push(item.id)
                })
            } else {
                this.rolePublicIdArr = []
            }
            this.role_cancel()
        },
        job_cancel(data){
            this.$emit("job_cancel",data)
        },
        onOrgSelect(data) {
            if (data[0].orgName === this.userOrgName) {
                this.warnMsg("岗位单位不能与所在单位一致")
                this.$refs['org'].$refs['input'].handleClear()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.bsp-job-assign {
    padding: 10px;
}

/deep/ .bsp-job-assign {
    font-size: 16px !important;
}

.bsp-job-assign .flow-modal-title {
    height: 40px;
    background: #2b5fda;
    width: 100%;
    text-indent: 1em;
    color: #fff;
    line-height: 40px;
}

.bsp-job-assign .page-box {
    line-height: 40px;
    height: 40px;
    text-align: right;
    padding: auto 15px;
    padding: 0 15px;
    border-top: 1px solid #CEE0F0;
    font-size: 15px !important;
}

.bsp-job-assign .img-box {
    margin: 7px 0 0px 0;
    cursor: pointer;
}

.bsp-job-assign .assign-table {
    border: none;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed;
}

.bsp-job-assign .assign-table td {
    font-size: 16px;
    border: none;
}

.bsp-job-assign .assign-table tr:hover {
    background: #F0F5FF;
}

.bsp-job-assign .assign-table .active {
    background: #F0F5FF !important;
    color: #2b5fd9 !important;
}

.bsp-job-assign .assign-table .bc {
    background: #f8f8f9;
}

.bsp-job-assign .td-box {
    padding: 8px 0 8px 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.bsp-job-assign .job-del {
    padding: 0 0 0 12px;
}

.bsp-job-assign .bsp-scroll {
    overflow: overlay;
}

.bsp-job-assign .role-add {
    background-color: #fff;
    border: 1px dotted #cee0f0;
    cursor: pointer;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAR0lEQVQ4T2P8//8/MwMVAOPQM0gn4fYfkM+vLFBlwRcCBL02ahADShjBwoPYZIUcAbQxCJtLRmMNNdZoGkbEJgWCeY3uBgEANkxmEzTRcVYAAAAASUVORK5CYII=') no-repeat 4px center;
    height: 28px;
    line-height: 28px;
    outline: none;
    color: #2B5FDA;
    padding: 0px 8px 0px 24px;
    margin: 2px 4px 2px 0;
    border-radius: 3px;
    vertical-align: middle;
    font-size: 15px;
}

.bsp-job-assign /deep/ .job-role .ivu-form-item-content {
    background: #F5F6FA;
    padding: 5px;
}

.bsp-job-assign /deep/ .job-role .ivu-tag,
.bsp-job-assign /deep/ .assign-role .ivu-tag {
    height: 28px;
    line-height: 28px;
    font-size: 15px;
    background: #fff;
    border-color: #CEE0F0;
}

.bsp-job-assign /deep/ .assign-role .ivu-form-item-content {
    background: #FFF;
    padding: 5px;
    border: 1px solid #CEE0F0;
}

/deep/ .bsp-job-assign .ivu-modal-content .ivu-modal-body,
/deep/ .bsp-job-assign .ivu-modal-header {
    padding: 0px
}

/**重置对话框 */

.bsp-job-assign .main-button {
    opacity: 1;
    background: #2b5fd9;
    font-size: 16px;
    border-radius: 2px;
    padding: 0;
    height: 30px;
    width: 76px;
}

.bsp-job-assign .cancel-btn {
    opacity: 1;
    font-size: 16px;
    margin-right: 10px;
    border-radius: 2px;
    padding: 0;
    height: 30px;
    width: 76px;
    border-color: #2B5FD9;
    color: #2B5FD9;
}

.bsp-job-assign .tit-box {
    background: #F2F6FC;
    line-height: 40px;
    padding: 0 15px;
    font-weight: bold;
}

.bsp-scroll::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 10px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 10px;

}

.bsp-scroll::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 3px;
    background: #b7c7dd;

}

.bsp-scroll::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    border-radius: 3px;
    background: #EDEDED;
}

</style>

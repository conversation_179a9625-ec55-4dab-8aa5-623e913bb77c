<template>
    <div class="content-defaulet">

        <div class="content-defaulet-main">
            <rs-DataGrid ref="grid" funcMark="ylyysh" :customFunc="true" v-if="showFormCompnent">
                <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index }">
                    <Button style="margin-right: 5px;" type="primary"  v-if="hasPermission('ylyysh:approved') && row.process_method === '0' "
                        @click="changeAction('mz1', row)">医生审核</Button>
                </template>
            </rs-DataGrid>
        </div>

        <div v-if="!showFormCompnent">
            <component v-bind:is='component' @on_show_table="on_show_table" modalTitle="医生审核" :cfId="cfId">
            </component>
        </div>

        <div v-if="showDetailModal">
            <Modal v-model="showDetailModal" :title="showDetailModalTitle" :mask-closable="false" :closable="true"
                width="1000">
                <rs-DataGrid ref="grids" :params="xqParams" funcMark="delivery:detail:list"></rs-DataGrid>
            </Modal>
        </div>
    </div>
</template>

<script>

import { mapActions } from 'vuex'
import { sDataGrid } from 'sd-data-grid'
import { userSelector } from 'sd-user-selector'
import approved from './approved.vue'
import { formatDateparseTime } from '@/libs/util'

export default {
  components: {
    sDataGrid,
    userSelector,
    approved
  },
  data () {
    return {
      modalTitle: '',
      modalShow: false,
      type: null,
      formData: {
        diseaseSymptom: '',
        userId: '',
        userName: '',
        id: ''
      },
      cfId: '',
      ryId: '',
      showFormCompnent: true,
      component: '',
      ruleValidate: {
        userId: [
          { required: true, message: '必填', trigger: 'change,blur' }
        ],
        remark: [
          { required: true, message: '必填', trigger: 'change,blur' }
        ]
      },
      xqParams: {
        prescribeId: '',
        startTime: ''
      },
      showDetailModal: false,
      showDetailModalTitle: ''
    }
  },

  mounted () {

  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    changeAction (type, row) {
      this.showFormCompnent = false
      this.cfId = row.id
      this.component = 'approved'
    },
    showDetailModalMethod (row, type) {
      this.showDetailModalTitle = '送药记录'
      this.xqParams = {
        prescribeId: '',
        startTime: ''
      }
      if (type == 1) {
        this.xqParams.prescribeId = row.id
      } else {
        this.xqParams.prescribeId = row.id
        this.xqParams.startTime = formatDateparseTime(new Date(), '{y}-{m}-{d}')
      }
      this.showDetailModal = true
    },
    on_show_table () {
      this.showFormCompnent = true
    },
    on_refresh_table () {
      this.$refs.grid.query_grid_data(1)
    }
  }
}
</script>

<style lang="less" scoped></style>

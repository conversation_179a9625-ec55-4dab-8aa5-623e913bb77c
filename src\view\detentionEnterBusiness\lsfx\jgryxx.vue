<template>
  <div class="jgryxx-class">
	<!-- {{ jgrybm }}
	{{ formData }} -->
	<div class="personInfo">
		<img :src="formData.frontPhoto" style="width: 118px; height: 146px;" alt="">
		<div class="jgryxm"><p>{{ formData.xm }}</p><img v-if="formData.xb == '1'" src="@/assets/images/wpgl/xingbienan.svg" alt=""><img v-else src="@/assets/images/wpgl/xingbienv.svg" alt=""></div>
		<p style="color: #000;">{{ formData.mzName ? formData.mzName : '-' }} &nbsp;&nbsp;|&nbsp;&nbsp; {{ formData.hjd }}</p>
		<div class="jgryzj"><img src="@/assets/images/wpgl/weibaorenyuanzhengjian.svg" alt="">{{formData.zjhm}}</div>
	</div>
	<div class="fenge"></div>
	<div class="roomInfo">
		<div class="roomName">
			<img src="@/assets/images/wpgl/icon_area.png" alt="" >
			<p>{{ formData.roomName }}</p>
		</div>
		<!-- <record :formData="formData" /> -->
		<Timeline v-if="formData && formData.rssj">
            <TimelineItem color="blue">
              <p style="font-size: 16px; color: #000"><span style="font-weight: 600;">入所时间：</span>{{ formData.rssj }}</p>
            </TimelineItem>
			<TimelineItem color="orange">
				<p style="font-size: 16px; color: #000"><span style="font-weight: 600;">关押期限：</span>{{ formData.gyqx }}</p>
            </TimelineItem>
        </Timeline>
	</div>
	<div class="fenge"></div>
	<div class="ajxx-class">
		<p><span>涉嫌罪名：</span>{{ formData.sxzm ? formData.sxzm : '-' }}</p>
		<p><span>入所原因：</span>{{ formData.rsyy ? formData.rsyy : '-' }}</p>
		<p><span>押送单位：</span>{{ formData.gzdw ? formData.gzdw : '-' }}</p>
		<p><span>押送人：</span>{{ formData.syr ? formData.syr : '-' }}</p>
	</div>
	<Button type="default" style="width: 100%;border-color: #57a3f3;color: #57a3f3;" @click="viewClick">查看更多</Button>

	<Modal v-model="openModalRyxx" :mask-closable="false" :closable="true" class-name="select-use-modal" width="800" title="人员基本信息">
		<div class="select-use">
			<ryxx v-if="openModalRyxx"  :jgrybm="jgrybm"  />
		</div>
		<div slot="footer">
				<!-- <Button  type="primary" @click="useSelect" class="save">确 定</Button>
				<Button @click="openModal=false" class="save">关 闭</Button> -->
		</div>
	</Modal>
  </div>
</template>

<script>
import ryxx from '@/components/ryxx/index.vue'
import { mapActions } from 'vuex'
export default {
	components: {
		ryxx
	},
	props: {
		jgrybm: {
			type: {
				type: String,
				default: ''
			}
		},
		// formData: {
		// 	type: Object,
		// 	default: {}
		// }
	},
	data() {
		return {
			openModalRyxx: false,
			jgrybm: '',
			formData: {}
		}
	},
	watch: {
		// formData: {
		// 	handler(value) {
		// 		if(value) {
		// 			console.log(value,'formData');
		// 			// this.jgrybm = value.jgrybm
		// 		}
		// 	}
		// },
		jgrybm: {
			handler(value) {
				if(value) {
					this.jgrybm = value
					this.getUserInfo(value)
				}
			}
		},
		immediate: true,
      	deep: true
	},
	methods: {
		...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
		viewClick() {
			this.openModalRyxx = true
		},
		getUserInfo (jgrybm) {
			console.log(jgrybm,'11111111111');
			let params = {
				jgrybm: jgrybm,
				ryzt: 'ZS'
			}
			this.$store.dispatch('authGetRequest', {
				url: this.$path.app_getPrisonerSelectCompomenOne,
				// url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
				params: params
			}).then(resp => {
				if (resp.code == 0) {
				this.formData = resp.data
				} else {
				this.$Notice.error({
					title: '错误提示',
					desc: resp.msg
				})
				}
			})
		},
	},
	created() {
		console.log(this.jgrybm,'this.jgrybm');
		if(this.jgrybm){
			this.getUserInfo(this.jgrybm)
		}
	}
}
</script>

<style lang="less" scoped>
.jgryxx-class{
	width: 100%;
	height: 100%;
	padding: 25px 20px;
	.personInfo{
		width: 100%;
		height: auto;
		// text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		// padding-top: 25px;
		// padding-bottom: 25px;
		.jgryxm{
			width: 100%;
			line-height: 35px;
			display: flex;
			justify-content: center;
			// align-items: center;
			p{
				font-size: 18px;
				font-weight: 600;
				color: #000;
				// margin-right: 10px;
			}
			img{
				width: 20px;
				height: 20px;
			}
		}
		.jgryzj{
			width: 100%;
			line-height: 35px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #000;
			img{
				width: 30px;
				height: 30px;
				margin-right: 12px;
			}
		}
	}
	.fenge{
		position: relative;
		margin: 25px 0px;
		&:before {
			content: "";
			// display: inline-block;
			position: absolute;
			left: 0;
			top: 5px;
			width: 100%;
			height: 1px;
			background: #cee0f0;
			border-radius: 50%;
		}
	}
	.roomInfo{
		width: 100%;
		height: auto;
		padding: 20px 10px;
		.roomName{
			width: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			// margin-bottom: 20px;
			img {
				width: 40px;
				height: 40px;
				margin-right: 10px;
			}
			p {
				font-size: 16px;
				font-weight: 600;
				color: #000;
			}
		}
	}
	.ajxx-class{
		width: 100%;
		height: auto;
		padding: 20px 10px;
		margin-bottom: 10px;
		p{
			font-size: 16px;
			color: #000;
			line-height: 30px;
			span{
				display: inline-block;
				font-weight: 600;
				width: 80px;
				text-align: justify;
				text-align-last: justify;
				// letter-spacing: 1px;
			}
		}
	}
}
/deep/.ivu-timeline-item-content{
	margin-top: 15px;
}
/deep/.ivu-btn:hover{
	background-color: #ebf4fd;
}
</style>
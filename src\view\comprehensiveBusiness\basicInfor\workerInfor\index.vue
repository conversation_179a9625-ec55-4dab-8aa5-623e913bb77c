<template>
    <div>
        <s-DataGrid ref="grid" funcMark="gzryxx-mjxxgl" :customFunc="true" v-if="!showAdd">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':mjxxgl:add')" @click.native="handleAdd"
                    icon="md-add">新增人员</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
                <Button type="primary" size="small" v-if="func.includes(globalAppCode + ':mjxxgl:xg')"
                    class="row-button" @click="handleEdit(row)">编辑 </Button>
                <Button type="info" size="small"
                    v-if="func.includes(globalAppCode + ':mjxxgl:fpjs') && row.rylx != '04'" class="row-button"
                    style="margin-left: 5px" @click="handleSetRole(row)">分配角色 </Button>
                <Button type="success" size="small"
                    v-if="func.includes(globalAppCode + ':mjxxgl:fpgw') && row.rylx != '04'" class="row-button"
                    style="margin-left: 5px" @click="handleSetJob(row)">分配岗位 </Button>
                <Button type="info" size="small"
                    v-if="func.includes(globalAppCode + ':mjxxgl:fpgw') && row.rylx != '04' && !row.shid" class="row-button"
                    style="margin-left: 5px" @click="handleSetCard(row)">绑定工牌</Button>
                <Button type="info" size="small" ghost
                    v-if="func.includes(globalAppCode + ':mjxxgl:fpgw') && row.rylx != '04' && row.shid" class="row-button"
                    style="margin-left: 5px" @click="handleTermCard(row)">工牌解绑</Button>
                <Button type="error" size="small" v-if="func.includes(globalAppCode + ':mjxxgl:sc') && row.rylx == '04'"
                    class="row-button" style="margin-left: 5px" @click="handleDelete(row)">删除</Button>
            </template>
        </s-DataGrid>
        <div v-if="showAdd" style="height: 100%">
            <component :is="component" @on_show_table="on_show_table" :formValidate="formData" :formDate="formData"
                :action="action" />
        </div>

        <role-assign v-if="showRoleAuth" :return-val="'2'" :user-id="userId" :org-id="orgId" :app-id="appId"
            @on_cancel="on_cancel"></role-assign>

        <s-UserJob v-if="userJobOpenStatus" :user="currUser" v-model="userJobOpenStatus" @job_cancel="job_cancel" />
        <Modal v-model="bindModal" width="30%" title="绑定工牌">
            <Form ref="formData" :model="formData" :label-width="130" :label-colon=true>
                <FormItem label="人员姓名" prop="bindPersonName">
                    <div class="ivu-form-item-label">{{ formData.bindPersonName }}</div>
                </FormItem>
                <FormItem label="工牌ID" prop="tagId"
                    :rules="[{ trigger: 'blur,change', message: '工牌ID为必填', required: true }]">
                    <Input type="text" v-model="formData.tagId" placeholder="请填写" style="width: 200px;" />
                </FormItem>

            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSubmit" class="save">确 定</Button>
                <Button @click="handleClose" class="save">关 闭</Button>
            </div>
        </Modal>
        <Modal v-model="unbindModal" width="30%" title="工牌解绑">
            <Form ref="formDataUn" :model="formDataUn" :label-width="130" :label-colon=true>
                <FormItem label="人员姓名" prop="bindPersonName">
                    <div class="ivu-form-item-label">{{ formData.bindPersonName }}</div>
                </FormItem>
                <FormItem label="工牌ID" prop="tagId">
                    <div class="ivu-form-item-label">{{ formDataUn.tagId }}</div>
                </FormItem>
                <FormItem label="解绑原因" prop="unbindReason"
                    :rules="[{ trigger: 'blur,change', message: '解绑原因必填', required: true }]">
                    <Input type="textarea" v-model="formDataUn.unbindReason" placeholder="请填写"
                        :autosize="{ minRows: 2, maxRows: 5 }" />
                </FormItem>

            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSubmitUn" class="save">确 定</Button>
                <Button @click="handleClose" class="save">关 闭</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import addPerson from './addPerson.vue';
import editPerson from './editPerson.vue';
import sUserJob from './user-job'
import roleAssign from '@/components/role-assign/role-assign.vue'
export default {
    components: {
        addPerson,
        editPerson,
        sUserJob,
        roleAssign
    },

    data() {
        return {
            showAdd: false,
            component: null,
            presentNo: null,
            rowData: null,
            formData: {},
            action: 'add',
            showRoleAuth: false,
            userJobOpenStatus: false,
            userId: '',
            orgId: '',
            currUser: {},
            appId: this.$route.query.appId ? this.$route.query.appId : 'admin',
            bindModal: false,
            unbindModal: false,
            formData: {
                bindPersonName: '',
                tagId: '',
                bindTime: ''

            },
            formDataUn: {
                bindPersonId: '',
                tagId: '',
                unbindReason: '',
                unbindTime: ''
            },
        }
    },
    methods: {
        on_show_table() {
            this.showAdd = false

            // this.on_refresh_table()
        },
        handleSetRole(row) {
            this.formData = row
            this.userId = row.id
            this.orgId = row.org_id
            this.showRoleAuth = false
            this.$nextTick(() => {
                this.showRoleAuth = true
            })
        },
        on_cancel() {
            this.showRoleAuth = false
        },
        handleAdd() {
            this.showAdd = true
            this.action = 'add'
            this.component = editPerson
        },
        handleEdit(row) {
            this.showAdd = true
            this.component = editPerson
            this.action = 'edit'
            if (row.loginId === 'admin') row.validType = '1'
            this.formData = row

            // if (row.rylx == '04') {
            //     this.component = addPerson
            //     this.action = 'edit'             
            //      this.formData = row
            //      console.log(row,this.formData);
            // }else {
            //     this.component = editPerson
            //      this.formData = row
            // }


        },
        handleSetJob(row) {
            // let data = this.$tools.objToCamel(row)
            this.currUser = row
            this.userJobOpenStatus = true
        },
        job_cancel(data) {
            this.userJobOpenStatus = false
            if (data) {
                this.$refs.grid.refreshGrid(1)
            }
        },
        handleDelete(row) {
            // 删除
            let _this = this
            this.$Modal.confirm({
                title: '是否确认删除',
                loading: true,
                onOk: async () => {
                    _this.submitDel(row.id)
                }
            })
        },
        submitDel(id) {
            let _this = this
            this.$store.dispatch('authGetRequest', { url: this.$path.pm_deleteGq, params: { ids: id } }).then(data => {
                if (data.success) {
                    this.$Notice.success({
                        title: '成功提示',
                        desc: '删除成功'
                    })
                    this.$refs.grid.query_grid_data()
                    _this.$Modal.remove()
                } else {
                    this.$Notice.error({
                        title: '错误提示',
                        desc: data.msg,

                    })
                    _this.$Modal.remove()
                }
            })
        },

        handleSetCard(row) {
            this.formData.bindPersonId = row.id
            this.formData.bindPersonName = row.name
            this.bindModal = true
        },
        handleTermCard(row) {
            this.formData.bindPersonName = row.name
            this.formDataUn.bindPersonId = row.id
            this.formDataUn.tagId = row.shid
            this.unbindModal = true
        },
        handleClose() {
            this.bindModal = false
            this.unbindModal = false
            this.$refs.formData.resetFields()
            this.$refs.formDataUn.resetFields()
        },
        handleSubmit() {
            this.formData.bindTime = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
            this.$refs.formData.validate(valid => {
                if (valid) {
                    let params = { ...this.formData }
                    this.$store.dispatch('authPostRequest', { url: this.$path.pm_mjCreate, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('绑定成功')
                            this.handleClose()
                            this.$refs.grid.query_grid_data()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        },
        handleSubmitUn() {
            this.formDataUn.unbindTime = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
            this.$refs.formDataUn.validate(valid => {
                if (valid) {
                    let params = { ...this.formDataUn }
                    this.$store.dispatch('authPostRequest', { url: this.$path.pm_znwdUnbind, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('解绑定成功')
                            this.handleClose()
                             this.$refs.grid.query_grid_data()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        },
    },

}


</script>

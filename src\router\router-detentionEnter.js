let menuMode = localStorage.getItem('menuMode')
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'

export default [
  {
    path: "/detentionBusiness",
    name: "detentionBusiness",
    meta: {
      title: "羁押业务",
    },
    component: menuMode=='side'?mainNew:main,
    children: [
      {
        path: "detentionEnterRegister",
        name: "detentionEnterRegister",
        meta: {
          title: "收押入所",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/detentionEnterManage/registrationManage/index.vue"),
      },
      {
        path: "lsfx",
        name: "lsfx",
        meta: {
          title: "留所服刑",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/lsfx/index.vue"),
      },
      {
        path: "detentionEnterRecord",
        name: "detentionEnterRecord",
        meta: {
          title: "收押台账",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/detentionEnterManage/recordManage/index.vue"),
      },
      {
        path: "rearrestRegister",
        name: "rearrestRegister",
        meta: {
          title: "收回登记",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/rearrestManage/registrationManage/index.vue"),
      },
      {
        path: "releaseRegister",
        name: "releaseRegister",
        meta: {
          title: "释放出所登记",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/releaseManage/registrationManage/release.vue"),
      },
      {
        path: "releaseRecord",
        name: "releaseRecord",
        meta: {
          title: "释放出所登记台账",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/releaseManage/recordManage/index.vue"),
      },
      {
        path: "transferRegister",
        name: "transferRegister",
        meta: {
          title: "转所登记",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/transferManage/registrationManage/transfer.vue"),
      },
      {
        path: "transferRecord",
        name: "transferRecord",
        meta: {
          title: "转所登记台账",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/transferManage/recordManage/index.vue"),
      },
      {
        path: "detainEnterRegister",
        name: "detainEnterRegister",
        meta: {
          title: "收拘登记",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detainEnterBusiness/detainEnterManage/registrationManage/index.vue"),
      },
      {
        path: "detainEnterRecord",
        name: "detainEnterRecord",
        meta: {
          title: "收拘台账",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detainEnterBusiness/detainEnterManage/recordManage/index.vue"),
      },
      {
        path: "outForTreatmentRegister",
        name: "outForTreatmentRegister",
        meta: {
          title: "出所就医登记",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/temporaryOutingBusiness/outForTreatmentManage/registrationManage/index.vue"),
      },
      {
        path: "outForTreatmentRecord",
        name: "outForTreatmentRecord",
        meta: {
          title: "出所就医台账",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/temporaryOutingBusiness/outForTreatmentManage/recordManage/index.vue"),
      },
      {
        path: "rehabilitationEnterRegister",
        name: "rehabilitationEnterRegister",
        meta: {
          title: "收戒入所",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/rehabilitationEnterBusiness/rehabilitationEnterManage/registrationManage/index.vue"),
      },
      {
        path: "treatmentEnterRegister",
        name: "treatmentEnterRegister",
        meta: {
          title: "收治入所",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/treatmentEnterBusiness/treatmentEnterManage/registrationManage/index.vue"),
      },
      {
				path: "stopDetention",
				name: "stopDetention",
				meta: {
					title: "建议停止执行拘留",
				},
				redirect: "/stopDetention/whenDetained",
				component: () => import("@/view/detentionEnterBusiness/stopDetentionManage/index.vue"),
				children: [
					{
						path: "whenDetained",
						name: "whenDetained",
						meta: {
							title: "收拘时",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/detentionEnterBusiness/stopDetentionManage/whenDetained/index.vue"),
					},
					{
						path: "afterDetention",
						name: "afterDetention",
						meta: {
							title: "收拘后",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/detentionEnterBusiness/stopDetentionManage/afterDetention/index.vue"),
					}
				]
			},
      {
        path: "inforChange",
        name: "inforChange",
        meta: {
          title: "人员信息维护",
        },
        sider: true,
        bread: true,
        component: () => import("@/view/detentionEnterBusiness/inforChange/index.vue"),
      },
    ]
  },
  // {
  //   path: "/healthCheck",
  //   name: "healthCheck",
  //   meta: {
  //     title: "入所健康检查",
  //   },
  //   component: main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "入所健康检查登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/healthCheckManage/registrationManage/index.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "入所健康检查台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/healthCheckManage/recordManage/index.vue"),
  //     },
  //   ]
  // },
  // {
  //   path: "/belongings",
  //   name: "belongings",
  //   meta: {
  //     title: "随身物品登记",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "随身物品登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/belongingsManage/registrationManage/index.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "随身物品登记台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/belongingsManage/recordManage/index.vue"),
  //     },
  //   ]
  // },
  // {
  //   path: "/biologicalInfoGather",
  //   name: "biologicalInfoGather",
  //   meta: {
  //     title: "生物信息采集",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "生物信息采集登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/biologicalInfoManage/registrationManage/index.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "生物信息采集登记台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/biologicalInfoManage/recordManage/index.vue"),
  //     },
  //   ]
  // },
  // {
  //   path: "/leadershipApproval",
  //   name: "leadershipApproval",
  //   meta: {
  //     title: "领导审批",
  //   },
  //   component:menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "approval",
  //       name: "approval",
  //       meta: {
  //         title: "审批",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/leadershipApprovalManage/registrationManage/index.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/leadershipApprovalManage/recordManage/record.vue"),
  //     },
  //   ]
  // },
  // {
  //   path: "/detentionEnter",
  //   name: "detentionEnter",
  //   meta: {
  //     title: "收回登记",
  //   },
  //   component: main,
  //   children: [
  //     {
  //       path: "rearrest",
  //       name: "rearrest",
  //       meta: {
  //         title: "收回登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/rearrestManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/release",
  //   name: "release",
  //   meta: {
  //     title: "释放出所",
  //   },
  //   component: main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "释放出所登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseManage/registrationManage/release.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "释放出所登记台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseManage/recordManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/releaseCheck",
  //   name: "releaseCheck",
  //   meta: {
  //     title: "出所检查",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "出所检查登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseCheckManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/financialHandover",
  //   name: "financialHandover",
  //   meta: {
  //     title: "财务交接",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "财务交接登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/financialHandoverManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/releaseApproval",
  //   name: "releaseApproval",
  //   meta: {
  //     title: "释放出所领导审批",
  //   },
  //   component:menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "approval",
  //       name: "approval",
  //       meta: {
  //         title: "领导审批",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseApprovalManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/releaseVerify",
  //   name: "releaseVerify",
  //   meta: {
  //     title: "防误放验证",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "防误放验证登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseVerifyManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/releaseConfirm",
  //   name: "releaseConfirm",
  //   meta: {
  //     title: "离所确认",
  //   },
  //   component: menuMode=='side'?mainNew:main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "出所确认",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/releaseConfirmManage/registrationManage/index.vue"),
  //     }
  //   ]
  // },
  // {
  //   path: "/transfer",
  //   name: "transfer",
  //   meta: {
  //     title: "转所登记",
  //   },
  //   component: main,
  //   children: [
  //     {
  //       path: "registration",
  //       name: "registration",
  //       meta: {
  //         title: "转所登记",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/transferManage/registrationManage/transfer.vue"),
  //     },
  //     {
  //       path: "record",
  //       name: "record",
  //       meta: {
  //         title: "转所登记台账",
  //       },
  //       sider: true,
  //       bread: true,
  //       component: () => import("@/view/detentionEnterBusiness/transferManage/recordManage/index.vue"),
  //     }
  //   ]
  // },
]

<!-- 新增类型 -->
<template>
    <div>
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="120">
            <FormItem label="指标分类名称" prop="name">
                <Input v-model="formValidate.name" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="初始分值" prop="name">
                <Input v-model="formValidate.name" type="number" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="考评人" prop="name">
                <user-selector v-model="formValidate.mediationPoliceSfzh" tit="考评人选择" @onCancel="onCancel"
                    @onClear="onClear" @onSelect="onSelect" :text.sync="formValidate.mediationPoliceXm"
                    returnField="idCard" numExp='num>=1' msg="至少选中1人">
                </user-selector>
            </FormItem>
            <FormItem label="考评对象" prop="name">
                <user-selector v-model="formValidate.mediationPoliceSfzh" tit="考评对象选择" @onCancel="onCancel"
                    @onClear="onClear" @onSelect="onSelect" :text.sync="formValidate.mediationPoliceXm"
                    returnField="idCard" numExp='num>=1' msg="至少选中1人">
                </user-selector>
            </FormItem>
        </Form>
    </div>
</template>
<script>
import { userSelector } from 'sd-user-selector'
export default {
    components: { userSelector },
    data() {
        return {
            formValidate: {},
            ruleValidate: {
                name: [
                    { required: true, message: '指标分类名称不能为空', trigger: 'blur,change' }
                ],
            }
        }
    },
    methods: {
        onSelect() { },
        onCancel() { },
        onClear() { },
    }
}
</script>
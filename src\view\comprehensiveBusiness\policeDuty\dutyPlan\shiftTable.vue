<template>
    <div class="shift-table-container" ref="table">
        <div class="shift-header-box">
            <div class="header-title">
                <span class="date">日期</span>
                <span class="post">值班岗位</span>
            </div>
            <div class="header-table">
                <template v-for="(post, idx1) in renderHeader">
                    <div :key="'post' + idx1" class="post" :style="getSpan(post.row, post.col)">{{ post.post }}</div>
                    <template v-for="(subPost, idx2) in post.subPostList">
                        <div :key="'post' + idx1 + idx2" :style="getSpan(subPost.row)" v-if="post.col !== 2">{{
                            subPost.subPost }}</div>
                        <template v-for="time in subPost.timeList">
                            <div :key="time.postKey">
                                <p v-if="time.dutyShift">{{ time.dutyShift }}:</p>
                                <p>{{ time.time }}</p>
                            </div>
                        </template>
                    </template>
                </template>
            </div>
        </div>
        <div class="shift-content-box" :style="getGrid()" v-clickOutside="closeTip">
            <Icon class="shift-tool tool-left" type="ios-arrow-dropleft" v-show="showTool.prev" @click="handleChange(-1)"/>
            <!-- <div class="shift-tool tool-left" v-show="showTool.prev" @click="handleChange(-1)"></div>
            <div class="shift-tool tool-right" v-show="showTool.next" @click="handleChange(1)"></div> -->
            <Icon class="shift-tool tool-right" type="ios-arrow-dropright" v-show="showTool.next" @click="handleChange(1)" />
            <template v-for="(sortObj, idx1) in renderSort">
                <template v-for="(personObj, idx2) in renderPerson">
                    <div class="shift-date" :key="personObj.date + sortObj.postKey" v-if="idx1 === 0" @click="closeTip">
                        <p>{{ getDate(personObj.date, 'year') }}</p>
                        <p>{{ getDate(personObj.date, 'day') }}</p>
                    </div>
                    <shift-person ref="targetElement" :default-key="personObj.date + sortObj.postKey"
                        :click-key="clickKey" class="shift-content" :key="personObj.date + sortObj.postKey"
                        :list="personObj[sortObj.postKey] || []" :count="renderNum"
                        :is-left="idx2 < renderPerson.length / 2" @on-select="selectPerson(personObj, sortObj)"
                        @on-click="showTip" v-else></shift-person>
                </template>
            </template>
        </div>
        <user-selector v-show="false" v-model="formValidate.idCard" :text.sync="formValidate.userName" ref='userSelect'
            @onSelect="onSelect" :bindEvent='false' tit="用户选择" returnField="idCard" msg="至少选中1人" @onCancel="onCancel">
        </user-selector>
    </div>
</template>

<script>
// import { clickOutside } from "material";
import shiftPerson from "./shiftPerson";
import { userSelector } from 'sd-user-selector'
// import personModal from "./personModal";
import dayjs from 'dayjs';
export default {
    name: "shiftTable",
    components: { shiftPerson, userSelector },
    //   directives: {clickOutside},
    data() {
        return {
            renderHeader: [],
            renderPerson: [],
            renderSort: ["datePlace"],
            curNum: 1,
            allNum: 1,
            showTool: {
                prev: false,
                next: false,
            },
            renderNum: Number.MAX_VALUE,
            renderWidth: 67,
            selectObj: {},
            clickKey: "",
            tableWidth: 0,
            formValidate: {
                idCard: '',
                userName: ''
            }

        };
    },
    props: {
        type: {
            type: String,
            default: "view",
            validator: (val) => ["edit", "view"].includes(val),
        },
        personList: {
            type: Array,
            default: () => [],
        },
        headerList: {
            type: Array,
            default: () => [],
        },
        isEdit: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        headerList: {
            handler() {
                this.initHeader();
            },
            immediate: true,
        },
        personList: {
            handler(list) {
                if (!list.length) return;
                this.initPerson();
            },
            immediate: true,
        }
    },
    activated() {
        this.tableWidth = this.$refs.table.getBoundingClientRect().width;
    },
    mounted() {
        this.tableWidth = this.$refs.table.getBoundingClientRect().width;
    },
    methods: {
        closeTip() {
            this.clickKey = "";
        },
        showTip(key) {
            this.clickKey = key;
        },
        initHeader() {
            this.renderSort = [{ postKey: "datePlace" }];
            let renderList = [];
            let postList = this.headerList;
            postList.forEach(post => {
                let subPostList = post.subPostList || [];
                let postObj = {
                    ...post,
                    col: post.hasSubPost ? 1 : 2,
                    row: 0,
                    subPostList: [],
                };
                subPostList.forEach(subPost => {
                    let timeList = subPost.subPostTimeVOS || [];
                    let subPostObj = {
                        ...subPost,
                        row: timeList.length,
                        timeList: [],
                    };
                    timeList.forEach(time => {
                        this.renderSort.push({ postKey: time.postKey, dutyPostId: subPostObj.dutyPostId });
                        subPostObj.timeList.push({ ...time, text: time.dutyShift ? `${time.dutyShift}：${time.time}` : `${time.time}` });
                    });
                    postObj.subPostList.push(subPostObj);
                    postObj.row += timeList.length;
                });
                renderList.push(postObj);
            });
            this.renderHeader = renderList;
        },
        initPerson() {
            this.allNum = Math.ceil(this.personList.length / 7);
            this.curNum = 1;
            this.getToolDisplay();
            this.getRenderPerson();
        },
        getRenderPerson() {
            this.renderPerson = this.personList.slice((this.curNum - 1) * 7, this.curNum * 7);
            this.$nextTick(() => {
                this.calRenderWidth();
            });
        },
        calRenderWidth() {
            this.renderWidth = (this.tableWidth - 394) / this.renderPerson.length;
            let boxWidth = this.renderWidth - 4 - 1;
            this.renderNum = Math.floor(boxWidth / 62) * 2;

        },
        getToolDisplay() {
            let flag1 = this.allNum > 1 && this.curNum !== 1;
            let flag2 = this.allNum > 1 && this.curNum !== this.allNum;
            this.showTool.prev = flag1;
            this.showTool.next = flag2;
        },
        getDate(date, type) {
            let time = dayjs(date);
            return type === "year" ? time.year() : time.format("MM-DD");
        },
        getSpan(row, col = 1) {
            return {
                gridRowStart: `span ${row}`,
                gridColumnStart: `span ${col}`,
            };
        },
        getGrid() {
            let col = this.renderPerson.length;
            return { gridTemplateColumns: `repeat(${col}, 1fr)` };
        },
        handleChange(step) {
            let curNum = this.curNum + step;
            this.curNum = curNum < 1 ? 1 : curNum > this.allNum ? this.allNum : curNum;
            this.getToolDisplay();
            this.getRenderPerson();
            this.closeTip();
        },
        selectPerson(personObj, sortObj) {
            if (this.type === "view") return;
            this.$emit("update:isEdit", true);
            this.selectObj = { personObj, sortObj };
            let list = personObj[sortObj.postKey] || [];
            console.log(list, sortObj.dutyPostId,'sssssssssssssssssss');
            this.formValidate.userName = list.map(item => item.policeName).join(',')
            this.formValidate.idCard = list.filter(item => item.policeId != null).map(item => item.policeId).join(',')

            

            this.$refs.userSelect.openDialog()
            // this.$refs.personModal.open(list, sortObj.dutyPostId);
        },
        onSelect(data) {
            console.info(data)
            let { personObj, sortObj } = this.selectObj;
            personObj[sortObj.postKey] = data.map(item => {
                return {
                    policeId: item.idCard,
                    policeName: item.name,
                    policeType: item.isFj,
                }
            });
            this.formValidate = {
                userName: '',
                idCard: '',
            }

        },
        onCancel() {
            this.formValidate = {
                userName: '',
                idCard: '',
            }
        },
        confirmSelect(list) {
            let { personObj, sortObj } = this.selectObj;
            personObj[sortObj.postKey] = list;
        }
    }
};
</script>

<style lang="less" scoped>
.shift-table-container {
    width: 100%;
    border-top: 1px solid #D9E1EA;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    box-sizing: border-box;
    user-select: none;

    .shift-header-box {
        user-select: none;

        .header-title {
            width: 394px;
            height: 74px;
            // background: url("../../assets/images/shiftManagement/table_bg.png");
            position: relative;
            background-color: #40b5ef;

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 141%;
                /* 100% × √2 (对角线长度) */
                height: 1px;
                background: #D9E1EA;
                transform: rotate(10.3deg);
                transform-origin: 0 0;
            }

            span {
                font-size: 16px;
                font-weight: bold;
                color: #FFFFFF;
                line-height: 21px;
                position: absolute;
            }

            .date {
                top: 20px;
                right: 45px;
            }

            .post {
                left: 28px;
                bottom: 20px;
            }
        }

        .header-table {
            display: inline-grid;
            grid-template-columns: 120px 120px 154px;
            grid-auto-rows: 70px;
            place-items: stretch stretch;

            div {
                border-right: 1px solid #D9E1EA;
                border-bottom: 1px solid #D9E1EA;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                font-size: 16px;
                color: #415060;
                text-align: center;
            }

            .post {
                font-weight: bold;
                color: #00244A;
            }
        }
    }

    .shift-content-box {
        user-select: none;
        flex: 1;
        display: inline-grid;
        grid-template-rows: 74px;
        grid-auto-rows: 70px;
        place-items: stretch stretch;
        position: relative;
        background: #F5F7FA;
        ;

        div {
            border-right: 1px solid #D9E1EA;
            border-bottom: 1px solid #D9E1EA;
        }

        .shift-tool {
            position: absolute;
            top: 20px;
            // width: 32px;
            // height: 50px;
            cursor: pointer;
            font-size: 30px;
            color: #40b5ef;

            &.tool-left {
                left: 0;
                //   background: url("../../assets/images/shiftManagement/tool_left.png");
            }

            &.tool-right {
                right: 0;
                //   background: url("../../assets/images/shiftManagement/tool_right.png");
            }
        }

        .shift-date {
            text-align: center;
            padding: 17px 0;
            background: white;

            p {
                font-size: 16px;
                color: #8D99A5;
                line-height: 21px;

                &:nth-child(2) {
                    font-weight: bold;
                    color: #415060;
                }
            }
        }

        .shift-content {
            user-select: none;
        }
    }
}
</style>
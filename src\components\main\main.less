.main{
  .logo-con{
    height: 60px;
    padding: 10px;
    img{
      height: 44px;
      width: auto;
      display: block;
      margin: 0 auto;
    }
  }
  .header-con{
    background: #fff;
    padding: 0 20px;
    width: 100%;
  }
  .main-layout-con{
    height: ~"calc(100vh - 70px)";
    overflow: hidden;
  }
  .main-content-con{
    overflow: hidden;
    background-color: #EAEDF4;
  }
  .tag-nav-wrapper{
    padding: 0;
    height:48px;
    background:#fff;
    border-bottom: 1px solid #F0F0F0;
  }
  .content-wrapper{
    padding: 10px;
    margin:0 20px 20px 0;
    border-radius: 6px;
    height: ~"calc(100% - 35px)";
    overflow: auto;
    position: relative;
    background: #FFFFFF !important;
    box-shadow: 0px 4px 16px 1px rgba(0,0,0,0.16);
  }
  .content-wrapper-app{
    padding: 10px;
    margin:0px;
    height: ~"calc(100% - 80px)";
    overflow: auto;
    position: relative;
    background: #FFFFFF;
    // box-shadow: 0px 4px 16px 1px rgba(0,0,0,0.16);

  }
  .left-sider{
    border-right:1px solid #E6E6E6;
    background: #005af5 !important;
    .ivu-layout-sider-children{
      overflow-y: scroll;
      margin-right: -18px;
      background-image:url('./logo.png');
      background-repeat:no-repeat;
      background-position: bottom;
    }
  }
}
.ivu-menu-item > i{
  margin-right: 12px !important;
}
.ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
  margin-right: 8px !important;
}
.collased-menu-dropdown{
  width: 100%;
  margin: 0;
  line-height: normal;
  padding: 7px 0 6px 16px;
  clear: both;
  font-size: 12px !important;
  white-space: nowrap;
  list-style: none;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
  &:hover{
    background: rgba(100, 100, 100, 0.1);
  }
  & * {
    color: #515a6e;
  }
  .ivu-menu-item > i{
    margin-right: 12px !important;
  }
  .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }
}

.ivu-select-dropdown.ivu-dropdown-transfer{
  max-height: 400px;
}



.panel {
  font-size: 14px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
  .panel-title {
    padding: 0 15px;
    color: #535351;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.2);
    .panel_title_select {
      display: inline-block;
      .el-input__inner {
        height: 28px;
      }
    }
  }
  .panel-body {
      padding: 10px;
      overflow: hidden;
      font-weight: normal;
      min-height: 70vh;
      position: relative;
      .top-toolbar{
          margin-bottom: 6px;
          min-height: 35px;
      .func{
          width:15%;
          display:inline-block;
      }
      .filter{
          width:85%;
          float:right;
          text-align:right;
          >span{
              font-size:14px;
              margin-left:15px;
              color:#8a8a8a;
              font-weight:600;
          }
          .ivu-input-wrapper, .ivu-select{
              width: 200px;
              margin-right: 10px;
          }
          .ivu-select-placeholder{
            text-align: left;
          }
         // display:inline-block;
      }
    }
    .bottom-toolbar {
      margin-top: 0px;
      padding: 15px 0;
    }
    .ivu-btn-icon {
      padding: 2px 15px 3px 8px;
    }
    .ivu-btn-no-icon {
      padding: 2px 15px 3px 15px;
    }
    .ivu-table {
      font-size: 14px;
    }
    .ivu-page-item {
      min-width: 28px;
      height: 27px;
      line-height: 25px;
      margin-right: 3px;
    }
    .ivu-page-prev {
      min-width: 30px;
      height: 27px;
      line-height: 24px;
    }
    .ivu-page-next {
      min-width: 30px;
      height: 27px;
      line-height: 24px;
    }
    .ivu-page-options-elevator input {
      height: 28px;
      line-height: 1.5;
    }
    .ivu-form {
      .ivu-form-item-label{
        font-size: 14px;
      }
    }
    .ivu-card-head {
      padding: 8px 16px;
    }
    .ivu-select-single .ivu-select-input {
      font-size: 14px;
    }
  }
  .ivu-table-small,.ivu-input,.ivu-btn{
    font-size: 14px;
  }
}
.ivu-input-group-append, .ivu-input-group-prepend {
    padding: 0;
}
.ivu-table-cell {
  .ivu-radio-wrapper {
    margin-right: 0px;
  }
}

.wpzxTopContent {
  width: 100%;
}

.wpzxTopContentInput {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.wpzxInputMain {
  width: 25%;
  height: 32px;
  display: flex;
  margin-bottom: 0.16rem;
}

.wpzxInputMainTitle {
  width: 100px;
  color: #333333;
  font-size: 14px;
  text-align: right;
  height: 32px;
  line-height: 32px;
}

.wpzxInputMainText {
  width: 300px;
  height: 32px;
  background-color: #ffffff;
  border: solid 1px #cccccc;
  box-sizing: border-box;
  padding-left: 10px;
  color: #333333;
  font-size: 14px;
}

.wpzxTopContent {
  background-color: #f7fafc;
  border: solid 1px #e6eaf0;
  box-sizing: border-box;
  padding-top: 15px;
  padding-bottom: 15px;
}

.btnOne {
  display: inline-block;
  height: 32px;
  background-color: #f5f8ff;
  border-radius: 2px;
  border: solid 1px #6199fa;
  text-align: center;
  min-width: 96px;
  padding-left: 10px;
  padding-right: 10px;
  line-height: 32px;
  cursor: pointer;
  color: #3179f5;
  font-size: 14px;
  margin-right: 20px;
}

.btnTwo {
  display: inline-block;
  height: 32px;
  background-color: #6199fa;
  border: solid 1px #6199fa;
  border-radius: 2px;
  text-align: center;
  min-width: 96px;
  padding-left: 10px;
  padding-right: 10px;
  line-height: 32px;
  cursor: pointer;
  color: #ffffff;
  font-size: 14px;
  margin-right: 20px;
}

.wpzxTopContentBtn {
  text-align: center;
}

.wpzxBottomContent {
  padding-top: 22px;
}

.wpzxBottomTab {
  display: flex;
  justify-content: space-between;
}
.wpzxBottomTabLeft {
  display: flex;
}
.wpzxBottomTabOne {
  width: 104px;
  height: 32px;
  line-height: 32px;
  padding-left: 8px;
  margin-right: 16px;
  cursor: pointer;
}

.wpzxBottomTabIcon {
  display: inline-block;
  width: 15px;
  height: 17px;
  vertical-align: middle;
  margin-right: 7px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}



.wpzxBottomTabText {
  color: #ffffff;
  font-size: 14px;
  vertical-align: middle
}

.bgBlue {
  background: #3179f5;
}

.bgGreen {
  background: #04cc89;
}

.bgRed {
  background: #f0606c;
}

.bgGray {
  background: #a8acb3;
}


.wpzxBottomTable {
  /* border: solid 1px #dadde5;
    border-bottom: none; */
}

.wpzxBottomPage {
  padding-top: 8px;
  border: solid 1px #dce4eb;
  border-top: none;
  height: 40px;
  display: flex;
  justify-content: space-between;
}

.checkBoxMain {
  cursor: pointer;
}

.wpztText {
  width: 56px;
  height: 24px;
  border-radius: 2px;
  padding: 6px;
  font-size: 14px;
}

.wpztText.ydj {
  background-color: #e3fce3;
  color: #22ac38;
}

.wpztText.dyj {
  background-color: #fff3e0;
  color: #ff7f00;
}

.wpztText.ydy {
  background-color: #dbecff;
  color: #0077e6;
}

.wpztText.yck {
  background-color: #ffe5ee;
  color: #e93a46;
}

.qrCodeBgBox {
  display: flex;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.toolBox {
  width: 100%;
  height: 100%;
  display: flex;
}

.toolBoxOne {
  display: inline-block;
  width: 100%;
  color: #5c7299;
  cursor: pointer;
}

.toolBoxOne:hover {
  color: #005af5;
}


.wpzxBottomPageLeftBtn {
  cursor: pointer;
  font-size: 14px;
}

.wpzxBottomPageLeft {
    padding-left: 16px;
    display: flex;
    font-size: 14px;
}

.hasCheckedNum {
    color: #3179f5;
    font-size: 14px;
}
.ivu-page-item-active {
    background-color: #2B5FD9!important;
}
.ivu-page-item-active:hover>a, .ivu-page-item-active>a {
    color: #fff!important;
}


// 认证中心共同样式
.size{
  width: 100%;
  height: 100%;
}
.base_flex {
  display: flex;
}
.base_style {
  .size;
  background-color: #fff;
  padding: 10px;
  padding-top: 0;
  display: flex;
  flex-direction: column;
}
// 认证中心顶部操作栏样式
.common_optation {
  .base_flex;
  justify-content: space-between;
  margin: -5px;
  .input, .select, .button {
    margin: 5px;
  }
  .input, .select {
    max-width: 200px;
    min-width: 100px;
  }
  .common_left {
    .base_flex;
    margin: 10px 0;
    width: 100%;
    flex-wrap: wrap;
    > * {
      margin: 5px;
    }
  }
  .common_right {
    .base_flex;
    margin: 10px 0;
    justify-content: flex-end;
    width: 50%;
    flex-wrap: wrap;
    > * {
      margin: 5px;
    }
  }
}
.content-wrapper-protal{
  height: calc(~'100vh - 60px');
  overflow-y: auto;
}
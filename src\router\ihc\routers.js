import main from '@/components/main'
// import main from '@/components/app-main/index.vue'
import mainNew from '@/components/main-menu-new/main.vue'
import routerYfgl from './router-yfgl'
import routerSnjy from './router-snjy'
import routerSygl from './routr-sygl'
import routerSick from './router-sick'
import routerInfectiousDisease from './router-infectious-disease'
import routerYlfx from './router-ylxwfx'
import routerHealthCare from './router-health-care'
import routerJsycry from './router-jsycry'
import routerTfjb from './router-tfjb'
import routerJkjc from './router-jkjc'
import routerYysh from './router-yysh'
let menuMode = localStorage.getItem('menuMode')

/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [
  {
    path: '/ihc',
    name: 'ihc_protal',
    redirect: '/ihc/snjy/mbgl',
    component: menuMode === 'side' ? mainNew : main,
    meta: {
      hideInMenu: true,
      notCache: true
    },
    children: [
      {
        path: '/home',
        name: 'ihc_home',
        meta: {
          hideInMenu: true,
          title: '首页',
          notCache: true,
          icon: 'md-home'
        },
        component: () => import('@/view/home.vue')
      },
      {
        path: '/test-bsp-layout',
        name: 'test-bsp-layout',
        meta: {
          hideInMenu: false,
          title: 'BSP布局组件测试',
          notCache: true,
          icon: 'ios-apps'
        },
        component: () => import('@/view/test-bsp-layout/index.vue')
      },
      {
        path: '/protal',
        name: 'ihc_protal',
        meta: {
          hideInMenu: true,
          title: '门户页面',
          icon: 'md-home'
        },
        component: () => import('@/view/protal-components/index.vue')
      }
    ]
  },
  {
    path: '/ihc/login',
    name: 'ihc_login',
    meta: {
      title: '系统登录',
      hideInMenu: true
    },
    component: () => import('@/view/login/login.vue')
  },
  {
    path: '/application',
    name: 'application',
    meta: {
      title: '应用程序'
    },
    component: () => import('@/components/app-main/application.vue')
  },
  ...routerYfgl,
  ...routerSnjy,
  ...routerSygl,
  ...routerSick,
  ...routerInfectiousDisease,
  ...routerYlfx,
  ...routerHealthCare,
  ...routerJsycry,
  ...routerTfjb,
  ...routerJkjc,
  ...routerYysh
]

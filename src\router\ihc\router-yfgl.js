let menuMode = localStorage.getItem('menuMode')
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/yfgl',
    name: 'yfgl',
    meta: {
      title: '药房管理'
    },
    component: menuMode=='side'?mainNew:main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'ypgl',
        name: 'ypgl',
        meta: {
          title: '药品管理',
          menu: true,
          bread: true,
        },
                // component: () => import('@/view/yfgl/ypcg/index.vue')
        component: () => import('@/view/yfgl/ypgl/index.vue')
      },
      {
        path: 'ypcg',
        name: 'ypcg',
        meta: {
          title: '药品采购',
          menu: true,
          bread: true,
        },
        component: () => import('@/view/yfgl/ypcg/index.vue')
      },
      {
        path: 'medicineIORecord',
        name: 'medicineIORecord',
        meta: {
          title: '出入库台账',
          menu: true,
          bread: true,
        },
        component: () => import('@/view/yfgl/medicineIORecord/index.vue')
      },
      {
        path: 'medicineLossRecord',
        name: 'medicineLossRecord',
        meta: {
          title: '报损台账',
          menu: true,
          bread: true,
        },
        component: () => import('@/view/yfgl/medicineLossRecord/index.vue')
      },
      {
        path: 'prescriptionMedicineOut',
        name: 'prescriptionMedicineOut',
        meta: {
          title: '处方药品出库',
          menu: true,
          bread: true,
        },
        component: () => import('@/view/yfgl/prescriptionMedicineOut/index.vue')
      },
      // /prescriptionMedicineOut
    ]
  },

]

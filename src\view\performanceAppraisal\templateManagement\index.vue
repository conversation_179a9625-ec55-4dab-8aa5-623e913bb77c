<!-- 绩效考核-模板管理 -->
<template>
    <div class="performanceAppraisal-templateManagement-wrap">
        <div class="performanceAppraisal-templateManagement-wrap-left">
            <p class="zbfl lor"><span>指标分类</span><Button type="primary" @click="addType">新增指标分类</Button></p>
            <div class="templateManagement-type" v-if="typeList && typeList.length > 0">
                <p v-for="(item, index) in typeList" :key="index">
                    <Icon type="ios-contact" /> &nbsp;{{ item.title }}
                </p>
            </div>
            <noData v-else></noData>
        </div>
        <div class="performanceAppraisal-templateManagement-wrap-right">
            <div class="zbfl" style="margin: 0 16px;"><span>考核截至日期</span>
                <p><Button type="primary" @click="addTypeDate">考核截至日期</Button>&nbsp;&nbsp;<Button type="primary"
                        @click="addTypeZb">新增考核指标</Button></p>
            </div>
            <Table :columns="columns" :data="dataTable" border style="margin: 16px;">
                <template slot-scope="{ row, index }" slot="status">
                    <el-switch @change="getPartner(row, index)" v-model="row.partner">
                    </el-switch>
                </template>
                <template slot-scope="{ row, index }" slot="action">
                    <Button type="error" size="small" @click="remove(index)">删除</Button>
                </template>
            </Table>
        </div>
        <!-- 新增类型 -->
        <Modal v-model="openModalType" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="30%"
            :title="modeTitle">
            <addType v-if="modalType == 'addType'" />
            <div slot="footer">
                <Button @click="openModalType = false">取消</Button>
                <Button type="primary" @click="submitType" :loading="loadingsignIn">提交</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import addType from "./addType.vue"
import noData from "@/components/noData/index.vue"
export default {
    components: { addType, noData },
    data() {
        return {
            typeList: [],
            modalType: 'addType',
            modeTitle: '新增类型',
            columns: [
                {
                    title: '序号',
                    type: 'index',
                    width: "80",
                    align: "center"
                },
                {
                    title: '状态',
                    key: 'status',
                    width: "100",
                    align: "center",
                },
                {
                    title: '指标名称',
                    width: "200",
                    align: "center",
                    key: "useMedicineMethodName",
                },
                {
                    title: '指标类型',
                    key: 'useFrequencyName',
                    width: "150",
                    align: "center"
                },
                {
                    title: '指标描述',
                    slot: 'oneDosageNum',
                    width: "150",
                    align: "center"
                },
                {
                    title: '分值类型',
                    slot: 'useDay',
                    align: "center"
                },
                {
                    title: '分值',
                    key: 'specs',
                    align: "center",
                    width: "300",
                },
                {
                    title: '操作',
                    key: 'entrust',
                    align: "center",
                    width: "100",
                }
            ],
            openModalType: false,
            loadingsignIn: false,
            dataTable: []
        }
    },
    methods: {
        addType() {
            this.openModalType = true
            this.modalType = 'addType'
            this.modeTitle = '新增类型'
        },
        submitType() {

        },
        addTypeDate() {
            this.openModalType = true
            this.modalType = 'addTypeDate'
            this.modeTitle = '考核截至日期'

        },
        addTypeZb() {

        },
    }
}
</script>
<style scoped lang="less">
.performanceAppraisal-templateManagement-wrap {
    width: 100%;
    height: 100%;
    display: flex;

}

.performanceAppraisal-templateManagement-wrap-left {
    width: 320px;
    height: 100%;
    border-right: 1px solid #F0F0F0;
}

.performanceAppraisal-templateManagement-wrap-right {
    width: calc(~'100% - 320px');
}

.templateManagement-type {
    margin: 10px 0;
}

.templateManagement-type p {
    line-height: 34px;
}

.zbfl {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
}

.lor {
    border-left: 5px solid #2b5fd9;
    text-indent: 4px;
}
</style>
<template>
    <div class="assignUser">
        <div class='sysUser'>
            <div class="common_optation">
                <div class="common_left">
                <Input class="input" v-model="query_params.name" clearable @on-enter="get_table_data(1)" placeholder="请输入角色名称搜索"/>
                <i-button type="info" @click="get_table_data(1)" class="ivu-btn-icon" icon="md-search">查询</i-button>
                <i-button class="ivu-btn-no-icon" @click="on_reset_table">返回</i-button>
                </div>
            </div>
            <div class="wpzxBottomTable">
                <Table stripe border ref="selection" :columns="table_columns" @on-selection-change="on_batch_select" @on-select-all="on_select_all" @on-select-all-cancel="on_select_all_cancel"
                    :loading="custom_loading" :data="table_list_data" :height="500" ></Table>
            </div>
            <div class="wpzxBottomPage">
              <div class="wpzxBottomPageLeft">
                <div class="wpzxBottomPageLeftBtn">
                  <Checkbox v-model="all_election" @click.prevent.native="check_more_all">全选</Checkbox>
                </div>
                <span>共选中<span class="hasCheckedNum">&nbsp;{{checkNum}}&nbsp;</span>条数据</span>
              </div>
              <div style="padding-right:10px;">
                <Page show-total :total="page.totalPage" size="small"  @on-change="get_table_data" @on-page-size-change="changePageSize" />
              </div>
            </div>
        </div>
        <div class="assignOpation">
          <Button type="error" size='large' :disabled="deleteToRole" @click="cancelUnionRole"  icon="ios-arrow-back"></Button>
          &nbsp;
          <Button type="primary" size="large" :disabled="addToRole" @click="unionRole" icon="ios-arrow-forward"></Button>
        </div>
        <div class="assignUser">
            <div class="assignUserTitle">已分配角色</div>
            <div class="wpzxBottomTable" style="margin-top: 8px">
                <Table stripe border ref="checkSelection" :columns="check_table_columns" @on-selection-change="on_batch_check" :loading="check_loading" :data="check_list_data" :height="500" ></Table>
            </div>
            <div class="wpzxBottomPage">
                <div class="wpzxBottomPageLeft">
                    <div class="wpzxBottomPageLeftBtn">
                        <Checkbox v-model="all_check_selection" @click.prevent.native="check_all_check">全选</Checkbox>
                    </div>
                <span>共选中<span class="hasCheckedNum">&nbsp;{{num}}&nbsp;</span>条数据</span>
                </div>
                <div style="padding-right:10px;">
                    <Page :total="checkPage.totalPage" size="small"  @on-change="get_check_data" @on-page-size-change="changeCheckPageSize" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions } from 'vuex'
import tableMethods from '@/components/mixins/public-table-methods.js' // 表格公共方法
export default {
  props: {
    formValidate: Object,
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    
  },
  mixins: [ tableMethods ],
  data () {
    return {
      ruleValidate: {
        name: [
          { required: true, message: '角色组名称不能为空', trigger: 'blur' }
        ]
      },
      table_columns: [
        { type: 'selection', width: 52, align: 'center', tooltip: true },
        { title: '角色名称', key: 'name', align: 'center', tree: true, ellipsis: false, tooltip: false, minWidth: 150 },
        { title: '角色编码', key: 'code', align: 'center' },
      ],
      check_table_columns: [
        { type: 'selection', width: 52, align: 'center', tooltip: true },
        { title: '角色名称', key: 'name', align: 'center', tree: true, ellipsis: false, tooltip: false, minWidth: 150 },
        { title: '角色编码', key: 'code', align: 'center' },
      ],
      checkPage: {
        // 分页插件数据
        totalPage: 0,
        currentpage: 1,
        pageSize: 10
      },
      check_list_data: [], // 选中角色列表数据
      check_loading: false,
      show: this.value,
      all_check_selection: false,
      check_select: [],
      num: 0,
      show: true,
      assignIds: []
    }
  },
  mounted () {
    if (this.formValidate.id) {
      this.get_table_data('1')
      this.get_check_data('1')
    }
    
  },
  computed: {
    addToRole () {
      return this.batch_select.length === 0
    },
    deleteToRole () {
      return this.check_select.length === 0
    }
  },
  methods: {
    ...mapActions(['postRequest']),
    cancel () {
      this.$emit('input', false)
    },
    changePageSize (pageSize) {
      this.get_table_data(1, pageSize)
    },
    get_table_data (pageNo, pageSize) { // 未选中用户数据
      this.query_params.userId = this.formValidate.id
      this.batch_select = []
      this.checkNum = 0
      this.all_election = false
      this.table_data_request(this.$path.user_role_not_union_page, pageNo, pageSize).then(data => {
        if (data.success) {
          this.assignIds = data.assignIds
          this.init_table_data(data)
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: data.msg
          })
          this.$emit('on_cancel')
        }
      })
    },
    on_return_table (isRefreshTable) {
      this.$refs['formValidate'].resetFields()
      this.$emit('on_show_table', isRefreshTable)
    },
    get_check_data (pageNo, pageSize) { // 选中用户数据
      this.query_params.userId = this.formValidate.id
      this.check_select = []
      this.num = 0
      this.all_check_selection = false
      this.table_data_request(this.$path.user_role_union_page_url, pageNo, pageSize).then(data => {
        this.check_list_data = data.rows
        this.checkPage.totalPage = data.total
      })
    },
    changeCheckPageSize (pageSize) {
      this.get_check_data('1', pageSize)
    },
    check_all_check () {
      this.check_select = []
      this.check_list_data.map((item, index) => {
        // 已选中
        if (this.all_check_selection) {
          this.$refs.checkSelection.$refs.tbody.objData[index]._isChecked = false
        } else {
          this.$refs.checkSelection.$refs.tbody.objData[index]._isChecked = true
          this.check_select.push(this.$refs.checkSelection.$refs.tbody.objData[index])
        }
      })
      this.all_check_selection = !this.all_check_selection
      this.num = this.check_select.length
    },
    on_batch_check (data) {
      this.check_select = data
      this.num = data.length
      if (this.check_list_data.length === data.length) {
        this.all_check_selection = true
      } else {
        this.all_check_selection = false
      }
    },
    unionRole () {
      let ids = []
      this.batch_select.forEach(it => {
        ids.push(it.id)
      })
      this.postRequest({ url: this.$path.add_user_role, params: { userId: this.formValidate.id, roleIds: ids.join(',') } }).then(data => {
        if (data.success) {
          this.$Notice.success({
            title: '成功提示',
            desc: data.msg
          })
          this.get_table_data('1')
          this.get_check_data('1')
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: data.msg
          })
        }
      })
    },
    cancelUnionRole () {
      let ids = []
      let bool = true
      let _this = this
      try {
        this.check_select.forEach(function (it, i) {
          if (_this.assignIds.indexOf(it.id) < 0) {
            _this.$Notice.error({
              title: '错误提示',
              desc: '不具有取消关联' + it.name + '的权限'
            })
            throw Error()
          } else {
            ids.push(it.id)
          }
        })
      } catch (e) {
        bool = false
      }
      if (bool) {
        this.postRequest({ url: this.$path.delete_user_role, params: { userId: this.formValidate.id, roleIds: ids.join(',') } }).then(data => {
          if (data.success) {
            this.$Notice.success({
              title: '成功提示',
              desc: data.msg
            })
            this.get_check_data('1')
            this.get_table_data('1')
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: data.msg
            })
          }
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.assignUser{
  display: flex;
  height: 600px;
  margin: 0 10px;
  .sysUser{
    width: 46%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .assignOpation {
    display: flex;
    flex-direction: column;
    margin: 0 10px;
    justify-content: center;
  }
  .assignUser{
    width: 45%;
    display: flex;
    flex-direction: column;
    .assignUserTitle{
      margin: 10px 0;
      font-size: 15px;
    }
  }
}
</style>
// import Viewer from "@/view/viewer";
let menuMode = serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [{
    path: '/dms',
    name: 'dms',
    redirect: '/dms/jzgl',
    component: menuMode == 'side' ? mainNew : main, // () => import('@/components/app-main/index.vue'),
    meta: {
      requireAuth: true,
      title: '卷宗管理',
    },
    children: [
      // {
      //     path: 'home',
      //     name: 'home',
      //     meta: {
      //         title: '首页',
      //         requireAuth: true,
      //         isDMS: true
      //     },
      //     component: () =>
      //         import ('@/view/jzgl/home'),
      // },
      {
        path: 'bmzj/xzaj',
        name: 'xzaj',
        meta: {
          title: '选择人员',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          // import ('@/view/DMS/bmzj/xzaj'),
          import('@/view/DMS/bmzj/xzaj/index.vue')
      },
      {
        path: 'bmzj/xzry',
        name: 'xzry',
        meta: {
          title: '选择人员',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          // import ('@/view/DMS/bmzj/xzaj'),
          import('@/view/DMS/bmzj/xzry/index.vue')
      },
      {
        path: 'bmzj/znbm',
        name: 'znbm',
        meta: {
          title: '编目组卷',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/bmzj/znbm.vue'),
      },
      {
        path: 'bmzj/znzj',
        name: 'znzj',
        meta: {
          title: '确认组卷',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/bmzj/znzj.vue'),
      },
      {
        path: 'bmzj/znzj_over',
        name: 'znzj_over',
        meta: {
          title: '归档',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/bmzj/znzj_over.vue'),
      },
      {
        path: 'bmzj/intelMarking',
        name: 'intelMarking',
        meta: {
          title: '智能阅卷',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/znyj/intelMarking_npm/index.vue'),
      },
      {
        path: 'jzgl',
        name: 'jzgl',
        meta: {
          title: '卷宗管理',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/jzgl/index'),
      },
      {
        path: 'jzgl/jzrz',
        name: 'jzrz',
        meta: {
          title: '卷宗日志',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/jzrz'),
      },
      {
        path: 'jzgl/jzdc',
        name: 'jzdc',
        meta: {
          title: '案宗管理-卷宗导出申请',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/jzdc'),
      },
      {
        path: 'jzgl/jzdcApply',
        name: 'jzdcApply',
        meta: {
          title: '案宗管理-卷宗导出申请',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/jzdcApply'),
      },
      {
        path: 'jzgl/preview',
        name: 'preview',
        meta: {
          title: '案宗预览',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/preview'),
      },
      {
        path: 'jzgl/cxjl',
        name: 'cxjl',
        meta: {
          title: '撤销记录',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/cxjl'),
      },
      {
        path: 'jzgl/sljl',
        name: 'sljl',
        meta: {
          title: '上链记录',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/sljl'),
      },
      {
        path: 'jzgl/jzyl',
        name: 'jzyl',
        meta: {
          title: '卷宗预览',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/jzyl'),
      },
      {
        path: 'jzgl/jzys',
        name: 'jzys',
        meta: {
          title: '卷宗移送',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/jzys'),
      },
      {
        path: 'jzgl/ysjl',
        name: 'ysjl',
        meta: {
          title: '移送记录',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/ysjl'),

      },
      {
        path: 'jzgl/ysyl',
        name: 'ysyl',
        meta: {
          title: '卷宗移送预览',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzgl/ysyl'),
      },
      {
        path: 'jzjy/jzjy',
        name: 'jzjy',
        meta: {
          title: '卷宗借阅',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzjy/jzjy'),
      },
      {
        path: 'jzjy/apply',
        name: 'apply',
        meta: {
          title: '申请借阅',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzjy/apply'),
      },
      {
        path: 'jzjy/lotApply',
        name: 'lotApply',
        // component:Head,
        meta: {
          title: '申请借阅',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzjy/lotApply'),
      },
      {
        path: 'ajxx/dmsAjxx',
        name: 'dmsAjxx',
        meta: {
          title: '待办案件',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/ajxx/dmsAjxx'),
      },
      {
        path: 'jzdr/xzaj',
        name: 'jzdr',
        meta: {
          title: '导入卷宗',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzdr/xzaj.vue'),
      },
      {
        path: 'jzdr/qrzj',
        name: 'jzdrQrzj',
        meta: {
          title: '导入卷宗-确认组卷',
          requireAuth: true,
          isDMS: true
        },
        component: () =>
          import('@/view/DMS/jzdr/qrzj.vue'),
      },

      {
        path: 'znyj/zkyx',
        name: 'zkyx',
        meta: {
          title: '左看右写',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/znyj/zkyx/index.vue'),
      },
      {
        path: 'znyj/clbd',
        name: 'clbd',
        meta: {
          title: '材料比对',
          requireAuth: true,
          isDMS: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/DMS/znyj/clbd/index.vue'),
      },
      {
        path: 'message/message',
        name: 'message',
        meta: {
          title: '消息列表',
          requireAuth: true,
          isALL: true
        },
        component: () =>
          import('_c/message/message')
      },
      {
        path: 'message/dmsAjjyApprove',
        name: 'dmsAjjyApprove',
        meta: {
          title: '电子卷宗案卷借阅审批',
          requireAuth: true,
          isALL: true
        },
        component: () =>
          import('_c/message/dmsAjjyApprove')
      },
      {
        path: 'message/dmsAjdcApprove',
        name: 'dmsAjdcApprove',
        meta: {
          title: '电子卷宗案卷导出审批',
          requireAuth: true,
          isALL: true
        },
        component: () =>
          import('_c/message/dmsAjdcApprove')
      },
      {
        path: 'dzjz_config',
        title: '卷宗配置',
        name: 'dzjz_config',
        meta: {
          title: '卷宗配置',
          isALL: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/systemSettings/dzjzManage/dzjz_config.vue'),
      },
      {
        path: 'templateList',
        title: '卷宗模板',
        name: 'templateList',
        meta: {
          title: '卷宗模板',
          isALL: true,
          menu: true,
          bread: true,
        },
        component: () =>
          import('@/view/systemSettings/dzjzManage/dzjz_templateList.vue'),
      },
    ]
  },
  {
    path: '/dms/pdfViewer',
    name: 'pdfViewer',
    meta: {
      title: 'pdf文件预览',
      requireAuth: true,
      isDMS: true
    },
    component: () =>
      import('@/components/pdfViewer'),
  },
  // {
  //     path: '/dms/bmzj/znbm_zjcl',
  //     name: 'znbm',
  //     meta: {
  //         title: '编目组卷',
  //         requireAuth: true,
  //         isDMS: true
  //     },
  //     component: () =>
  //         import ('@/view/DMS/bmzj/znbm_icp_zjcl.vue'),
  // },
  // {
  //     path: '/dms/bmzj/znbm_dw',
  //     name: 'znbm',
  //     meta: {
  //         title: '编目组卷',
  //         requireAuth: true,
  //         isDMS: true
  //     },
  //     component: () =>
  //         import ('@/view/DMS/bmzj/znbm_icp_dw.vue'),
  // },

]

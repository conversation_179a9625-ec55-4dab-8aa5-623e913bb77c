let menuMode = localStorage.getItem('menuMode')
import main from '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'

export default [
	{
		path: "/comprehensive",
		name: "comprehensive",
		meta: {
			title: "综合管理",
		},
		component: menuMode == 'side' ? mainNew : main,
		children: [
			{
				path: "affairsMeeting",
				name: "affairsMeeting",
				meta: {
					title: "所务会议",
				},
				sider: true,
				bread: true,
				component: () => import("@/view/comprehensiveBusiness/affairsMeeting/index.vue"),
			},
			{
				path: "policeDuty",
				name: "policeDuty",
				meta: {
					title: "民警值班管理",
				},
				redirect: "/policeDuty/dutyPublish",
				component: () => import("@/view/comprehensiveBusiness/policeDuty/index.vue"),
				children: [
					{
						path: "dutyPublish",
						name: "dutyPublish",
						meta: {
							title: "值班发布",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/policeDuty/dutyPublish/index.vue"),
					},
					{
						path: "dutyPlan",
						name: "dutyPlan",
						meta: {
							title: "值班安排",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/policeDuty/dutyPlan/index.vue"),
					},
					{
						path: "dutyModel",
						name: "dutyModel",
						meta: {
							title: "模板管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/policeDuty/dutyModel/index.vue"),
					},
					{
						path: "schedule",
						name: "schedule",
						meta: {
							title: "班次管理",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/policeDuty/schedule/index.vue"),
					}
				]
			},
			{
				path: "deathRegister",
				name: "deathRegister",
				meta: {
					title: "死亡登记",
				},
				redirect: "/deathRegister/deathRegisterHome",
				component: () => import("@/view/comprehensiveBusiness/deathRegister/index.vue"),
				children: [
					{
						path: "deathRegisterHome",
						name: "deathRegisterHome",
						meta: {
							title: "死亡登记",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/deathRegister/deathRegisterHome/index.vue"),
					},
					{
						path: "deathRegisterRecord",
						name: "deathRegisterRecord",
						meta: {
							title: "业务台账",
							menu: true,
							bread: true,
						},
						component: () => import("@/view/comprehensiveBusiness/deathRegister/deathRegisterRecord/index.vue"),
					}
				]
			},
			{
				path: "superviseRule",
				name: "superviseRule",
				meta: {
					title: "值班督导",
				},
				sider: true,
				bread: true,
				component: () => import("@/view/comprehensiveBusiness/superviseRule/index.vue"),
			},
			{
				path: "prisonSupervisionLayout",
				name: "prisonSupervisionLayout",
				meta: {
					title: "监所督导",
				},
				redirect: "/comprehensive/prisonSupervisionLayout/prisonSupervisionHome",
				component: () => import("@/view/comprehensiveBusiness/prisonSupervisionCenter/index.vue"),
				children: [
					{
						path: "prisonSupervisionHome",
						name: "prisonSupervisionHome",
						meta: {
							title: "监所督导",
						},
						sider: true,
						bread: true,
						component: () => import("@/view/comprehensiveBusiness/prisonSupervisionCenter/home.vue"),
					},
					{
						path: "prisonAddSupervision",
						name: "prisonAddSupervision",
						meta: {
							title: "新增督导",
						},
						sider: true,
						bread: true,
						component: () => import("@/view/comprehensiveBusiness/prisonSupervisionCenter/addSupervision.vue"),
					},
					{
						path: "prisonAddComplain",
						name: "prisonAddComplain",
						meta: {
							title: "新增申诉",
						},
						sider: true,
						bread: true,
						component: () => import("@/view/comprehensiveBusiness/prisonSupervisionCenter/complainForm.vue"),
					},
					{
						path: "prisonAddFeedBack",
						name: "prisonAddFeedBack",
						meta: {
							title: "新增反馈",
						},
						sider: true,
						bread: true,
						component: () => import("@/view/comprehensiveBusiness/prisonSupervisionCenter/feedBackForm.vue"),
					}
				],
			},
			{
				path: "performanceAppraisal",
				name: "performanceAppraisal",
				meta: {
					title: "绩效考核",
				},
				// redirect: "src\view\performanceAppraisal\index.vue",
				component: () => import("@/view/performanceAppraisal/index.vue"),
				children: [{
					path: "templateManagement",
					name: "templateManagement",
					meta: {
						title: "模板管理",
					},
					sider: true,
					bread: true,
					component: () => import("@/view/performanceAppraisal/templateManagement/index.vue"),
				},
				{
					path: "performanceFilling",
					name: "performanceFilling",
					meta: {
						title: "绩效填写",
					},
					sider: true,
					bread: true,
					component: () => import("@/view/performanceAppraisal/performanceFilling/index.vue"),
				},

				],
			}

		]
	},
	// 数据固化
	{
		path: "/dataSolidification",
		name: "dataSolidification",
		meta: {
			title: "数据固化",
		},
		component: menuMode == 'side' ? mainNew : main,
		children: [
			{
				path: "guardDayData",
				name: "guardDayData",
				meta: {
					title: "每日数据报送", //看守所
				},
				sider: true,
				bread: true,
				component: () => import("@/view/dataSolidification/guard/guardDayData.vue"),
			},
			{
				path: "guardWeekData",
				name: "guardWeekData",
				meta: {
					title: "每周数据报送", //看守所
				},
				sider: true,
				bread: true,
				component: () => import("@/view/dataSolidification/guard/guardWeekData.vue"),
			},
			{
				path: "detentionDayData",
				name: "detentionDayData",
				meta: {
					title: "每日数据报送", //拘留所
				},
				sider: true,
				bread: true,
				component: () => import("@/view/dataSolidification/detention/detentionDayData.vue"),
			},
			{
				path: "detentionWeekData",
				name: "detentionWeekData",
				meta: {
					title: "每周数据报送", //拘留所
				},
				sider: true,
				bread: true,
				component: () => import("@/view/dataSolidification/detention/detentionWeekData.vue"),
			},
		]
	},
	// 智能腕带
	{
		path: "/watch",
		name: "watch",
		redirect: '/watch/alarm',
		meta: {
			title: "智能腕带",
		},
		component: menuMode == 'side' ? mainNew : main,
		children: [
			{
				path: "alarm",
				name: "alarm",
				meta: {
					title: "智能腕带告警",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/smartWristband/alarm/index.vue"),
			},
			{
				path: "person",
				name: "person",
				meta: {
					title: "人员管理",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/smartWristband/person/index.vue"),
			}
		],
	},
	// 门禁控制

	{
		path: "/doorControl",
		name: "doorControl",
		redirect: '/doorControl/alarm',
		meta: {
			title: "门禁控制",
		},
		component: menuMode == 'side' ? mainNew : main,
		children: [
			{
				path: "room",
				name: "room",
				meta: {
					title: "监室门禁",
					menu: true,
					bread: true,
				},
				component: () => import("@/view/doorControl/room/index.vue"),
			},
		],
	},
	// 基础信息管理 
	{
		path: "/basicInfor",
		name: "basicInfor",
		meta: {
			title: "基础信息管理",
		},
		component: menuMode == 'side' ? mainNew : main,
		redirect: "/basicInfor//workerInfor",
		children: [
			{
				path: "workerInfor",
				name: "workerInfor",
				meta: {
					title: "工作人员信息",
				},
				sider: true,
				bread: true,
				component: () => import("@/view/comprehensiveBusiness/basicInfor/workerInfor/index.vue"),
			},
			{
				path: "resideInfor",
				name: "resideInfor",
				meta: {
					title: "起居条件信息",
				},
				sider: true,
				bread: true,
				component: () => import("@/view/comprehensiveBusiness/basicInfor/resideInfor/index.vue"),
			}
		]
	}
]
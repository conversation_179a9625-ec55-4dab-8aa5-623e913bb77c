<template>
    <div class="user-avatar-dropdown" style="padding-right: 3px;">
        <Dropdown trigger="click" @on-click="handleClick" placement="bottom-end">
            <div style="width:auto;height: 60px;display: flex; align-items: center;">
                <Avatar :src="photo" alt="" v-if="photo" />
                <Avatar icon="ios-person" v-else />
                <a href="javascript:void(0)" class="author-menu-user" style="margin-right: 20px;color: #fff;">
                    {{ $store.state.common.userName || userName }}
                </a>
                <!-- <span class="useNme-header"></span> -->
                <Icon style="font-size: 18px;" type="md-arrow-dropdown" />
            </div>
            <DropdownMenu slot="list" transfer style="min-width:150px;max-width: 200px;">
                <DropdownItem name="info">
                    <div>
                        <div style="font-size:18px;color:#2B3646;">{{ $store.state.common.userName || userName }}</div>
                        <div style="font-size:14px;color: #7A8699;margin-top: 8px;"
                            :title="$store.state.common.orgName">
                            {{ $store.state.common.orgName.length > 12 ? `${$store.state.common.orgName.substring(0,
                                12)}...` : $store.state.common.orgName }}
                        </div>
                    </div>
                </DropdownItem>
                <Dropdown placement="left-start" v-if="userJobList.length > 0 && list.length > 0"
                    class="change-dropdown" transfer>
                    <DropdownItem name="checkJob" divided>
                        <Icon type="ios-arrow-back"></Icon>
                        岗位切换
                    </DropdownItem>
                    <DropdownMenu slot="list" transfer style="width: 200px;">
                        <!-- <DropdownItem v-for="job in userJobList" :key="job.id" @click.native="changeJob(job.id)">{{job.jobName}}</DropdownItem> -->
                        <DropdownItem v-for="(item, index) in userJobList" :key="index" :name="item.userName"
                            @click.native="changeJob(item.id, item.jobName)">
                            <div :class="$store.state.common.jobId === item.id ? 'active' : 'job-check'"></div>
                            <div class="job-content">
                                {{ item.jobName ? item.jobName : '&nbsp;&nbsp;' }}
                                <div>
                                    <div class="job-addr">{{ item.orgName && item.orgName.length > 10 ?
                                        `${item.orgName.substring(0, 10)}...` : item.orgName }}</div>
                                </div>
                            </div>
                        </DropdownItem>
                    </DropdownMenu>
                </Dropdown>
                <DropdownItem name="changePW" divided>密码修改</DropdownItem>
                <DropdownItem name="mhManage" divided v-if="$route.path === '/homePage'">门户管理</DropdownItem>
                <DropdownItem name="version" divided>系统版本</DropdownItem>
                <DropdownItem name="logout" divided>退出登录</DropdownItem>
            </DropdownMenu>
        </Dropdown>
        <Modal v-model="modal" class-name="sys-ver-modal" width="400" title="系统版本" @on-cancel="cancel()"
            :closable="false" :mask-closable="false">
            <div class="flow-modal-title" slot="header">
                <span @click="cancel()" style="position: absolute; right: 6px;cursor: pointer;">
                    <i class="ivu-icon ivu-icon-ios-close"></i>
                </span>
            </div>
            <div style="text-align:center;">
                <p style="line-height:40px;font-size:16px;margin-top:30px;">系 统 版 本：{{ $config.version }}</p>
                <p style="line-height:30px;font-size:16px;margin-top:5px;">数 据 库：{{ dbVersion }}</p>
                <p :class="{ 'yellow-alert': licSurplusDays }" style="line-height:30px;font-size:16px;margin-top:5px;"
                    v-if="enableLicenseCheck && license">License到期时间：{{ license }}</p>
                <p :class="{ 'yellow-alert': warSurplusDays, 'red-alert': warSurplusDays < 0 }"
                    style="line-height:30px;font-size:16px;margin-top:5px;" v-if="enableLicenseCheck && zbEndTime">
                    质保服务到期时间：{{ zbEndTime }}</p>
            </div>
            <div style="font-size:12px;text-align:center;color:#999;line-height:50px;margin-top:20px;">
                {{ $config.copyRight }}</div>
            <div slot="footer">
                <Button type="primary" @click="cancel()" class="main-button cancel-btn">关&nbsp;&nbsp;闭</Button>
            </div>
        </Modal>
        <Modal v-model="alertZBtimeout" class-name="timeout-alert" width="400">
            <template #header>
                <p>
                    <span>提示</span>
                </p>
            </template>
            <div style="text-align:center;margin-top:-40px">
                <Icon type="ios-alert-outline" size="48" color="#f60"></Icon>
                <p style="font-size: 32px;">系统质保时间已到期</p>
            </div>
            <template #footer>
                <p style="color:gray;text-align:center" v-if="!contactPerson && !contactNumber">请联系当地客户代表，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactPerson">请联系：<span
                        style="color:#ff9900">{{ contactPerson }}</span>，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactNumber">联系电话：<span
                        style="color:#ed4014">{{ contactNumber }}</span></p>
            </template>
        </Modal>
        <!-- <Modal v-model="alertZB" class-name="timeout-alert" width="400">
            <template #header>
                <p>
                    <span>提示</span>
                </p>
            </template>
            <div style="text-align:center;margin-top:-40px">
                <Icon type="ios-alert-outline" size="48" color="#f60"></Icon>
                <p style="font-size: 24px;">系统质保时间还剩 <span
                        style="color: #ed4014;font-size: 38px;">{{warSurplusDays}}</span> 天</p>
            </div>
            <template #footer>
                <p style="color:gray;text-align:center" v-if="!contactPerson && !contactNumber">请联系当地客户代表，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactPerson">请联系：<span style="color:#ff9900">{{contactPerson}}</span>，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactNumber">联系电话：<span style="color:#ed4014">{{contactNumber}}</span></p>
            </template>
        </Modal>
        <Modal v-model="alertLic" class-name="timeout-alert" width="400">
            <template #header>
                <p>
                    <span>提示</span>
                </p>
            </template>
            <div style="text-align:center;margin-top:-40px">
                <Icon type="ios-alert-outline" size="48" color="#f60"></Icon>
                <p style="font-size: 24px;">系统License时间还剩 <span
                        style="color: #ed4014;font-size: 38px;">{{licSurplusDays}}</span> 天</p>
            </div>

            <template #footer>
                <p style="color:gray;text-align:center" v-if="!contactPerson && !contactNumber">请联系当地客户代表，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactPerson">请联系：<span style="color:#ff9900">{{contactPerson}}</span>，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactNumber">联系电话：<span style="color:#ed4014">{{contactNumber}}</span></p>
            </template>
        </Modal> -->
        <Modal v-model="alertLicZB" class-name="timeout-alert" width="400">
            <template #header>
                <p>
                    <span>提示</span>
                </p>
            </template>
            <div style="text-align:center;margin-top:-40px">
                <Icon type="ios-alert-outline" size="48" color="#f60"></Icon>
                <p style="font-size: 24px;" v-if="licSurplusDays > 0">系统License时间还剩 <span
                        style="color: #ed4014;font-size: 38px;">{{ licSurplusDays }}</span> 天</p>
                <p style="font-size: 24px;" v-if="warSurplusDays > 0">系统质保时间还剩 <span
                        style="color: #ed4014;font-size: 38px;">{{ warSurplusDays }}</span> 天</p>
                <p style="font-size: 32px;" v-if="!warEndDateStr || warSurplusDays < 0">系统质保时间已到期</p>

            </div>

            <template #footer>
                <p style="color:gray;text-align:center" v-if="!contactPerson && !contactNumber">请联系当地客户代表，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactPerson">请联系：<span
                        style="color:#ff9900">{{ contactPerson }}</span>，保障产品正常使用</p>
                <p style="color:gray;text-align:center" v-if="contactNumber">联系电话：<span
                        style="color:#ed4014">{{ contactNumber }}</span></p>
            </template>
        </Modal>
    </div>
</template>

<script>
import "./user.less";
import { mapActions, mapMutations } from "vuex";
import { getUserCache } from '@/libs/util'

export default {
    name: "User",
    inject: ['reload'],
    props: {
        userAvatar: {
            type: String,
            default: "",
        },
        messageUnreadCount: {
            type: Number,
            default: 0,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        }
    },
    data() {
        return {
            userName: getUserCache.getUserName(),
            modal: false,
            userJobList: [],
            currentUser: '',
            dbVersion: serverConfig.db,
            photo: '',
            license: '',
            zbEndTime: '',
            warSurplusDays: 0,
            licSurplusDays: 0,
            warEndDateStr: false,
            alertZB: false,
            alertLic: false,
            alertLicZB: false,
            alertZBtimeout: false,
            contactNumber: '',
            contactPerson: '',
            enableLicenseCheck: serverConfig.enableLicenseCheck,
            showTip: !localStorage.getItem('SurplusDays') ? false : true,
        };
    },
    mounted() {
        this.getUserPhoto()
        this.getJobUserList()
        if (serverConfig.enableLicenseCheck && !this.showTip) {
            this.getLicenseInfo()
        }
    },
    methods: {
        ...mapActions(["handleLogOut", "switch_access_token", "setting_session", "clearTagNavList"]),
        ...mapMutations(['setManageTag', 'setTagNavList']),
        showDetail() {
            this.modal = true;
        },
        getUserPhoto() {
            this.$store.dispatch('postRequest', { url: this.$path.get_user_photo, params: { userId: this.$store.state.common.userId } }).then(resp => {
                if (resp.success && resp.data) {
                    this.photo = resp.data.photo
                }
            })
        },
        getLicenseInfo() {
            this.$store.dispatch('getRequest', { url: this.$path.get_license_info, params: {} }).then(resp => {
                // let resp = {
                //     success: true,
                //     licSurplusDays: 0,
                //     warSurplusDays: 0,
                //     endDate: 180,
                //     warEndDateStr: '',
                //     contactNumber: '10086',
                //     contactPerson: '测试后勤'
                // }
                if (resp.success) {
                    if (resp.licSurplusDays || resp.warSurplusDays > 0) {
                        this.alertLicZB = true
                        this.warEndDateStr = resp.warEndDateStr
                    } else if (resp.warSurplusDays < 0 || !resp.warEndDateStr) {
                        this.alertZBtimeout = true
                        // setInterval(() => {
                        //     this.alertZBtimeout = true
                        // }, 60 * 30 * 1000);
                    }
                    // if (resp.licSurplusDays && !this.alertLicZB) {
                    //     this.alertLic = true
                    // }
                    // if (resp.warSurplusDays && resp.warSurplusDays >= 0 && !this.alertLicZB) {
                    //     this.alertZB = true
                    // }
                    // if (resp.warSurplusDays < 0 || !resp.warEndDateStr) {
                    //     this.alertZBtimeout = true
                    //     // setInterval(() => {
                    //     //     this.alertZBtimeout = true
                    //     // }, 60 * 30 * 1000);
                    // }
                    this.showTip = true
                    localStorage.setItem('SurplusDays', this.showTip)
                    this.license = resp.endDate
                    this.zbEndTime = resp.warEndDateStr
                    this.warSurplusDays = Number(resp.warSurplusDays)
                    this.licSurplusDays = Number(resp.licSurplusDays)
                    this.contactNumber = resp.contactNumber
                    this.contactPerson = resp.contactPerson

                }
            })
        },
        logout() {
            this.handleLogOut({ path: this.$path.login_out_url }).then(() => {
                //this.setTagNavList([])
                this.$router.push({
                    name: "login",
                });
            });
        },
        handleClick(name) {
            const _this = this
            const dropName = ['logout', 'version', 'checkJob', 'info']
            switch (name) {
                case "logout":
                    this.$Modal.confirm({
                        title: '是否确认退出登录',
                        onOk: async () => {
                            _this.logout();
                        }
                    })
                    break;
                case "changePW":
                    this.showChangePW();
                    break;
                case "mhManage":
                    this.setManageTag(false);
                    break;
                case "version":
                    this.showDetail();
                    break;
                case 'info':
                    // this.showUserInfo()
                    break;
            }
            // 判断其他操作
            if (!dropName.includes(name)) {
                this.currentUser = name
            }
        },
        // 修改密码页面
        showChangePW() {
            window.open('/#/changePW')
        },
        // 用户基本信息
        showUserInfo() {
            window.open('/#/userInfo')
            // this.$router.push('/userInfo')
        },
        // 获取用户岗位
        getJobUserList() {
            this.$store.dispatch('postRequest', {
                url: this.$path.query_user_job_url,
                params: {
                    userId: this.$store.state.common.userId,
                    appId: serverConfig.APP_ID,
                }
            }).then(data => {
                if (data.success) {
                    if (data.state) {
                        this.userJobList = data.data
                    }
                } else {
                    this.$Modal.warning({
                        title: '温馨提示',
                        content: data.msg
                    })
                }
            })
        },
        cancel() {
            this.modal = false
        },
        changeJob(jobId, jobName) {
            this.switch_access_token({ userName: this.$store.state.common.loginId, jobId: jobId, path: this.$path.login_url }).then(res => {
                if (res.access_token) {
                    this.setting_session(res).then(res => {
                        let resData = this.list.filter(
                            (item) => item.path === this.$config.homeName
                        )
                        this.$emit("on-close", resData, "all")
                        let desc = '岗位切换成功'
                        if (jobName) {
                            desc = '岗位【' + jobName + '】切换成功'
                        }
                        this.$Notice.success({
                            title: '温馨提示',
                            desc: desc
                        })
                        this.reload()
                    }).then((res) => {
                    })
                } else {
                    this.$Modal.warning({
                        title: '温馨提示',
                        content: '岗位【' + jobName + '】切换失败，原因:' + res.msg
                    })
                }
            })
        }
    },
};
</script>

<style scoped>
.red-alert {
    color: #ed4014 !important
}

.yellow-alert {
    color: #ff9900;
    font-weight: bold;
}

/deep/ .timeout-alert .ivu-modal-footer,
/deep/ .timeout-alert .ivu-modal-header {
    border-bottom: none !important;
    border-top: none !important
}

/deep/ .sys-ver-modal .ivu-modal-body,
/deep/ .sys-ver-modal .ivu-modal-header {
    padding: 0 !important;
}

/deep/ .sys-ver-modal .ivu-modal-footer {
    border-top: 1px solid #CEE0F0;
    padding: 9px 18px 10px 18px;
    background: #F7FAFF;
}

/deep/ .sys-ver-modal .ivu-icon-ios-close {
    font-size: 32px;
    line-height: 40px;
}

.sys-ver-modal .flow-modal-title {
    height: 40px;
    background: #2b5fda;
    width: 100%;
    text-indent: 1em;
    color: #fff;
    line-height: 40px;
}

.sys-ver-modal .main-button {
    background: #2b5fd9;
    margin-left: 20px;
}

.sys-ver-modal .cancel-btn {
    opacity: 1;
    font-size: 16px;
    border-radius: 2px;
    padding: 0;
    height: 30px;
    min-width: 70px;
}

.job-content {
    color: #3E4E66;
    font-size: 16px;
    display: inline-block;
}

.job-addr {
    color: #7A8699;
    font-size: 14px;
}

.job-check {
    width: 15px;
    height: 15px;
    margin-right: 10px;
    display: inline-block;
    position: relative;
}

.active {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
    top: 20%;
    left: 10px;
    background: url('../../../../assets//images/checked.png');
    width: 15px;
    height: 15px;
    background-size: cover;
}
</style>

<style lang="less" scoped>
/deep/.ivu-dropdown-item {
    font-size: 16px !important;
}

/deep/ .ivu-dropdown-item:hover {
    background: #F0F7FF;
}

.ivu-avatar {
    width: 40px;
    height: 40px;
    margin: 0px 10px 0px 25px;
}

/deep/ .ivu-avatar-image {
    border: 2px solid #FFFFFF;
}

/deep/.ivu-dropdown-rel {
    text-align: right;
    height: 60px;
    line-height: 60px;
}

/deep/.change-dropdown {
    width: 100%;

    .ivu-dropdown-rel {
        background-color: #fff;
        height: auto;
    }

    .ivu-icon {
        float: left;
        height: 25px;
        line-height: 25px;
        margin-right: -30px;
    }
}

/deep/.ivu-avatar.ivu-avatar-icon {
    font-size: 27px;
    border: 2px solid #fff;
}

/deep/.ivu-dropdown-item-divided {
    text-align: center;
}
</style>

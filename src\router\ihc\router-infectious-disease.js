// import main from '@/components/app-main/index.vue'
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
let menuMode = localStorage.getItem('menuMode')

export default [
  {
    path: '/infectious-disease',
    name: 'infectious-disease',
    meta: {
      title: '传染病管理'
    },
    redirect: '/infectious-disease/list',
    component: menuMode == 'side' ? mainNew : main,
    children: [
      {
        path: 'list',
        name: 'aids-management-list',
        meta: {
          title: '传染病管理',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/crb/index.vue')
      },
      {
        path: 'aids/form',
        name: 'aids-management-addForm',
        meta: {
          title: '传染病管理',
          menu: false,
          bread: true
        },
        component: () => import('@/view/snjy/aids-management/AddForm.vue')
      }
    ]
  }
]

// import main from '@/components/app-main/index.vue'
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
let menuMode = localStorage.getItem('menuMode')
export default [
  {
    path: '/ylxwfx',
    name: 'ylxwfx',
    meta: {
      title: '医疗行为分析'
    },
    component: menuMode === 'side' ? mainNew : main,
    children: [
      {
        path: 'bqfx',
        name: 'bqfx',
        meta: {
          title: '病情趋势',
          menu: true,
          bread: true
        },
        component: () => import('@/view/ylxwfx/bqfx/index.vue')
      },
      {
        path: 'ycfxjtx',
        name: 'ycfxjtx',
        meta: {
          title: '异常分析及提醒',
          menu: true,
          bread: true
        },
        component: () => import('@/view/ylxwfx/ycfxjtx/index.vue')
      },
      {
        path: 'jfytj',
        name: 'jfytj',
        meta: {
          title: '拒服药统计',
          menu: true,
          bread: true
        },
        component: () => import('@/view/ylxwfx/jfytj/index.vue')
      },
      {
        path: 'ywgzltj',
        name: 'ywgzltj',
        meta: {
          title: '医务工作量统计(医生)',
          menu: true,
          bread: true
        },
        component: () => import('@/view/ylxwfx/ywgzltj/index.vue')
      },
      {
        path: 'ywgzltj-hs',
        name: 'ywgzltj-hs',
        meta: {
          title: '医务工作量统计(护士)',
          menu: true,
          bread: true
        },
        component: () => import('@/view/ylxwfx/ywgzltj-hs/index.vue')
      }
    ]
  }

]

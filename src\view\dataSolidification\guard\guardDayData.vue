<template>
    <div class="bsp-base-form-guard">
        <div class="top">
            <RadioGroup v-model="type" type="button" button-style="solid" @on-change="changeType">
                <Radio label="0">当前报送</Radio>
                <Radio label="1">历史报送</Radio>
            </RadioGroup>
            <!-- <Alert class="tip" show-icon v-if="type == '0'">统计周期：{{ yesterdayRange }}</Alert> -->
            <div class="tip" v-if="type == '0'">统计周期：{{ yesterdayRange }}</div>
        </div>
        <div style="height: 73vh;overflow: auto;">
            <comDay v-if="type == '0'" :msgData="msgData"> </comDay>
            <div v-else>
                <s-DataGrid ref="grid" funcMark="mrsjbs-kss" :customFunc="true">
                    <template slot="customRowFunc" slot-scope="{ func,row,index }">
                        <Button type="primary" size="small" v-if="func.includes(globalAppCode + ':mrsjbskss:xq')"
                            @click="handleDetails(row)">详情</Button>
                    </template>
                </s-DataGrid>
            </div>
        </div>
        <Modal v-model="modalVisible" width="80%" :title="`详情（${msgData?.solidificationDate }）`" :footer-hide="true">
            <comDay :msgData="msgData"> </comDay>
        </Modal>


    </div>
</template>



<script>
import { Affix } from 'view-design';
import comDay from "./comDay.vue";
import { mapActions } from "vuex";
export default {
    name: 'guardDayData',
    components: {
        comDay, Affix
    },
    data() {
        return {
            // dataList: []
            type: '0',
            msgData: {
            },
            yesterdayRange: this.getYesterdayRange(),
            modalVisible: false,
        }
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        // 获取数据
        getData() {
            this.authGetRequest({ url: this.$path.ds_getBySolidificationDate, params: {} }).then(res => {
                if (res.success) {
                    this.msgData = res.data;
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        getDayData(id) {
            this.authGetRequest({ url: this.$path.ds_getBySolidificationDateId, params: { id } }).then(res => {
                if (res.success) {
                    this.msgData = res.data;
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        changeType(type) {
            console.log(type);
            if (type == '0') {
                this.getData();
            }

        },
        getYesterdayRange() {
            const yesterday = this.dayjs().subtract(1, "day");
            const dateStr = yesterday.format("YYYY年M月D日");
            return `${dateStr} 0:00 至 ${dateStr} 24:00`;

        },
        handleDetails(row) {
            console.log(row.id);
            this.modalVisible = true
            this.getDayData(row.id)
        }
    },
    mounted() {
        this.getData();
    }
}



</script>


<style scoped lang="less">
.bsp-base-form-guard {
    // padding: 15px;
    // position: relative;

    .top {
        display: flex;
        margin-bottom: 15px;
        align-items: center;

        .tip {
            margin-left: 100px;
            font-size: 16px;
            font-weight: bold;
            
        }
    }



}

/deep/.ivu-modal-body {
    max-height: 750px;
    overflow: auto;
    padding-bottom: 16px !important;
}
</style>
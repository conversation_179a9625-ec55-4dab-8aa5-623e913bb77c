<template>
    <div>
        <div class="cont">
            <div class="fm-content-info">
                <p class="fm-content-info-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />详情
                </p>
                <div class="fm-content-box">
                    <Row>
                        <Col span="4"><span>门禁点名称</span></Col>
                        <Col span="8"><span>{{ formItem.name }}</span></Col>
                        <Col span="4"><span>所属区域</span></Col>
                        <Col span="8"><span>{{ formItem.origin_region_name }}</span></Col>
                        <Col span="4"><span>区域路径</span></Col>
                        <Col span="8"><span>{{ formItem.origin_region_path_name }}</span></Col>
                        <Col span="4"><span>监室</span></Col>
                        <Col span="8"><span>{{ formItem.room_name }}</span></Col>
                        <Col span="4"><span>门禁点编号</span></Col>
                        <Col span="8"><span>{{ formItem.door_no }}</span></Col>
                        <Col span="4"><span>门禁点状态</span></Col>
                        <Col span="8"><span>{{ formItem.door_statusName }}</span></Col>
                        <Col span="4"><span>绑定监室</span></Col>
                        <Col span="20"><span>{{ formItem.bind_room_statusName }}</span></Col>
                    </Row>
                </div>
            </div>
        </div>
        <div class="tab-box">
            <Tabs value="1">
                <TabPane label="门禁控制事件" name="1">
                    <Table border :columns="columns1" :data="tableData"></Table>
                </TabPane>
            </Tabs>
        </div>


        <div class="bsp-base-fotter">
            <Button @click="close" style="margin-right: 10px;">取消</Button>
        </div>
    </div>
</template>

<script>

export default {
    props: {
        formItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            tableData: [],
            columns1: [
                {
                    title: '告警ID',
                    key: 'id',
                    align: 'center'
                }
            ]
        }
    },
    methods: {
        close() {
            this.$emit('close')
        },
         getData() {
            this.authGetRequest({url:this.$path.pm_getDoorControlEventList,params:{}}).then(res => {
                
                if (res.success) {
                    console.log(res,'res');
                    this.tableData = res.data
                }
                
            })

        }
    },
    mounted() {

    },
}

</script>

<style scoped lang="less">
.fm-content-info-title {
    border-top: 1px solid #CEE0F0;
    border-left: 1px solid #CEE0F0;
    border-right: 1px solid #CEE0F0;
}

.cont {
    box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
    border-radius: 4px;
    padding: 10px;
}

.tab-box {
    margin-top: 10px;
    box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
    padding: 10px;
}
</style>
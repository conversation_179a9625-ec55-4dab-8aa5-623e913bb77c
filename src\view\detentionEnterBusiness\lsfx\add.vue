<template>
  <div class="personal-calss">
	  <div class="bsp-base-content" style="width: 100%; ">
		<div class="personal-left">
		  <jgryxx v-if="jgrybm" :jgrybm="jgrybm"></jgryxx>
		</div>
		<div class="personal-right">
            <div class="add-form" style="padding: 20px;">
                <Form ref="formData" :model="formData" :label-width="140" :label-colon="true" >
                    <Row>
                        <Col span="24">
                            <FormItem label="留所原因" prop="detainReason" :rules="[{ trigger: 'change', message: '请选择',required: true}]">
                                <s-dicgrid v-model="formData.detainReason" style="width: 300px;"  dicName="ZD_DB_LSYY" />
                            </FormItem>
                        </Col>
                        <Col span="24">
                            <FormItem label="留所原因详情" prop="detainReasonDetails">
                                <Input type="textarea" :autosize="{minRows: 3,maxRows: 3}"
                                v-model="formData.detainReasonDetails" placeholder="请输入" maxlength=""></Input>
                            </FormItem>
                        </Col>
                        <Col span="24">
                            <FormItem label="备注" prop="remark">
                                <Input type="textarea" :autosize="{minRows: 3,maxRows: 3}"
                                v-model="formData.remark" placeholder="请输入" maxlength=""></Input>
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="经办人" prop="operatePolice" :rules="[{ trigger: 'change', message: '请选择',required: true}]">
                                <Input type="text" placeholder="请输入" v-model="formData.operatePolice" />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="经办时间" prop="operateTime" :rules="[{ trigger: 'change', message: '请选择',required: true}]" style="width: 100%;">
                                <el-date-picker v-model="formData.operateTime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择" style="width: 100%;"></el-date-picker>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
		</div>
	  </div>
	  <div class="bsp-base-fotter">
		<Button @click="on_return_table(false)" >返 回</Button>
		<Button @click="submit" type="primary">提 交</Button>
	  </div>
	</div>
</template>

<script>
import { mapActions } from "vuex";
import jgryxx from "./jgryxx.vue";
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js"
export default {
    components: {
        jgryxx
    },
    props: {
	  stepType: {
		type: String,
		default: "",
	  },
	  jgrybm: {
		type: String,
		default: "",
	  },
      ryxxObj: {
        type: Object,
        default: {}
      }
	},
    data() {
        return {
            formData: {
                operatePolice: this.$store.state.common.userName,
                operatePoliceSfzh: this.$store.state.common.idCard,
                operateTime: formatDateparseTime(new Date())
            }
        }
    },
    mounted() {
        console.log(this.$store.state.common)
    },
    methods: {
        submit() {
            this.$refs.formData.validate((valid) => {
                if(valid) {
                    this.saveData()
                } else {
                    this.$$Message.error('请填写完整内容')
                }
            })
        },
        saveData() {
            console.log('保存');
            console.log(this.ryxxObj,'ryxxObj')
            // return;
            this.formData.jgrybm = this.ryxxObj.jgrybm
            this.formData.jgryxm = this.ryxxObj.xm
            this.$store.dispatch('authPostRequest',{
                url: this.$path.app_serveSentence_create,
                params: this.formData
            }).then(res => {
                if(res.success) {
                    console.log(res,'res');
                    this.$nextTick(() => {
                        this.on_return_table()
                    })
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作识别!'
                    })
                }
            })
        },
        on_return_table() {
            this.$emit("on_show_table");
        }
    }
}
</script>

<style lang="less" scoped>
  .personal-calss {
	width: 100%;
	height: 100%;
	display: flex;
	.personal-left {
	  width: 20%;
	  height: 100%;
	  border-right: 1px solid #cee0f0;
	}
	.personal-right {
	  width: 80%;
	  overflow-y: auto;
	  height: 100%;
	}
  }
  .bsp-base-content {
	// right: 32px !important;
	bottom: 50px !important;
	display: flex;
  }
  .personal-right::-webkit-scrollbar,.main-content::-webkit-scrollbar,.tree-box::-webkit-scrollbar,.bsp-scroll::-webkit-scrollbar {/*滚动条整体样式*/
	width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
	height: 10px;
  
  }
  .personal-right::-webkit-scrollbar-thumb,.main-content::-webkit-scrollbar-thumb,.tree-box::-webkit-scrollbar-thumb,.bsp-scroll::-webkit-scrollbar-thumb  {/*滚动条里面小方块*/
	border-radius: 3px;
	background: #b7c7dd;
  
  }
  .personal-right::-webkit-scrollbar-track ,.main-content::-webkit-scrollbar-track,.tree-box::-webkit-scrollbar-track,.bsp-scroll::-webkit-scrollbar-track{/*滚动条里面轨道*/
	border-radius: 3px;
	background: #EDEDED;
  }
 .ivu-timeline-item-head{
	background-color: none !important;
  }
  .bsp-base-content{
    top: 60px !important;
  }
  /deep/.el-input--prefix .el-input__inner {
    height: 30px !important;
  }
  
  </style>
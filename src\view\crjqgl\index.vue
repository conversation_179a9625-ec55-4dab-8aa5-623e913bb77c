<!-- 出入监区管理 -->
<template>
    <div class="content-defaulet">
        <div class="content-defaulet-main">
            <s-DataGrid ref="grid" funcMark="ckyw-crjqgllb" :customFunc="true" :params="{}">
                <template slot="customHeadFunc" slot-scope="{ func }">
                    <Button type="primary" v-if="func.includes(globalAppCode + ':ckyw-crjqgllb:add')"
                        @click.native="addRqrcxx">新增入区人车信息</Button>
                </template>
                <template slot="customRowFunc" slot-scope="{ func, row, index }" class="btnList">
                    <Button type="primary"
                        v-if="func.includes(globalAppCode + ':ckyw-crjqgllb:dj') && (row.status == '0' || row.status == '1')"
                        @click.native="wlryDj(row)" style="margin-right: 5px;">登记</Button>
                    <Button type="primary" v-if="func.includes(globalAppCode + ':ckyw-crjqgllb:detail')"
                        @click.native="wlryDetail(row)" style="margin-right: 5px;">详情</Button>
                    <Button type="primary" v-if="func.includes(globalAppCode + ':ckyw-crjqgllb:xxfy')"
                        @click.native="xxfyEvent(row)">信息复用</Button>
                </template>
            </s-DataGrid>
        </div>
        <!-- 外来人员登记 -->
        <Modal v-model="openModalTimed" :mask-closable="false" :closable="true" class-name="select--modal"
            width="1200px" title="外来人员登记">
            <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
                <div class="sys-sub-title">人车信息</div>
                <el-descriptions class="margin-top" :column="3" size="small" border>
                    <el-descriptions-item label="姓名" :labelStyle="{ width: '10em' }">
                        {{ djInfo.xm }}
                    </el-descriptions-item>
                    <el-descriptions-item label="证件类型" :labelStyle="{ width: '10em' }">
                        {{ djInfo.zjlx }}
                    </el-descriptions-item>
                    <el-descriptions-item label="身份证号" :labelStyle="{ width: '10em' }">
                        {{ djInfo.zjhm }}
                    </el-descriptions-item>
                    <el-descriptions-item label="性别" :labelStyle="{ width: '10em' }">
                        {{ djInfo.xb }}
                    </el-descriptions-item>
                    <el-descriptions-item label="车辆号码" :labelStyle="{ width: '10em' }">
                        {{ djInfo.clhm }}
                    </el-descriptions-item>
                    <el-descriptions-item :span="2" label="进出监区原因" :labelStyle="{ width: '10em' }">
                        {{ djInfo.jrjqry }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系方式" :labelStyle="{ width: '10em' }">
                        {{ djInfo.lxfs }}
                    </el-descriptions-item>
                    <el-descriptions-item label="业务民警" :labelStyle="{ width: '10em' }">
                        {{ userName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="身份背景调查" :labelStyle="{ width: '10em' }">
                        {{ djInfo.sfbjdc }}
                    </el-descriptions-item>
                    <el-descriptions-item label="来访预计时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.yjlfsj }}
                    </el-descriptions-item>
                    <el-descriptions-item label="预计离所时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.yjlssj }}
                    </el-descriptions-item>
                </el-descriptions>

                <Form ref="formIn" :model="djInfo" :label-colon="true" :rules="rulesIn">
                    <div class="sys-sub-title" v-if="formData.status == '1' || formData.status == '2'">进监区信息</div>
                    <el-descriptions class="margin-top" :column="2" size="small" border
                        v-if="formData.status == '1' || formData.status == '2'">
                        <el-descriptions-item label="进入监区时间" :labelStyle="{ width: '10em' }">
                            <FormItem prop="jrjqsj">
                                <el-date-picker v-model="djInfo.jrjqsj" format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                    placeholder="请选择"></el-date-picker>
                            </FormItem>
                        </el-descriptions-item>
                        <el-descriptions-item label="检查人" :labelStyle="{ width: '10em' }">
                            <FormItem prop="jrjqaqjcrsfzh">
                                <user-selector v-model="djInfo.jrjqaqjcrsfzh" tit="用户选择" :text.sync="djInfo.jrjqaqjcrxm"
                                    @onCancel="onCancelJr" @onClear="onClearJr" @onSelect="onSelectJr"
                                    returnField="idCard" numExp='num>=1'>
                                </user-selector>
                            </FormItem>
                        </el-descriptions-item>
                        <el-descriptions-item label="安全检查信息" :labelStyle="{ width: '10em' }">
                            <FormItem prop="jrjqaqjcxx">
                                <Input v-model="djInfo.jrjqaqjcxx" placeholder="请输入" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 3 }" />
                            </FormItem>
                        </el-descriptions-item>
                    </el-descriptions>
                </Form>
                <Form ref="formOut" :model="djInfo" :label-colon="true" :rules="rulesOut">
                    <div class="sys-sub-title" v-if="formData.status == '2'">出监区信息</div>
                    <el-descriptions class="margin-top" :column="2" size="small" border v-if="formData.status == '2'">
                        <el-descriptions-item label="离开监区时间" :labelStyle="{ width: '10em' }">
                            <FormItem prop="lkjqsj">
                                <el-date-picker v-model="djInfo.lkjqsj" format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                    placeholder="请选择"></el-date-picker>
                            </FormItem>
                        </el-descriptions-item>
                        <el-descriptions-item label="检查人" :labelStyle="{ width: '10em' }">
                            <FormItem prop="lkjqaqjcrsfzh">
                                <user-selector v-model="djInfo.lkjqaqjcrsfzh" tit="用户选择" :text.sync="djInfo.lkjqaqjcrxm"
                                    @onCancel="onCancelLk" @onClear="onClearLk" @onSelect="onSelectLk"
                                    returnField="idCard" numExp='num>=1'>
                                </user-selector>
                            </FormItem>
                        </el-descriptions-item>
                        <el-descriptions-item label="安全检查信息" :labelStyle="{ width: '10em' }">
                            <FormItem prop="lkjqaqjcxx">
                                <Input v-model="djInfo.lkjqaqjcxx" placeholder="请输入" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 3 }" />
                            </FormItem>
                        </el-descriptions-item>
                    </el-descriptions>
                </Form>

            </div>
            <div slot="footer" style="text-align: center;">
                <Button @click="openModalTimed = false">取消</Button>
                <Button @click="saveDj" type="primary">提交</Button>
            </div>
        </Modal>

        <!-- 新增入区人车信息 -->
        <Modal v-model="addRqrcxxModal" :mask-closable="false" :closable="true" class-name="select--modal"
            width="1100px" title="新增入区人车信息">
            <div class="form-content">
                <Form ref="formData" :model="formData" :label-width="140" :label-colon="true">
                    <div class="form">
                        <Row>
                            <Col span="12">
                            <FormItem label="姓名" prop="xm"
                                :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]">
                                <Input v-model="formData.xm" placeholder="请输入姓名" />
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="性别" prop="xb">
                                <s-dicgrid v-model="formData.xb" :isSearch="true" dicName="ZD_GABBZ_XB" />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="12">
                            <FormItem label="证件类型" prop="zjlx"
                                :rules="[{ trigger: 'blur,change', message: '证件类型为必填', required: true }]">
                                <s-dicgrid v-model="formData.zjlx" :isSearch="true" dicName="ZD_GABBZ_ZJZL" />
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="身份证号" prop="zjhm"
                                :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]">
                                <Input v-model="formData.zjhm" placeholder="请输入身份证号" />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="12">
                            <FormItem label="进入监区原因" prop="jrjqry"
                                :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]">
                                <Input v-model="formData.jrjqry" placeholder="请输入进入监区原因" />
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="身份背景调查" prop="sfbjdc"
                                :rules="[{ trigger: 'blur', message: '必填', required: true }]">
                                <s-dicgrid v-model="formData.sfbjdc" :isSearch="true" dicName="ZD_WB_SFBJDC" />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="12">
                            <FormItem label="联系方式" prop="lxfs"
                                :rules="[{ trigger: 'blur,change', message: '证件类型为必填', required: true }]">
                                <Input v-model="formData.lxfs" placeholder="请输入联系方式" />
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="车辆号码" prop="clhm">
                                <Input v-model="formData.clhm" placeholder="请输入" />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="12">
                            <FormItem label="预约来访时间" prop="yjlfsj"
                                :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]" style="width: 100%;">
                                <el-date-picker v-model="formData.yjlfsj" format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择"
                                    @change="handleYjlfsjChange" style="width: 100%;"> </el-date-picker>
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="预约离所时间" prop="yjlssj"
                                :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]" style="width: 100%;">
                                <el-date-picker v-model="formData.yjlssj" format="yyyy-MM-dd HH:mm:ss"
                                    value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择"
                                    style="width: 100%;">
                                </el-date-picker>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="12">
                            <FormItem label="人员照片" prop="xm"
                                :rules="[{ trigger: 'blur', message: '人员照片为必传', required: true }]">
                                <Tooltip max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。"
                                    placement="top">
                                    <s-ImageUploadLocal v-model="formData.zpUrl" :maxSize="5120" ref="imgUpload"
                                        :multiple="false" @getfile="getfile" :defaultList="defaultList" :maxFiles="1" />
                                </Tooltip>
                            </FormItem>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </div>
            <div slot="footer" style="text-align: center;">
                <Button @click="addRqrcxxModal = false">取消</Button>
                <Button type="primary" @click="saveRqrcInfo" :loading="timedLoading">提交</Button>
            </div>
        </Modal>

        <!-- 外来人员详情 -->
        <Modal v-model="addRqrcxxDetailModal" :mask-closable="false" :closable="true" class-name="select--modal"
            width="1200px" title="外来人员详情">
            <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
                <div class="sys-sub-title">人车信息</div>
                <el-descriptions class="margin-top" :column="3" size="small" border>
                    <el-descriptions-item label="姓名" :labelStyle="{ width: '10em' }">
                        {{ djInfo.xm }}
                    </el-descriptions-item>
                    <el-descriptions-item label="证件类型" :labelStyle="{ width: '10em' }">
                        {{ djInfo.zjlx }}
                    </el-descriptions-item>
                    <el-descriptions-item label="身份证号" :labelStyle="{ width: '10em' }">
                        {{ djInfo.zjhm }}
                    </el-descriptions-item>
                    <el-descriptions-item label="性别" :labelStyle="{ width: '10em' }">
                        {{ djInfo.xb }}
                    </el-descriptions-item>
                    <el-descriptions-item label="车辆号码" :labelStyle="{ width: '10em' }">
                        {{ djInfo.clhm }}
                    </el-descriptions-item>
                    <el-descriptions-item :span="2" label="进出监区原因" :labelStyle="{ width: '10em' }">
                        {{ djInfo.jrjqry }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系方式" :labelStyle="{ width: '10em' }">
                        {{ djInfo.lxfs }}
                    </el-descriptions-item>
                    <el-descriptions-item label="业务民警" :labelStyle="{ width: '10em' }">
                        {{ userName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="身份背景调查" :labelStyle="{ width: '10em' }">
                        {{ djInfo.sfbjdc }}
                    </el-descriptions-item>
                    <el-descriptions-item label="来访预计时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.yjlfsj }}
                    </el-descriptions-item>
                    <el-descriptions-item label="预计离所时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.yjlssj }}
                    </el-descriptions-item>
                </el-descriptions>

                <div class="sys-sub-title">进监区信息</div>
                <el-descriptions class="margin-top" :column="3" size="small" border>
                    <el-descriptions-item label="进入监区时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.jrjqsj }}
                    </el-descriptions-item>
                    <el-descriptions-item label="安全检查信息" :labelStyle="{ width: '10em' }">
                        {{ djInfo.jrjqaqjcxx }}
                    </el-descriptions-item>
                    <el-descriptions-item label="监察人" :labelStyle="{ width: '10em' }">
                        {{ djInfo.jrjqaqjcrxm }}
                    </el-descriptions-item>
                </el-descriptions>

                <div class="sys-sub-title">出监区信息</div>
                <el-descriptions class="margin-top" :column="3" size="small" border>
                    <el-descriptions-item label="离开监区时间" :labelStyle="{ width: '10em' }">
                        {{ djInfo.lkjqsj }}
                    </el-descriptions-item>
                    <el-descriptions-item label="安全检查信息" :labelStyle="{ width: '10em' }">
                        {{ djInfo.lkjqaqjcxx }}
                    </el-descriptions-item>
                    <el-descriptions-item label="监察人" :labelStyle="{ width: '10em' }">
                        {{ djInfo.lkjqaqjcrxm }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <div slot="footer" style="text-align: center;">
                <Button @click="addRqrcxxDetailModal = false">取消</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import tabs from "@/components/tabs/index.vue";
import { sImageUploadLocal } from '@/components/upload/image'
import { userSelector } from 'sd-user-selector'

export default {
    components: {
        tabs,
        sImageUploadLocal,
        userSelector
    },
    data() {
        return {
            showFormCompnent: true,
            addRqrcxxModal: false,
            openModalTimed: false,
            detail: {},
            defaultList: [],
            formData: {
                status: '0'
            },
            timedLoading: false,
            addRqrcxxDetailModal: false,
            djInfo: {},
            userName: this.$store.state.common.userName,
            rulesIn: {
                jrjqsj: [{ validator: this.validateInGroup, trigger: 'blur' }],
                jrjqaqjcxx: [{ validator: this.validateInGroup, trigger: 'blur' }],
                jrjqaqjcrsfzh: [{ validator: this.validateInGroup, trigger: 'change' }]
            },
            rulesOut: {
                lkjqsj: [{ validator: this.validateOutGroup, trigger: 'blur' }],
                lkjqaqjcxx: [{ validator: this.validateOutGroup, trigger: 'blur' }],
                lkjqaqjcrsfzh: [{ validator: this.validateOutGroup, trigger: 'change' }]
            }
        }
    },
    methods: {
        zyryDetail(row) {
            this.openModalTimed = true
        },
        saveTimedVb() {

        },
        addRqrcxx() {
            this.formData = {}
            this.defaultList = []
            this.svaeType = 'add'
            this.addRqrcxxModal = true
        },
        xxfyEvent(row) {
            this.svaeType = 'xxfy'
            console.log(row, '信息复用');
            this.$store.dispatch('authGetRequest', {
                url: this.$path.acp_prisonAreaInout_get,
                params: {
                    id: row.id
                }
            }).then(res => {
                if (res.success) {
                    console.log(res, 'res');
                    // this.openModalTimed = true
                    this.formData = res.data
                    this.formData.yjlfsj = ''
                    this.formData.yjlssj = ''
                    this.formData.id = ''
                    this.formData.status = '0'
                    this.formData.statusName = '待入所'
                    let urlObj = { url: res.data.zpUrl, name: '' }
                    // this.defaultList = [urlObj] 
                    // this.$set(this, 'defaultList', [urlObj])
                    this.defaultList.splice(0, this.defaultList.length)
                    this.defaultList.push(urlObj)
                    this.$forceUpdate()
                    this.addRqrcxxModal = true
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        saveRqrcInfo() {
            // console.log(this.formData,'提交数据')
            // return;
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    console.log(this.formData, 'formData');
                    if (this.svaeType == 'add' || this.svaeType == 'xxfy') {
                        this.saveData()
                    } else if (this.svaeType == 'xxfy') {
                        this.updateEvent()
                    }
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: '请填写完整内容!'
                    })
                }
            })
        },
        saveData() {
            this.formData.status = '0'
            this.formData.statusName = '待入所'
            this.$store.dispatch('authPostRequest', {
                url: this.$path.acp_prisonAreaInout_create,
                params: this.formData
            }).then(res => {
                if (res.success) {
                    console.log(res, 'res')
                    this.addRqrcxxModal = false
                    this.$nextTick(() => {
                        this.on_refresh_table()
                    })
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        updateEvent() {
            this.$store.dispatch('authPostRequest', {
                url: this.$path.acp_prisonAreaInout_update,
                params: this.formData
            }).then(res => {
                if (res.success) {
                    this.addRqrcxxModal = false
                    this.$nextTick(() => {
                        this.on_refresh_table()
                    })
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        // saveDj() {
        //     console.log('123',this.djInfo)
        //     console.log(this.formData.status,'this.formData.status');
        //     this.djInfo.status = this.formData.status
        //     // return;
        //     this.$store.dispatch('authPostRequest',{
        //         url: this.$path.acp_prisonAreaInout_update,
        //         params: this.djInfo
        //     }).then(res => {
        //         if(res.success) {
        //             this.openModalTimed = false
        //             this.$nextTick(() => {
        //                 this.on_refresh_table()
        //             })
        //         } else {
        //             this.$Modal.error({
        //                 title: '温馨提示',
        //                 content: res.msg || '接口操作失败!'
        //             })
        //         }
        //     })
        // },
        saveDj() {
            // 先校验两个表单
            this.$refs.formIn.validate((validIn) => {
                if (!validIn) return;
                this.$refs.formOut.validate((validOut) => {
                    if (!validOut) return;
                    // 校验通过，继续提交
                    this.djInfo.status = this.formData.status;
                    this.$store.dispatch('authPostRequest', {
                        url: this.$path.acp_prisonAreaInout_update,
                        params: this.djInfo
                    }).then(res => {
                        if (res.success) {
                            this.openModalTimed = false;
                            this.$nextTick(() => {
                                this.on_refresh_table();
                            });
                        } else {
                            this.$Modal.error({
                                title: '温馨提示',
                                content: res.msg || '接口操作失败!'
                            });
                        }
                    });
                });
            });
        },
        getfile(data) {
            console.log(data, 'data')
            this.formData.zpUrl = data.url
        },
        wlryDetail(row) {
            this.$store.dispatch('authGetRequest', {
                url: this.$path.acp_prisonAreaInout_get,
                params: {
                    id: row.id
                }
            }).then(res => {
                if (res.success) {
                    console.log(res, res.jrjqaqjcrsfzh, 'res');
                    // this.openModalTimed = true
                    this.djInfo = res.data
                    this.addRqrcxxDetailModal = true
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        wlryDj(row) {
            this.djInfo = {}
            console.log(row, '登记')
            if (row.status == '0') {
                this.formData.status = '1'
                this.formData.statusName = '待出所'
            } else if (row.status == '1') {
                this.formData.status = '2'
                this.formData.statusName = '已出所'
            }
            this.$store.dispatch('authGetRequest', {
                url: this.$path.acp_prisonAreaInout_get,
                params: {
                    id: row.id
                }
            }).then(res => {
                if (res.success) {
                    this.djInfo = res.data
                    if (!res.data.jrjqaqjcrsfzh) {
                        this.$set(this.djInfo, 'jrjqaqjcrsfzh', '')
                    }
                    if (!res.data.lkjqaqjcrsfzh) {
                        this.$set(this.djInfo, 'lkjqaqjcrsfzh', '')
                    }
                    this.$forceUpdate()
                    this.openModalTimed = true
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        handleYjlfsjChange(val) {
            if (val) {
                // val 是字符串格式 "yyyy-MM-dd HH:mm:ss"
                const visitDate = new Date(val);
                visitDate.setHours(17, 0, 0, 0);
                const yjlssjStr = this.formatDate(visitDate);
                // this.formData.yjlssj = yjlssjStr;
                this.$set(this.formData, 'yjlssj', yjlssjStr)
            } else {
                this.formData.yjlssj = '';
            }
        },
        formatDate(date) {
            const pad = (n) => (n < 10 ? '0' + n : n);
            const year = date.getFullYear();
            const month = pad(date.getMonth() + 1);
            const day = pad(date.getDate());
            const hours = pad(date.getHours());
            const minutes = pad(date.getMinutes());
            const seconds = pad(date.getSeconds());
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        onCancelJr(data) {
            console.log(data, 'cancal进入');
        },
        onClearJr(data) {
            console.log(data, 'calear进入');
        },
        onSelectJr(data) {
            console.log(data, 'select进入');
        },
        onCancelLk(data) {
            console.log(data, 'cancal离开');
        },
        onClearLk(data) {
            console.log(data, 'calear离开');
        },
        onSelectLk(data) {
            console.log(data, 'select离开');
        },
        validateInGroup(rule, value, callback) {
            const { jrjqsj, jrjqaqjcxx, jrjqaqjcrsfzh } = this.djInfo;
            const anyFilled = jrjqsj || jrjqaqjcxx || jrjqaqjcrsfzh;
            if (anyFilled) {
                if (!jrjqsj) {
                    callback(new Error('进入监区时间为必填'));
                    return;
                }
                if (!jrjqaqjcxx) {
                    callback(new Error('安全检查信息为必填'));
                    return;
                }
                if (!jrjqaqjcrsfzh) {
                    callback(new Error('检查人为必填'));
                    return;
                }
            }
            callback();
        },
        validateOutGroup(rule, value, callback) {
            const { lkjqsj, lkjqaqjcxx, lkjqaqjcrsfzh, jrjqsj } = this.djInfo;
            const anyFilled = lkjqsj || lkjqaqjcxx || lkjqaqjcrsfzh;
            if (anyFilled) {
                if (!lkjqsj) {
                    callback(new Error('离开监区时间为必填'));
                    return;
                }
                if (!lkjqaqjcxx) {
                    callback(new Error('安全检查信息为必填'));
                    return;
                }
                if (!lkjqaqjcrsfzh) {
                    callback(new Error('检查人为必填'));
                    return;
                }
                // 时间顺序校验
                if (jrjqsj && lkjqsj < jrjqsj) {
                    callback(new Error('离开监区时间不能早于进入监区时间'));
                    return;
                }
            }
            callback();
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        },
    }
}
</script>

<style lang="less" scoped>
.list-title {
    line-height: 2.4;
    background: #f2f5fc;
    font-size: 14px;
    padding: 0 0.8em;
    font-weight: bold;
    border: 1px solid #CEE0F0;
    border-bottom: unset;
    margin-top: 16px;
}

/deep/.el-descriptions .is-bordered .el-descriptions-item__cell {
    text-align: center !important;
}

/deep/.ivu-modal-body {
    height: 500px !important;
    overflow: auto;
}

/deep/.el-input--prefix .el-input__inner {
    height: 32px !important;
}
</style>
<template>
    <div class="custom-dropdown">
        <!-- 触发下拉的输入框 -->
        <div class="dropdown-trigger" @click="toggleDropdown">
            <div v-for="item in selectedItemsName">{{ item }}</div>
        </div>

        <!-- 下拉选项列表 -->
        <ul v-show="isOpen" class="dropdown-options">
            <li v-for="(item, index) in options" :key="index" class="option-item">
                <!-- 自定义按钮 -->
                <Button type="primary"  @click="selectItem(item, 'normal')" class="btn-normal">正常</Button>
                <Button size="small" @click="selectItem(item, 'abnormal')" class="btn-abnormal">异常</Button>
                <span :title="item.label" class="label-name">{{ item.label }}</span>
            </li>
        </ul>
    </div>
</template>
<script>
export default {
    name: "cusSelect",
    props: {
        options: { type: Array, required: true },  // 格式: [{ value, label }]
        placeholder: { type: String, default: "请选择" },
        value: { type: [Array] }          // 通过v-model绑定
    },
    data() {

        return {
            isOpen: false,      // 控制下拉框显示
            selectedItems: [],   // 存储已选中的项（带状态）
            selectedItemsName: [],
        }

    },

    methods: {
        selectItem(item, status) {
            // 如果点击的是当前已选状态按钮 → 清除状态
            if (item.status === status) {
                item.status = null; // 清除状态标识
                this.selectedItems = this.selectedItems.filter(i => i.value !== item.value); // 从选中列表移除[5,7](@ref)
            } else {
                item.status = status;
                if (!this.selectedItems.some(i => i.value === item.value)) {
                    this.selectedItems.push({ ...item });
                }
            }
            this.$emit('input', this.selectedItems);
        },
        toggleDropdown() {
            this.isOpen = !this.isOpen;
        },
        handleClickOutside(event) {
            event.stopPropagation()
            // 确保下拉框已打开
            if (!this.isOpen) return;

            // 检查点击目标是否在下拉框内部
            const isInside = this.$el.contains(event.target);

            // 排除触发按钮本身
            const isTrigger = event.target.classList.contains('custom-dropdown');

            if (!isInside && !isTrigger) {
                this.isOpen = false;
            }
        }
    },

    components: {},

    created() { },
    mounted() {
        document.addEventListener("click", this.handleClickOutside);
    },
    beforeDestroy() {
        document.removeEventListener("click", this.handleClickOutside);
    },

    computed: {
        // selectedItemsName() {
        //     return this.selectedItems.map(item => {
        //         let items = `${item.label}(检查结果:${item.status == 'normal' ? "正常" : '异常'})`
        //         return items
        //     })
        // }
    },
    watch: {
        value: {
            immediate: true,
            handler(newVal) {
                this.selectLi = newVal
            }
        },
        selectedItems: {
            handler(newVal) {
                console.error(newVal);

                this.selectedItemsName = newVal.map(item => {
                    let items = `${item.label}(检查结果:${item.status == 'normal' ? "正常" : '异常'})`
                    return items
                });
                this.$emit('handleSelectNameList', this.selectedItemsName)
            }
        }
    }

}

</script>

<style scoped lang="less">
button {
    border: none;
    width: 60px;
    height: 30px;
    border-radius: 2px;
}

.btn-normal {
    background-color: #2390ff;
    color: #ffffff;
    /* 正常状态绿色 */
}

.btn-abnormal {
    background-color: #ffffff;
    border: solid 1px #2390ff;
    color: #2390ff;
    /* 异常状态红色 */
}

.custom-dropdown {
    position: relative;
    width: 100%;
    min-width: 600px;
}

.dropdown-trigger {
    min-height: 32px;
    border-radius: 4px;
    padding: 4px 7px;
    border: 1px solid #dcdee2;
    cursor: pointer;
    line-height: 1.5;
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    min-width: 600px;
    border: 1px solid #ddd;
    background: white;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .label-name {
        display: inline-block;
        flex: 1;
        min-width: 300px;
        /* 增加最小宽度 */
        white-space: nowrap;
        /* 禁止换行 */
        overflow: hidden;
        /* 隐藏溢出内容 */
        text-overflow: ellipsis;
        /* 溢出时显示省略号 */
        padding-left: 8px;
        line-height: 30px;
    }
}

.option-item {
    display: flex;
    align-items: center;
    padding: 8px;
    min-height: 46px;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: #f5f7fa;
    }
}

.option-item button {
    margin-right: 8px;
    cursor: pointer;
    flex-shrink: 0;
}
</style>

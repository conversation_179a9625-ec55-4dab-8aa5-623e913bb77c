<template>
    <div>
        <div class="bsp-base-form">
            <div class="bsp-base-tit">{{ modalTitle }}</div>
            <div class="bsp-base-content">
                <div class="content-left">
                    <Tabs v-model="choseFormType" v-if="action == 'add'">
                        <TabPane v-for="item in formType" :label="item.name" :name="item.value"></TabPane>
                    </Tabs>
                    <div v-else class="title">{{ templateYypeName }}</div>
                    <div class="form-tip" v-if="action == 'add'">添加人：{{ username }}</div>
                    <div class="content-left-form">
                        <Form v-show="choseFormType == 1" ref="formData1" :model="formData" :label-width="120"
                            :disabled="action == 'detail'" :label-colon="true" :rules="ruleValidate1">
                            <FormItem prop="templateName" label="模板名称">
                                <Input v-model="formData.templateName" maxlength="255" type="textarea" show-word-limit
                                    @on-focus="focusItem('templateName')" :ref="'templateName' + choseFormType"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="mainComplaint" label="主诉">
                                <Poptip trigger="click" placement="bottom" width="800" class="popContent" :transfer="false" :options="{modifiers: {preventOverflow: {enabled: false}, flip: {enabled: false}}}">
                                    <Input v-model="formData.mainComplaint" maxlength="500" type="textarea"
                                        @on-focus="focusItem('mainComplaint')" :ref="'mainComplaint' + choseFormType"
                                        show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                        placeholder="请填写"></Input>
                                    <div slot="content" class="popContent-inner">
                                        <div v-for="item in dicDataList.zs" class="popContent-inner-item"
                                            @click="addLabel(item.lable, 'mainComplaint')">
                                            {{ item.lable }}
                                        </div>
                                        <div class="line"></div>
                                        <div v-for="item in dicDataList.zszq" class="popContent-inner-item"
                                            @click="addLabels(item.lable, 'mainComplaint')">{{ item.lable }}</div>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="medicalHistory" label="病史">
                                <Poptip trigger="click" placement="bottom" width="800" class="popContent" :transfer="false" :options="{modifiers: {preventOverflow: {enabled: false}, flip: {enabled: false}}}">
                                    <Input v-model="formData.medicalHistory" maxlength="500" type="textarea"
                                        @on-focus="focusItem('medicalHistory')" :ref="'medicalHistory' + choseFormType"
                                        show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                        placeholder="请填写"></Input>
                                    <div slot="content" class="popContent-inner">
                                        <div v-for="item in dicDataList.jws" class="popContent-inner-item"
                                            @click="addLabel(item.lable, 'medicalHistory')">
                                            {{ item.lable }}
                                        </div>
                                        <div class="line"></div>
                                        <div class="lx">既往有</div>
                                        <div v-for="item in dicDataList.jwy" class="popContent-inner-item"
                                            @click="addLabelJWY(item.lable, 'medicalHistory')">
                                            {{ item.lable }}
                                        </div>
                                        <div class="line"></div>
                                        <div class="lx">个人史</div>
                                        <div v-for="item in dicDataList.grs" class="popContent-inner-item"
                                            @click="addLabel(item.lable, 'medicalHistory')">
                                            {{ item.lable }}
                                        </div>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="physicalCheck" label="体格检查">
                                <Poptip trigger="click" placement="bottom" width="800" class="popContent" :transfer="false" :options="{modifiers: {preventOverflow: {enabled: false}, flip: {enabled: false}}}">
                                    <Input v-model="formData.physicalCheck" maxlength="500" type="textarea"
                                        show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                        @on-focus="focusItem('physicalCheck')" :ref="'physicalCheck' + choseFormType"
                                        placeholder="请填写"></Input>
                                    <div slot="content" class="popContent-content">
                                        <Tabs v-model="tgjcYype" @on-click="choseTgjcTabs">
                                            <TabPane v-for="item in tgjcTabs" :label="item.name" :name="item.value">
                                            </TabPane>
                                        </Tabs>

                                        <div class="popContent-content-body">
                                            <div class="popContent-inner" v-if="tgjcYype == 'xyz'">
                                                <div class="lx">收缩压</div>
                                                <div v-for="item in dicDataList.ssy" class="popContent-inner-item"
                                                    @click="addLabel(choseTgjcTabsItem.name + item.lable, 'physicalCheck')">
                                                    {{ item.lable }}
                                                </div>
                                                <div class="line"></div>
                                                <div class="lx">舒张压</div>
                                                <div v-for="item in dicDataList.szy" class="popContent-inner-item"
                                                    @click="addLabels('/' + item.lable + choseTgjcTabsItem.dw, 'physicalCheck')">
                                                    {{ item.lable }}
                                                </div>
                                            </div>
                                            <div v-else-if="tgjcYype == 'tzl'" class="popContent-content-bodys">
                                                <div class="popContent-inner-lbTab">
                                                    <div @click="choseTgjcTabsItem.lb = 1" class="lb"
                                                        :class="{ 'lb-active': choseTgjcTabsItem.lb == 1 }">青少年(30~60)
                                                    </div>
                                                    <div @click="choseTgjcTabsItem.lb = 2" class="lb"
                                                        :class="{ 'lb-active': choseTgjcTabsItem.lb == 2 }">成年人(45~100)
                                                    </div>
                                                </div>
                                                <div class="popContent-inner">
                                                    <div v-for="item in choseTgjcTabsItem.lb == 1 ? dicDataList.qsntz : dicDataList.cnrtz"
                                                        class="popContent-inner-item"
                                                        @click="addLabel(choseTgjcTabsItem.name + item.lable + choseTgjcTabsItem.dw, 'physicalCheck')">
                                                        {{ item.lable }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-else-if="tgjcYype == 'sgl'" class="popContent-content-bodys">
                                                <div class="popContent-inner-lbTab">
                                                    <div @click="choseTgjcTabsItem.lb = 1" class="lb"
                                                        :class="{ 'lb-active': choseTgjcTabsItem.lb == 1 }">青少年(100~150)
                                                    </div>
                                                    <div @click="choseTgjcTabsItem.lb = 2" class="lb"
                                                        :class="{ 'lb-active': choseTgjcTabsItem.lb == 2 }">成年人(150~200)
                                                    </div>
                                                </div>
                                                <div class="popContent-inner">
                                                    <div v-for="item in choseTgjcTabsItem.lb == 1 ? dicDataList.qsnsg : dicDataList.cnrsg"
                                                        class="popContent-inner-item"
                                                        @click="addLabel(choseTgjcTabsItem.name + item.lable + choseTgjcTabsItem.dw, 'physicalCheck')">
                                                        {{ item.lable }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="popContent-inner" v-else>
                                                <div v-for="e in dicDataList[tgjcYype]" class="popContent-inner-item"
                                                    @click="addLabel(((choseTgjcTabsItem.value == 'tz' ? '' : choseTgjcTabsItem.name) + e.lable + (choseTgjcTabsItem.dw ? choseTgjcTabsItem.dw : '')), 'physicalCheck')">
                                                    {{ e.lable }}
                                                </div>
                                            </div>
                                            <div class="dw" v-if="choseTgjcTabsItem.dw">{{ choseTgjcTabsItem.dw }}</div>
                                        </div>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="auxiliaryCheck" label="辅助检查">
                                <Input v-model="formData.auxiliaryCheck" maxlength="500" type="textarea" show-word-limit
                                    @on-focus="focusItem('auxiliaryCheck')" :ref="'auxiliaryCheck' + choseFormType"
                                    :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="primaryDiagnosis" label="初步诊断">
                                <Input v-model="formData.primaryDiagnosis" maxlength="500" type="textarea"
                                    @on-focus="focusItem('primaryDiagnosis')" :ref="'primaryDiagnosis' + choseFormType"
                                    show-word-limit :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="suggestion" label="处理意见">
                                <Input v-model="formData.suggestion" maxlength="500" type="textarea" show-word-limit
                                    @on-focus="focusItem('suggestion')" :ref="'suggestion' + choseFormType"
                                    :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                        </Form>
                        <Form v-show="choseFormType == 2" ref="formData2" :model="formData" :label-width="120"
                            :disabled="action == 'detail'" :label-colon="true" :rules="ruleValidate2">
                            <FormItem prop="templateName" label="模板名称">
                                <Input v-model="formData.templateName" maxlength="255" type="textarea" show-word-limit
                                    @on-focus="focusItem('templateName')" :ref="'templateName' + choseFormType"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="mainComplaint" label="门诊主诉">
                                <Poptip trigger="click" placement="bottom" width="800" class="popContent" :transfer="false" :options="{modifiers: {preventOverflow: {enabled: false}, flip: {enabled: false}}}">
                                    <Input v-model="formData.mainComplaint" maxlength="500" type="textarea"
                                        @on-focus="focusItem('mainComplaint')" :ref="'mainComplaint' + choseFormType"
                                        show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                        placeholder="请填写"></Input>
                                    <div slot="content" class="popContent-inner">
                                        <div v-for="item in dicDataList.zs" class="popContent-inner-item"
                                            @click="addLabel(item.lable, 'mainComplaint')">
                                            {{ item.lable }}
                                        </div>
                                        <div class="line"></div>
                                        <div v-for="item in dicDataList.zszq" class="popContent-inner-item"
                                            @click="addLabels(item.lable, 'mainComplaint')">{{ item.lable }}</div>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="illnessResume" label="病情简述">
                                <Input v-model="formData.illnessResume" maxlength="500" type="textarea" show-word-limit
                                    @on-focus="focusItem('illnessResume')" :ref="'illnessResume' + choseFormType"
                                    :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                        </Form>
                        <Form v-show="choseFormType == 3" ref="formData3" :model="formData" :label-width="120"
                            :disabled="action == 'detail'" :label-colon="true" :rules="ruleValidate3">
                            <FormItem prop="templateName" label="模板名称">
                                <Input v-model="formData.templateName" maxlength="255" type="textarea" show-word-limit
                                    @on-focus="focusItem('templateName')" :ref="'templateName' + choseFormType"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="mainComplaint" label="主诉">
                                <Poptip trigger="click" placement="bottom" width="800" class="popContent" :transfer="false" :options="{modifiers: {preventOverflow: {enabled: false}, flip: {enabled: false}}}">
                                    <Input v-model="formData.mainComplaint" maxlength="500" type="textarea"
                                        @on-focus="focusItem('mainComplaint')" :ref="'mainComplaint' + choseFormType"
                                        show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                        placeholder="请填写"></Input>
                                    <div slot="content" class="popContent-inner">
                                        <div v-for="item in dicDataList.zs" class="popContent-inner-item"
                                            @click="addLabel(item.lable, 'mainComplaint')">
                                            {{ item.lable }}
                                        </div>
                                        <div class="line"></div>
                                        <div v-for="item in dicDataList.zszq" class="popContent-inner-item"
                                            @click="addLabels(item.lable, 'mainComplaint')">{{ item.lable }}</div>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="visitState" label="巡诊情况登记">
                                <Input v-model="formData.visitState" maxlength="500" type="textarea" show-word-limit
                                    @on-focus="focusItem('visitState')" :ref="'visitState' + choseFormType"
                                    :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                            <FormItem prop="visitConclusion" label="巡诊结论">
                                <Input v-model="formData.visitConclusion" maxlength="500" type="textarea"
                                    @on-focus="focusItem('visitConclusion')" :ref="'visitConclusion' + choseFormType"
                                    show-word-limit :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                            </FormItem>
                        </Form>
                    </div>
                </div>
                <div class="content-right" v-if="action != 'detail'">
                    <sybol @choseSymbol="choseSymbol"></sybol>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="returnBack">返 回</Button>
                <Button @click="submit" v-if="action != 'detail'">提 交</Button>
            </div>
        </div>

    </div>
</template>

<script>
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'
import sybol from "./sybol.vue";

export default {
    components: {
        sybol
    },
    props: {
        modalTitle: String,
        choseformTypeNum: String,
        templateYypeName: String,
        action: String,
        id: String
    },
    data() {
        return {
            username: getUserCache.getUserName(),
            choseFormType: "1",
            formType: [
                {
                    name: "医嘱模版",
                    value: '1'
                },
                {
                    name: "门诊登记模版",
                    value: '2'
                },
                {
                    name: "巡诊登记模版",
                    value: '3'
                }
            ],
            formData: {
                templateName: "",
                illnessResume: "",
                auxiliaryCheck: "",
                illnessResume: "",
                mainComplaint: "",
                medicalHistory: "",
                physicalCheck: "",
                primaryDiagnosis: "",
                suggestion: "",
                visitConclusion: "",
                visitState: ""
            },
            dicDataList: [],
            // 体格检查
            tgjcYype: "tz",
            tgjcTabs: [{
                name: "体征",
                value: "tz"
            },
            {
                name: "体温",
                value: "tw",
                dw: "℃"
            },
            {
                name: "脉搏",
                value: "mb",
                dw: "bpm"
            }, {
                name: "呼吸",
                value: "hx",
                dw: "次/分"
            }, {
                name: "血压",
                value: "xyz",
                dw: "mmHg"
            }, {
                name: "体重",
                value: "tzl",
                dw: "kg",
                lb: 1
            }, {
                name: "身高",
                value: "sgl",
                dw: "cm",
                lb: 1
            }, {
                name: "血氧",
                value: "xy",
                dw: "%"
            }, {
                name: "血糖",
                value: "xt",
                dw: "mmol/L"
            }],
            focusType: null,
            choseTgjcTabsItem: {
                name: "体征",
                value: "tz"
            },
            ruleValidate1: {
                templateName: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
                primaryDiagnosis: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
            },
            ruleValidate2: {
                templateName: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
                illnessResume: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
            },
            ruleValidate3: {
                templateName: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
                mainComplaint: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
                visitConclusion: [
                    { trigger: 'change,blur', message: '必填', required: true },
                ],
            }
        };
    },
    mounted() {
        this.getDicDataList()
        if (this.action != "add") {
            this.choseFormType = this.choseformTypeNum
            this.getTemplateDetail()
        }
    },
    methods: {
        ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
        // 获取模版详情数据
        getTemplateDetail() {
            this.authGetRequest({
                url: this.$path.get_template_xq,
                params: {
                    id: this.id,
                }
            }).then((resp) => {
                if (resp.success) {
                    this.formData = resp.data
                }
            });
        },
        // 获取pop中数据
        getDicDataList() {
            this.authGetRequest({
                url: this.$path.get_template_dic,
            }).then((resp) => {
                if (resp.success) {
                    this.dicDataList = resp.data
                }
            });
        },
        // 选中pop中的文字添加内容，用"，"分割
        addLabel(e, key) {
            this.formData[key] = this.formData[key] && this.formData[key] != '' ? this.formData[key].concat("，" + e) : this.formData[key].concat(e)
            this.$forceUpdate()
        },
        // 选中pop中的文字添加内容
        addLabels(e, key) {
            this.formData[key] = this.formData[key].concat(e)
            this.$forceUpdate()
        },
        // 病史中添加既往有文字
        addLabelJWY(e, key) {
            if (this.formData[key] && this.formData[key] != '') {
                if (this.formData[key].includes('既往有')) {
                    let data1 = this.formData[key].split('，')
                    let data2 = []
                    data1.forEach(item => {
                        if (item.includes('既往有')) {
                            item = item.concat('、' + e)
                            this.$forceUpdate()
                        }
                        data2.push(item)
                    })
                    this.formData[key] = data2.join('，')
                } else {
                    this.formData[key] = this.formData[key].concat("，既往有" + e)
                }
            } else {
                this.formData[key] = "既往有" + this.formData[key].concat(e)
            }
        },
        // 切换体格检查的tabs
        choseTgjcTabs(e) {
            this.choseTgjcTabsItem = this.tgjcTabs.filter(item => item.value == e)[0]
        },
        // 获取聚焦文本框类型
        focusItem(e) {
            this.focusType = e
        },
        // 向对应文本框中添加选择的符号
        choseSymbol(e) {
            if (this.focusType) {
                this.$refs[this.focusType + this.choseFormType].focus({
                    cursor: "end"
                });
                this.addLabels(e, this.focusType)
            }
        },
        // 提交
        submit() {
            let url = ""
            let params = {
                ...this.formData,
                templateType: this.choseFormType
            }
            if (this.action == 'add') {
                url = this.$path.create_template

            } else {
                url = this.$path.get_template_update
                params.id = this.id
            }
            this.$refs['formData' + this.choseFormType].validate((valid) => {
                if (valid) {
                    this.authPostRequest({
                        url,
                        params: params
                    }).then(res => {
                        if (res.success) {
                            this.$Notice.success({
                                title: '成功提示',
                                desc: "保存成功"
                            })
                            this.$Modal.remove()
                            this.returnBack()
                        } else {
                            this.$Notice.error({
                                title: '错误提示',
                                desc: res.msg
                            })
                        }
                    })
                }
            })
        },

        returnBack() {
            this.$emit("on_show_table")
        }
    },
};
</script>

<style lang="less" scoped>
.bsp-base-content {
    padding: 0 16px;
}

// 错误提示动画
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



.bsp-base-content {
    display: flex;

    .title {
        border-left: 4px solid #2d8cf0;
        padding-left: 8px;
        font-size: 16px;
        font-weight: 700;
        height: 20px;
        line-height: 20px;
        position: relative;
        margin-top: 16px;
    }

    .content-left {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        // 确保容器不会裁剪错误提示
        overflow: visible;

        /deep/ .ivu-tabs-tab {
            font-size: 16px;
        }

        .form-tip {
            line-height: 20px;
            font-size: 14px;
            color: #616f6f;
            text-align: right;
            margin: 16px 30px;
        }

        .content-left-form {
            flex: 1;
            overflow: auto;
            padding-right: 10px;

            // 确保表单项有足够空间显示错误提示
            /deep/ .ivu-form-item {
                margin-bottom: 24px !important;
                position: relative;
            }

            // 优化错误提示样式
            /deep/ .ivu-form-item-error-tip {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                line-height: 1.4;
                padding-top: 4px;
                color: #E60012 !important;
                font-size: 12px;
                z-index: 10;
                white-space: normal;
                overflow: visible;
                word-wrap: break-word;
                word-break: break-all;
                animation: fadeInUp 0.3s ease;
                max-width: 100%;
                display: block;
            }

            // 为包含文本域的表单项提供更多空间
            /deep/ .ivu-form-item:has(.ivu-input[type="textarea"]) {
                margin-bottom: 32px !important;
            }
        }

        /deep/.ivu-form-item-label {
            font-size: 14px;
        }

        /deep/.ivu-poptip {
            width: 100%;

            .ivu-poptip-rel {
                width: 100%;
            }
        }
    }

    .content-right {
        width: 336px;
        height: 100%;
        border-left: 1px solid #e8eef0;
        padding: 16px 0 16px 16px;
        overflow: hidden;
    }
}
</style>

<style lang="less">
.popContent {
    .popContent-inner {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        overflow-y: auto; /* 添加垂直滚动条 */
        max-height: 300px; /* 限制内容区域最大高度 */
        padding: 8px; /* 添加内边距 */
        box-sizing: border-box; /* 确保padding不影响总宽度 */

        .line {
            background: #e8eaec;
            height: 1px;
            width: 100%;
            margin-bottom: 10px;
        }

        .lx {
            width: 100%;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .popContent-inner-item {
            margin-right: 20px;
            margin-bottom: 10px;
            cursor: pointer;
            padding: 4px 8px; /* 添加内边距 */
            border-radius: 4px; /* 添加圆角 */
            transition: background-color 0.2s ease; /* 添加过渡效果 */

            &:hover {
                background-color: #f2f4f7; /* 悬停背景色 */
            }

            &:active {
                background-color: #e9f2fe; /* 点击时的背景色 */
            }
        }
    }

    .popContent-content {
        display: flex;
        flex-direction: column;
        max-height: 400px; /* 限制最大高度 */
        overflow: hidden; /* 隐藏外层溢出 */

        .popContent-content-body {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            overflow-y: auto; /* 添加垂直滚动条 */
            max-height: 350px; /* 为内容区域设置最大高度 */

            .dw {
                width: 10%;
                text-align: right;
                font-size: 20px;
                color: #c2c3c4;
            }

        }

        .popContent-content-bodys {
            display: flex;
            justify-content: space-between;
            flex: 1;
            overflow-y: auto; /* 添加垂直滚动条 */
            max-height: 350px; /* 为内容区域设置最大高度 */

            .popContent-inner-lbTab {
                margin-right: 10px;
                flex-shrink: 0; /* 防止在flex布局中被压缩 */
                min-width: 140px; /* 确保最小宽度 */

                .lb {
                    height: 32px;
                    padding: 0 8px;
                    border-radius: 4px;
                    width: 126px;
                    color: #000;
                    font-size: 16px;
                    line-height: 32px;
                    cursor: pointer;
                    margin-bottom: 5px;

                    &:hover {
                        background-color: #f2f4f7;
                    }
                }

                .lb-active {
                    background-color: #e9f2fe;
                }
            }


        }
    }

}
</style>

<style lang="less">
/* 全局样式：强制所有 Poptip 弹出框固定在底部 */
.ivu-poptip-popper[x-placement^="bottom"] {
    transform: none !important;
    margin-top: 8px !important;
}

.ivu-poptip-popper[x-placement^="top"] {
    transform: none !important;
    margin-bottom: 8px !important;
    /* 强制转换为底部显示 */
    top: auto !important;
    bottom: auto !important;
}

/* 禁用 Poptip 的自动翻转功能 */
.popContent .ivu-poptip-popper {
    transform: none !important;
}
</style>

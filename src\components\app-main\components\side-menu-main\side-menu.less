.side-menu-wrapper {
  height: 100%;
  user-select: none;
  // background: url('~@/assets/images/common/side-bg.png') no-repeat;
  // background-size: 100% 100%;
  // background: #203D80 url("~@/assets/images/bigpp.png") no-repeat center bottom/240px 250px;
  // 多级菜单的情况
  &.multilevel {
    .ivu-menu-dark {
      padding-top: 0px !important;
      background: none !important;
      .ivu-menu-opened {
        background: #1f3466 !important;
        .ivu-menu-submenu-title {
          background: #1f3466 !important;
          flex-wrap: wrap;
          flex-flow: inherit;
         
          
          &:hover {
            background-color: #224383 !important;
          }
        }
      }
      .ivu-menu-submenu-title:hover {
        background-color: #1F3466 !important;
      }
      .ivu-menu-submenu-title {
        height: 60px;
        max-height: 60px;
        flex-wrap: wrap;
        flex-flow: inherit;
        -webkit-box-align: left !important;
        padding-left: 16px;
        justify-content:flex-start !important;
        >i{
          font-size: 24px !important;
        }
      }
      .ivu-menu-item {
        padding: 5px;
        min-height: 50px;
        height: 50px;
        max-height: 60px;
        width: 240px;
        flex-wrap: wrap;
        flex-flow: inherit;
        -webkit-box-align: left !important;
        padding-left: 18px;
        justify-content:flex-start !important;
        >.ivu-icon{
          padding-right:5px;
        }
        > p {
          margin-left: 20px;
          text-align: left;
        }
        &.ivu-menu-item:hover {
          // background-color: #224383 !important;
          // background: rgba(49,127,245,0.2) !important;
          color: #fff;
        }
        &.ivu-menu-item-active {
          &::after {
            content: '';
            display: inline-block;
            width: 45px;
            height: 50px;
            // background: url('~@/assets/images/common/side-active.png') no-repeat;
            background-size: 100% 100%;
            position: absolute;
            left: 0;
            top: 0;
          }
        }
        &.ivu-menu-item-active,
        &.ivu-menu-item-active:hover {
          // background-color: #224383 !important;
          // background: rgba(49,127,245,0.2) !important;
          color: #fff;
          position: relative;
        }
      }
    }
  }
  .multilevel  {
    .ivu-menu-item,
    /deep/.ivu-menu-submenu-title{
      > i {  // iview 图标
        font-size: 24px !important;
    }
    }
  }
  // 单级菜单
  .ivu-menu {
    height: 100%;
    background: #1F3466;
    // background: #1F3466 url("~@/assets/images/ppside.png") no-repeat center bottom/104px 148px;
    &.ivu-menu-dark {
      padding-top: 15px;
    }
    .ivu-menu-item,
    .ivu-menu-submenu-title {
      font-size: 16px;
      font-weight: 400;
      color: #d6e2ff;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      .ivu-tooltip {
        text-align: center;
        .ivu-tooltip-inner {
          max-width: none;
        }
      }
      > i {  // iview 图标
        font-size: 32px;
        margin-right: 0px !important;
        margin-bottom: 5px;
      }
      .ivu-menu-submenu-title-icon {
        right: 10px;
      }
    }
    .ivu-menu-item {
      width: 104px;
      height: 104px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 12px;
      &.ivu-menu-item:hover {
        // background-color: #234a98 !important;
        // background: rgba(49,127,245,0.2) !important;
        color: #fff;
        .common-icon-box {
          background: rgba(32, 61, 128, 0);
        }
      }
      &.ivu-menu-item-active,
      &.ivu-menu-item-active:hover {
        // background: url('~@/assets/images/common/single-menu.png') no-repeat;
        background-size: 100% 100%;
        background-color: #203d80 !important;
        color: #fff;
        // font-weight: bold;
        position: relative;
        .common-icon-box {
          background: rgba(32, 61, 128, 0);
        }
      }
      .common-icon-box {
        background: rgba(32, 61, 128, 0.1);
      }
      > span {
        min-height: 35px;
        display: flex;
        align-items: center; 
      }
    }
    .activeRow {
      .ivu-menu-submenu-title {
        padding: 12px 18px;
        font-size: 16px;
        display: flex;
        flex-flow: nowrap !important;
        justify-content: left !important ;
        align-items: center !important;
        align-content: center;
        text-align: left !important;
      }
      .ivu-menu-item {
        font-size: 16px;
        display: flex;
        flex-flow: nowrap !important;
        justify-content: left !important ;
        align-items: center;
        text-align: left !important;
        // padding-left: 25px !important;
      }
      > .ivu-icon:first-child,
      .base-app .ivu-menu-vertical .activeRow .ivu-menu .ivu-menu-item > .ivu-icon:first-child {
        margin-top: 10px !important;
      }
      .ivu-icon,
      .base-app .ivu-menu-vertical .activeRow .ivu-menu .ivu-menu-item .ivu-icon {
        font-size: 20px;
        padding-right: 5px;
        margin-bottom: 0;
      }
    }
  }
  .menu-collapsed {
    padding-top: 10px;
    .ivu-dropdown {
      width: 100%;
      .ivu-dropdown-rel a {
        width: 100%;
      }
    }
    .ivu-tooltip {
      width: 100%;
      .ivu-tooltip-rel {
        width: 100%;
      }
      .ivu-tooltip-popper .ivu-tooltip-content {
        .ivu-tooltip-arrow {
          border-right-color: #fff;
        }
        .ivu-tooltip-inner {
          background: #fff;
          color: #495060;
        }
      }
    }
  }
  a.drop-menu-a {
    display: inline-block;
    padding: 6px 15px;
    width: 100%;
    text-align: center;
    color: #495060;
  }
}
.menu-title {
  padding-left: 6px;
}


.flex-left-title{
  display: flex;
    align-items: center;
    justify-content: flex-start;
}
import axios from '@/libs/api.request'
import Vue from 'vue'
import Qs from 'qs'
import {
  setToken,
  setSopToken,
  getLoginTime,
  setLoginTime,
  getToken,
  getRefreshToken,
  clear,
  clearSop,
  addUserCache,
  getUserCache,
  cachePermMenu,
  getPermMenu,
  setRootRouter,
  getRootRouter,
  filterAsyncRouter,
  cacheFuncPerm,
  getFuncPerm,
  addSopUserCache
} from '@/libs/util'
export default {
  state: {
    cityCode: getUserCache.getCityCode(),
    cityName: getUserCache.getCityName(),
    regCode: getUserCache.getRegCode(),
    regName: getUserCache.getRegName(),
    orgCode: getUserCache.getOrgCode(),
    orgName: getUserCache.getOrgName(),
    orgType: getUserCache.getOrgType(),
    userId: getUserCache.getUserId(),
    jobId: getUserCache.getJobId(),
    idCard: getUserCache.getIdCard(),
    userName: getUserCache.getUserName(),
    loginId: getUserCache.getLoginId(),
    expiresIn: getUserCache.getExpiresIn(),
    isAdmin: getUserCache.isAdmin(),
    isSAdmin: getUserCache.isSAdmin(),
    loginTime: getLoginTime(),
    avatarImgPath: '',
    refreshToken: getRefreshToken(),
    token: getToken(),
    access: '',
    hasGetInfo: false,
    routerList: getPermMenu(),
    funcPermList: getFuncPerm(),
    activeRootRouterName: getRootRouter(),
    interId: '',
    unreadCount: 0,
    requestFlag: false,
    platformName: 'esp',
    manageTag: true,
    menuMode: 'side'
  },
  mutations: {
    setManageTag(state, payload) {
      state.manageTag = payload
    },
    setMenuMode(state, payload) {
      state.menuMode = payload
    },
    setAvatar(state, avatarPath) {
      state.avatarImgPath = avatarPath
    },
    setUserId(state, id) {
      state.userId = id
    },
    setJobId(state, jobId) {
      state.jobId = jobId
    },
    setIdCard(state, idCard) {
      state.idCard = idCard
    },
    setUserName(state, name) {
      state.userName = name
    },
    setOrgId(state, orgId) {
      state.orgId = orgId
    },
    setIsAdmin(state, isAdmin) {
      state.isAdmin = isAdmin
    },
    setOrgName(state, orgName) {
      state.orgName = orgName
    },
    setLoginId(state, loginId) {
      state.loginId = loginId
    },
    setAccess(state, access) {
      state.access = access
    },
    setToken(state, token) {
      state.token = token
      setToken(token)
    },
    setLoginTime(state, time) {
      state.loginTime = time
      setLoginTime(time)
    },
    setHasGetInfo(state, status) {
      state.hasGetInfo = status
    },
    setRouteList(state, data) {
      let routerList = filterAsyncRouter(data)
      // //console.log(routerList,'routerList')
      cachePermMenu(routerList)
      state.routerList = routerList
      state.activeRootRouterName = data[0].code
      setRootRouter(data[0].code)
      // state.routerList = data
      // state.routerList = filterAsyncRouter(data)
      // state.activeRootRouterName = state.routerList[0].name
    },
    setActiveRootRouterName(state, data) {
      state.activeRootRouterName = data
      setRootRouter(data)
    },
    setFuncPerm(state, data) {
      state.funcPermList = data
      cacheFuncPerm(data)
    },
    setRequestFlag(state, data) {
      state.requestFlag = data
    },
    setInterId(state, data) {
      state.interId = data
    },
    setUserCache(state, data) {
      state.cityCode = data.cityCode
      state.regCode = data.regCode
      state.orgCode = data.orgCode
      state.cityName = data.cityName
      state.regName = data.regName
      state.orgName = data.orgName
      state.orgType = data.orgType
      state.userId = data.id || data.jobId
      state.jobId = data.jobId
      state.idCard = data.idCard
      state.userName = data.name || data.userName
      state.isAdmin = data.isAdmin
      state.loginId = data.loginId
      addUserCache(data)
    }
  },
  getters: {
    isAdmin: state => state.isAdmin,
    isSAdmin: state => state.isSAdmin,
    sessionUser: state => {
      return {
        userId: state.userId,
        idCard: state.idCard,
        loginId: state.loginId,
        cityCode: state.cityCode,
        regCode: state.regCode,
        orgCode: state.orgCode
      }
    }
  },
  actions: {
    // 下载请求方式，需传入返回类型
    downloadPostRequest({
      state
    }, {
      url,
      params,
      responseType
    }) {
      let _param = {
        access_token: state.token
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'post',
          url: `${url}`,
          data: params,
          params: _param,
          responseType
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    // 下载请求方式，需传入返回类型
    downloadGetRequest({
      state
    }, {
      url,
      params,
      responseType
    }) {
      let _param = {
        access_token: state.token
      }
      if (params) {
        Object.assign(_param, params)
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'get',
          url: `${url}`,
          params: _param,
          responseType
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    axiosPostRequest({
      state
    }, {
      url,
      params
    }) {
      return axios.request({
        method: 'post',
        url: `${url}`,
        data: Qs.stringify(params),
      })
    },
    axiosGetRequests({
      state,
      dispatch
    }, {
      url,
      params
    }) {
      let _param = {
        access_token: state.token
      }

      if (state.token) {
        if (params) {
          Object.assign(_param, params)
        }
      } else {
        _param = params
      }
      // //console.log(Object.assign(_param, params),_param,'Token监控：【token】' + state.token + '【refreshToken122222222】' + state.refreshToken)

      // return
      return new Promise((resolve, reject) => {
        dispatch('axiosGetRequest', {
          url: url,
          params: _param,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    axiosGetRequest({
      state
    }, {
      url,
      params
    }) {
      return axios.request({
        method: 'get',
        url: `${url}`,
        data: params, // Qs.stringify(params)
        params: params
      })
    },
    // 获取用户菜单和功能权限
    get_permission({
      state,
      commit,
      dispatch
    }) {
      return Promise.all([
        new Promise((resolve, reject) => {
          dispatch('authGetRequest', {
            url: Vue.path.get_user_menu,
            params: {
              appCode: serverConfig.APP_CODE
            }
          }).then(resp => {
            if (resp.data.length > 0) {
              commit('setRouteList', resp.data)
            } else {
              cachePermMenu([])
            }
            resolve(resp.data)
          })
        }),
        new Promise((resolve, reject) => {
          dispatch('authGetRequest', {
            url: Vue.path.get_user_oper,
            params: {
              appCode: serverConfig.APP_CODE
            }
          }).then(resp => {
            commit('setFuncPerm', resp.data)
            resolve(resp.data)
          })
        })
      ])
    },
    handleSingleSignOn({
      state,
      commit,
      dispatch
    }, {
      token
    }) {
      console.log(token, 'token')
      return new Promise((resolve, reject) => {
        dispatch('axiosPostRequest', {
          url: '/bsp-uac/oauth/check_token',
          params: {
            token: token
          }
        }).then(resp => {
          if (resp.status === 200) {
            let data = resp.data
            data.access_token = token
            dispatch('setting_session', data).then(() => {
              commit('setToken', token)
              resolve(resp.data)
            })

          } else {
            reject(resp)
          }
        })
      })
    },
    postRequest({
      state,
      dispatch
    }, {
      url,
      params
    }) {
      let _param = {
        access_token: state.token
      }
      if (params) {
        Object.assign(_param, params)
      }
      if (_param && _param.mark && url === Vue.path.get_executeMultiQuery && !_param.mark.startsWith(serverConfig.APP_CODE)) {
        _param.mark = serverConfig.APP_CODE + ':' + _param.mark
      }
      if (_param && _param.modelId && url === Vue.path.get_query_grid && !_param.modelId.startsWith(serverConfig.APP_CODE)) {
        _param.modelId = serverConfig.APP_CODE + ':' + _param.modelId
      }
      if (_param && _param.modelId && url === Vue.path.get_query_count_grid && !_param.modelId.startsWith(serverConfig.APP_CODE)) {
        _param.modelId = serverConfig.APP_CODE + ':' + _param.modelId
      }
      return new Promise((resolve, reject) => {
        dispatch('axiosPostRequest', {
          url: url,
          params: _param
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },

    // 认证中心post请求方法，不以表单形式提交
    authPostRequest({
      state
    }, {
      url,
      params
    }) {
      let _param = {
        access_token: state.token
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'post',
          url: `${url}`,
          data: params,
          params: _param
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    // 认证中心post请求方法，不以表单形式提交
    authPostRequestBlob({
      state
    }, {
      url,
      params
    }) {
      let _param = {
        access_token: state.token
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'post',
          url: `${url}`,
          data: params,
          params: _param,
          responseType: 'blob'
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    getRequest({
      state,
      dispatch
    }, {
      url,
      params
    }) {
      let _param = {
        access_token: state.token
      }

      if (state.token) {
        if (params) {
          Object.assign(_param, params)
        }
      } else {
        _param = params
      }
      // //console.log(Object.assign(_param, params),_param,'Token监控：【token】' + state.token + '【refreshToken122222222】' + state.refreshToken)

      // return
      return new Promise((resolve, reject) => {
        dispatch('axiosGetRequest', {
          url: url,
          params: _param
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    // 认证中心get请求方法
    authGetRequest({
      state
    }, {
      url,
      params
    }) {
      // //console.log('Token监控：【token】' + state.token + '【refreshToken】' + state.refreshToken)
      let _param = {
        access_token: state.token
      }
      if (params) {
        Object.assign(_param, params)
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'get',
          url: `${url}`,
          params: _param
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    authGetRequestBlob({
      state
    }, {
      url,
      params
    }) {
      // //console.log('Token监控：【token】' + state.token + '【refreshToken】' + state.refreshToken)
      let _param = {
        access_token: state.token
      }
      if (params) {
        Object.assign(_param, params)
      }
      return new Promise((resolve, reject) => {
        axios.request({
          method: 'get',
          url: `${url}`,
          params: _param,
          responseType: 'blob'
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },

    access_token({
      dispatch
    }, {
      userName,
      password,
      path
    }) {
      userName = userName.trim()
      password = password.trim()
      let params = {
        username: userName,
        password: password,
        client_id: 'user_client',
        client_secret: 'user_client',
        // scope: 'trust',
        grant_type: 'password'
      }
      return new Promise((resolve, reject) => {
        dispatch('axiosPostRequest', {
          url: path,
          params: params
        }).then(resp => {
          if (resp.status === 200) {
            resolve(resp.data)
          }
        })
      })
    },
    setting_session({
      commit
    }, data) {
      return new Promise((resolve, reject) => {
        commit('setToken', data.access_token)
        commit('setUserCache', data)
        resolve()
      })
    },
    // 获取用户相关信息
    getUserInfo({
      state,
      commit,
      dispatch
    }, {
      path
    }) {
      return new Promise((resolve, reject) => {
        let params = {
          access_token: state.token
        }
        dispatch('axiosPostRequest', {
          url: path,
          params: params
        }).then(resp => {
          if (resp.status === 200) {
            var data = resp.data
            commit('setToken', data.access_token)
            commit('setUserCache', data)
            resolve()
          }
        })
      })
    },
    // 退出登录
    handleLogOut({
      state,
      commit,
      dispatch
    }, {
      path
    }) {
      let param = {
        access_token: state.token
      }
      return new Promise((resolve, reject) => {
        dispatch('axiosPostRequest', {
          url: path,
          params: param
        }).then(resp => {
          if (resp.status === 200) {
            localStorage.removeItem('tagNaveList')
            // 强制刷新页面（可选）
            localStorage.removeItem('menuMode')
            localStorage.removeItem('usreInfo')
            localStorage.removeItem('orgType')
            clear()
            resolve()
          }
        })
      })
    }
  }
}

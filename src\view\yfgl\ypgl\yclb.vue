<template>
  <div>
    <div class="ypcgFlex">
      <p>
        <span><i class="total">入库人：</i></span><span><i class="total">{{
          userName
        }}</i></span>
      </p>
      <div class="ts">以下药品数据导入时出现异常，请手动确认</div>
    </div>
    <div style="padding: 0 16px">
      <Table :key="tableKey" :columns="columnsInfo" :data="dataTable" height="590" tooltip-theme="light"
             :tooltip-max-width="300" border>

        <template slot-scope="{ row,index }" slot="sl">
          <Input v-model.number="row.sl" @on-change="changeItem(index, row)" placeholder="请输入"
                 type="number"></Input>
        </template>


        <template slot-scope="{ row,index }" slot="expireDate">
          <DatePicker @on-change="changeDateItem($event, index, row)" v-model="row.expireDate" type="datetime"
                      format="yyyy-MM-dd" placeholder="请选择">
          </DatePicker>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <Button type="error" size="small" @click="remove(row, index, 'del')">删除</Button>
        </template>
      </Table>
    </div>

    <div class="bsp-base-fotter" style="left: 0px">
      <Button @click="on_return_table">返 回</Button>
      <Button type="primary" @click="handleSubmit" :loading="spinShow">保 存</Button>
    </div>
  </div>

</template>

<script>
import {mapActions} from "vuex";
import {getUserCache} from '@/libs/util'


export default {
  components: {},
  props: {
    modalTitle: String,
    yclbData: Object,
    syncAction: String
  },
  data() {
    return {
      spinShow: false,
      dicData: null,
      userName: getUserCache.getUserName(),
      paramentData: {},
      rowData: {},
      tableKey: Math.random(),
      columnsInfo: [
        {
          title: '药品基本信息',
          align: 'center',
          children: [{
            type: "index",
            width: 80,
            align: "center",
            title: "序号",
          },
            {
              title: "批准文号",
              key: "approvalNum",
              align: "center",
              tooltip: true
            },
            {
              title: "药品名称",
              key: "medicineName",
              align: "center",
              tooltip: true
            },
            {
              title: "剂型",
              align: "center",
              key: "dosageForm"
            },
            {
              title: "规格",
              key: "specs",
              align: "center",
              tooltip: true
            },
            // {
            //   title: "类型",
            //   key: "specs",
            //   align: "center",
            // },
            {
              title: "生产单位",
              key: "productUnit",
              align: "center",
              tooltip: true
            },]
        },
        {
          title: '入库信息',
          align: 'center',
          children: [
            {
              title: "数量",
              slot: "sl",
              align: "center",
            },
            {
              title: "有效期至",
              slot: "expireDate",
              align: "center",
              width: 200,
            },
            // {
            //   title: "生产日期",
            //   type: "num",
            //   align: "center",
            // }
          ]
        },
        {
          title: "错误信息",
          key: "errMsg",
          align: "center",
          tooltip: true
        },

        {
          title: "操作",
          slot: "action",
          align: "center",
        },
      ],
    };
  },
  mounted() {
    // console.log(this.yclbData,888)
  },
  computed: {
    dataTable() {
      return this.yclbData.medicineInImportVOErrorList
    }
  },
  methods: {
    ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
    changeItem(index, row) {
      this.$set(this.dataTable, index, row)
    },
    changeDateItem(e, index, row) {
      row.expireDate = e
      this.$set(this.dataTable, index, row)
    },
    async handleSubmit() {
      this.yclbData.medicineInImportVOErrorList = this.dataTable
      this.$store
        .dispatch("authPostRequest", {
          url: this.$path.add_yclb_in,
          params: this.yclbData,
        })
        .then((resp) => {
          console.log(resp);
          if (resp.success) {
            this.spinShow = false;
            this.$Notice.success({
              title: "提示",
              desc: resp.data || "保存成功",
            });
            this.on_return_table();
          } else {
            this.spinShow = false;
            this.$Notice.error({
              title: "错误提示",
              desc: resp.msg || "保存失败",
            });
          }
        });
    },
    on_return_table() {
      this.$emit("on_show_table");
    },
    remove(row, index, tag) {
      this.$Modal.confirm({
        title: "是否确认删除该条数据？",
        loading: true,
        onOk: async () => {
          this.dataTable.splice(index, 1);
          this.$Modal.remove();
        },
      });
    },
  },
};
</script>

<style scoped>
.ypcgFlex {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: space-between;
}


i {
  font-style: normal;
}

.total {
  font-weight: bold;
  padding: 0 5px;
  margin-bottom: 10px;
}

.ts {
  font-size: 16px;
  color: rgb(239, 49, 49);
  text-align: center;
}

.editBox .ivu-input {
  border: none;
  border-bottom: 1px solid rgb(127, 127, 238);
  background: transparent !important;
  border-radius: 0 !important;
  text-align: center;
}

.editBox:focus-visible {
  outline: none !important;
}

.bsp-base-fotter {
  box-shadow: none;
  position: relative;
}
</style>

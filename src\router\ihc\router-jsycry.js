let menuMode = localStorage.getItem('menuMode')
// import main from '@/components/app-main/index.vue'
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'

export default [
  {
    path: '/jsyc',
    name: 'jsyc',
    meta: {
      title: '精神病异常管理'
    },
    redirect: '/jsyc/index',
    component: menuMode == 'side' ? mainNew : main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'index',
        name: 'index',
        meta: {
          title: '精神病异常管理',
          menu: true,
          bread: true
        },
        component: () => import('@/view/snjy/jsycry/index')
      }
    ]
  }
]

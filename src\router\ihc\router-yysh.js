/*
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-07-10 17:13:34
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-07-10 17:27:41
 * @FilePath: \rs-acp-web\src\router\ihc\router-jkjc.js
 * @Description: 健康检测的路由
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
let menuMode = localStorage.getItem('menuMode')
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/appointmentReview',
    name: 'appointmentReview',
    meta: {
      title: '预约审核'
    },
    redirect: '/appointmentReview/index',
    component: menuMode=='side'?mainNew:main,
    children: [
      {
        path: 'index',
        name: 'index',
        meta: {
          title: '预约审核',
          menu: true,
          bread: true
        },
        component: () => import('@/view/appointmentReview/index.vue')
      }
    ]

  }
]



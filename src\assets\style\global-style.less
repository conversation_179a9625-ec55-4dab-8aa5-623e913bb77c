body,
td,
th {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-size: 15px;
}

@import url("~@/assets/style/detail.less");
@import url("~@/assets/style/formInfo.css");
@import url("~@/assets/style/formForm.css");

// @import url("~@/assets/style/dzjz_common.less");
// @bsp-base-fotter-left:200px;
:root {
  --bsp-base-fotter-left: 200px;
  --bsp-base-fotter-left-new: 0px !important;
}

.content-wrap-main-right .bsp-base-fotter {
  left: var(--bsp-base-fotter-left-new) !important;
}

/**表单样式*/
.data-content {
  top: 65px !important;
}

.data-content2 {
  top: -15px !important;
}

.bsp-base-form {
  position: absolute;
  bottom: 0px;
  top: 0px;
  left: 0px;
  right: 0px;
  box-sizing: border-box;
}

.ivu-modal-close {
  top: 5px;
}

.bsp-base-form .bsp-base-tit {
  height: 48px;
  color: #2B3346;
  box-sizing: border-box;
  font-size: 18px;
  padding: 0 15px;
  // margin: 0 15px;
  line-height: 48px;
  // background: linear-gradient( 180deg, #E6F6FF 0%, rgba(230,246,255,0.3) 100%) !important;
  font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
  border-bottom: 1px solid #cee0f0;
}

.bsp-base-form .bsp-base-content {
  overflow: auto;
  position: absolute;
  bottom: 45px;
  left: 15px;
  right: 15px;
  top: 48px; //25px;
  background-color: #ffffff;
}

.bsp-base-form .bsp-base-content .form {
  margin: 15px;
}


.bsp-base-form .ivu-form .ivu-form-item-label {
  font-size: 16px;
  color: #2b3646;
  padding: 10px 8px;
}

.bsp-base-form .ivu-form-item-error .ivu-input,
.bsp-base-form .ivu-form-item-error .el-input__inner {
  border-color: #e60012;
}

.bsp-base-form .ivu-input {
  height: 30px;
  font-size: 16px;
  color: #3d4e66;
  border-color: #cee0f0;
  padding: 2px 7px 0px 7px;
  line-height: 30px;
}

.bsp-base-form .ivu-input-with-suffix {
  padding-right: 32px;
}

.bsp-base-form .ivu-input-type-number .ivu-input {
  padding-right: 0px;
}

.bsp-base-form .ivu-input:focus {
  border-color: #337bf5;
  box-shadow: inset 0 0 0 1000px #FFFFFF !important;
}

.bsp-base-form .ivu-form-item-error-tip {
  padding-top: 2px;
  color: #E60012;
}

.bsp-base-form .ivu-form-item {
  margin-bottom: 20px;
}

.bsp-base-form .ivu-checkbox-inner,
.bsp-base-form .ivu-radio-inner {
  border-color: #cee0f0;
}

.bsp-base-form .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #2b5fda;
  background-color: #2b5fda;
}

.bsp-base-form .ivu-checkbox-wrapper,
.bsp-base-form .ivu-radio-wrapper {
  font-size: 16px;
  color: #3d4e66;
}

.bsp-base-form .ivu-checkbox-group {
  margin-top: 6px;
}

.bsp-base-form .ivu-checkbox-group,
.bsp-base-form .ivu-radio-group {
  line-height: 26px;
}

.bsp-base-form .ivu-radio-inner:after {
  background-color: #2b5fda;
}

.bsp-base-form .ivu-radio-checked .ivu-radio-inner {
  border-color: #2b5fda;
}

/**日期框样式重置 */
.bsp-base-form .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
  font-size: 16px;
  color: #3d4e66;
}

.bsp-base-form .el-input--small .el-input__inner:focus {
  border-color: #337bf5;
  box-shadow: inset 0 0 0 1000px #FFFFFF !important;
}

.bsp-base-form .el-input__inner {
  border-color: #cee0f0;
}

.bsp-base-form .el-input__prefix {
  color: #2b5fda;
}

.bsp-base-form .el-input--small .el-input__prefix .el-input__icon {
  line-height: 28px;
  font-size: 17px;
}

/*下拉框样式重置 */
.bsp-base-form .ivu-select-selection {
  border-color: #cee0f0;
}

.bsp-base-form .ivu-select-selection:focus {
  border-color: #337bf5;
  box-shadow: inset 0 0 0 1000px #FFFFFF !important;
}

.bsp-base-form .ivu-select-single .ivu-select-selection {
  height: 30px;
}

.bsp-base-form .ivu-form-item-error .ivu-select-selection {
  border-color: #e60012;
}

.bsp-base-form .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.bsp-base-form .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  font-size: 16px;
  color: #3d4e66;
}

.bsp-base-form .ivu-select-item {
  font-size: 16px !important;
}

.bsp-base-form .ivu-select-dropdown {
  box-shadow: 0px 0px 3px 0px rgba(84, 135, 255, 1);
}

.bsp-base-form .ivu-select-item-focus,
.bsp-base-form .ivu-select-item-focus:hover {
  background: #628ef5 !important;
  color: #fff !important;
}

.bsp-base-form .ivu-select-item:hover {
  background: #f0f4ff;
  color: #515a6e;
}

.bsp-base-form div.v-selectpage div.sp-input-container.sp-open div.sp-input,
.bsp-base-form div.v-selectpage div.sp-input-container.sp-open div.sp-inputs {
  border-color: #337bf5;
}

.bsp-base-form div.v-selectpage div.sp-input-container div.sp-base {
  border-color: #cee0f0;
}

div.v-dropdown-container.v-dropdown-no-border {
  box-shadow: 0 0 3px 0 #337bf5 !important;
}


.bsp-base-form .form-btn {
  font-size: 14px;
  line-height: 1;
  min-width: 80px;
  height: 28px;
  padding: 0 5px;
  border-radius: 2px;
}

.bsp-base-form .form-btn.primary {
  background: #2b5fd9;
}

.bsp-base-form .form-btn.success {
  background: #11C28A;
}

.pure-content .bsp-base-fotter {
  left: 0;
}

.bsp-base-fotter {
  position: fixed;
  display: flex;
  height: 56px;
  right: 0px;
  // left: 0px;
  left: var(--bsp-base-fotter-left);
  bottom: 0px;
  background-color: #ffffff;
  align-items: center;
  justify-content: center;
  box-shadow: 0px -2px 4px 0px rgba(208, 211, 217, 0.60);
  z-index: 999;
}


/**表单按钮重置 */
.bsp-base-fotter .ivu-btn-primary {
  font-size: 16px;
  min-width: 80px;
  height: 36px;
  opacity: 1;
  background: #2b5fd9;
  border-radius: 2px;
  margin: 0 20px;
}

.bsp-base-fotter .cus-buttom {
  font-size: 16px;
  min-width: 80px;
  height: 36px;
  opacity: 1;
  border-radius: 2px;
  margin: 0 20px;
}

.bsp-base-fotter .ivu-btn-default {
  font-size: 16px;
  width: 80px;
  height: 36px;
  border-radius: 2px;
  margin: 0 20px;
}

.bsp-base-fotter .ivu-btn-error {
  font-size: 16px;
  width: 80px;
  height: 36px;
  border-radius: 2px;
  margin: 0 20px;
}

.bsp-base-small-fotter {
  text-align: center;
}

.bsp-base-small-fotter .ivu-btn-primary {
  font-size: 16px;
  min-width: 70px;
  height: 30px;
  opacity: 1;
  background: #2b5fd9;
  border-radius: 2px;
  margin: 0 15px;
}

.bsp-base-small-fotter .ivu-btn-default {
  font-size: 15px;
  width: 70px;
  height: 30px;
  border-radius: 2px;
  margin: 0 15px;
}

.bsp-scroll {
  overflow: auto;
}

.csqk-box::-webkit-scrollbar {
  background: transparent !important;
  width: 10px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;
  margin-left: 6px;
}

.csqk-box::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #b7c7dd;
}

// .csqk-box::-webkit-scrollbar-track{
//   border-radius: 3px;
//   background: #b7c7dd;
// }
.side-menu-wrap::-webkit-scrollbar {
  display: none !important;
}

.bsp-base-content::-webkit-scrollbar,
.main-content::-webkit-scrollbar,
.tree-box::-webkit-scrollbar,
.bsp-scroll::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 10px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;

}

.bsp-base-content::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb,
.tree-box::-webkit-scrollbar-thumb,
.bsp-scroll::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 3px;
  background: #b7c7dd;

}

.bsp-base-content::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track,
.tree-box::-webkit-scrollbar-track,
.bsp-scroll::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 3px;
  background: #EDEDED;
}


.bsp-base-form .ivu-table th {
  background: #DEE9FC;
}

.bsp-base-form .ivu-table-border th,
.ivu-table-border td {
  border-right: 1px solid #f5f4f4;
}


.bsp-base-form .base-left-content,
.bsp-base-form .base-left-content2 {
  position: absolute;
  bottom: 0px;
  left: 15px;
  right: 0px;
  top: 48px;
  background-color: #fff;
  width: 345px;
  border-right: 1px solid #CEE0F0;
}

.bsp-base-form .base-left-flex {
  border-right: 1px solid #CEE0F0;
  overflow: hidden;
}

.bsp-base-form .base-left-flex .tree-box {
  overflow: auto;
  padding: 0 15px;
}

.bsp-base-form .base-left-flex .ivu-tree ul {
  font-size: 16px;
}

.bsp-base-form .base-left-flex .ivu-tree .ivu-checkbox-wrapper {
  margin-right: 0px;
}

.bsp-base-form .base-left-flex .ivu-tree .ivu-tree-arrow i {
  font-size: 17px;
}

.bsp-base-form .base-left-content .search-box,
.bsp-base-form .base-left-flex .search-box,
.bsp-base-form .base-left-content2 .search-box {
  height: 48px;
  background: #F5FAFF;
  line-height: 48px;
  padding: 0px 16px;
}

.bsp-base-form .base-left-flex .oper-box {
  height: 40px;
  text-align: right;
  background: #F5FAFF;
  line-height: 40px;
  padding: 0px 16px;
}

.bsp-base-form .base-left-content .main-content,
.bsp-base-form .base-left-content2 .main-content {
  position: absolute;
  bottom: 0px;
  top: 84px;
  width: 100%;
  padding: 8px 0px;
  overflow: overlay;
  white-space: nowrap;
}

.bsp-base-form .base-left-content ul {
  width: fit-content;
}

.bsp-base-form .base-left-content li {
  list-style: none;
  height: 36px;
  min-width: 294px;
  line-height: 36px;
  font-size: 16px;
  padding: 0px 10px;
  cursor: pointer;
  border-left: 4px solid #FFF;
}

.bsp-base-form .base-left-content li.active,
.bsp-base-form .base-left-content li.active:hover {
  border-left: 4px solid #2B5FDA;
  background: #EBF1FF;
}

.bsp-base-form .base-left-content li:hover {
  background: #EBF1FF;
  border-left: 4px solid #EBF1FF;
}

.bsp-base-form .base-left-content li.h48 {
  height: 48px;
  line-height: 48px;
}

.bsp-base-form .base-left-content li.h42 {
  height: 42px;
  line-height: 42px;
}

.bsp-base-form .bsp-base-subtit {
  border: 1px solid #CEE0F0;
  margin-top: -1px;
}

.bsp-base-form .bsp-base-subtit .subtit {
  background: #F2F6FC;
  font-size: 17px;
  border-bottom: 1px solid #CEE0F0;
  padding: 1px 15px 0px 15px;
  line-height: 35px;
}

.bsp-base-form .bsp-base-subtit.no-border {
  border-width: 0px !important;
  margin-top: 0px !important;
}

.bsp-base-form .bsp-base-subtit .no-border {
  border-width: 0px !important;
  margin-top: 0px !important;
}

/**表单现象卡重置**/
.bsp-base-form .ivu-tabs-bar {
  margin-bottom: 5px;
  border-bottom-color: #CEE0F0;
}

.bsp-base-form .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab {
  border: none;
  background: none;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  padding: 0;
  margin: 0 32px 0 0;
  color: #333333;
}

.bsp-base-form .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active {
  position: relative;
  padding: 0;
  line-height: 32px;
  color: #2B5FD9;
  font-weight: bold;
}

.bsp-base-form .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active::after {
  content: " ";
  position: absolute;
  width: 76px;
  height: 4px;
  background: #2B5FD9;
  border-radius: 4px;
  top: 100%;
  left: 50%;
  margin-left: -38px;
  margin-top: 0px;
}

.bsp-base-form .ivu-tabs-nav-scroll,
.bsp-base-form .ivu-tabs-nav-wrap,
.bsp-base-form .ivu-tabs-nav-container {
  overflow: initial;
}

.bsp-base-form .ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-nav-container {
  height: 38px;
  line-height: 38px;
  padding: 0 22px;
}

/**表单现象卡重置结束**/

/***表单样式结束**/



/***列表样式开始**/
.bsp-list-form-container {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  margin-bottom: 15px;
  background-color: #ffffff;
  box-sizing: border-box;
  // box-shadow:0px 0px 5px #dedede;
}

.bsp-list-container {
  background: #ffffff;
  padding: 15px;
  padding-bottom: 30px;
}

.bsp-list-form-container .com-button {
  opacity: 1;
  font-size: 16px;
  border-radius: 2px;
}

.bsp-list-form-container .header-button {
  opacity: 1;
  background: #2b5fd9;
  font-size: 16px;
  border-radius: 2px;
}

.bsp-list-form-container .row-button {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  margin-left: 4px;
}

.bsp-list-form-container .BOX .ivu-table th,
.bsp-list-form-container .ivu-table td {
  height: 40px;
}

.bsp-base-form .ivu-page-options-sizer .ivu-select-small.ivu-select-single .ivu-select-selection {
  height: 24px;
}

.bsp-base-form .ivu-page-options-sizer .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.bsp-base-form .ivu-page-options-sizer .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  font-size: 15px;
}

.bsp-base-form .ivu-page-options-sizer .ivu-select-item {
  font-size: 15px !important;
}

.ivu-modal-header {
  padding: 0 !important;
  height: 40px;
  background: #2b5fda;
  width: 100%;
  color: #fff;
  line-height: 40px;
}

.flow-modal-title,
.ivu-modal-header-inner {
  height: 40px;
  background: #2b5fda;
  width: 100%;
  text-indent: 1em;
  color: #fff;
  line-height: 40px;
}


/*----------------reset-modal  重置弹窗样式----------------*/
.reset-modal {
  .ivu-modal-header {
    background: #2b5fda;

    p {
      color: #fff;
      font-size: 16px;
    }
  }

  .ivu-modal-close {
    .ivu-icon-ios-close {
      color: #fff !important;
    }
  }

  .ivu-modal-footer {
    // background: #f7faff;
    border-top: 1px solid #e8eaec;
    padding: 12px 18px 12px 18px;
    text-align: right;
    // border-color: #CEE0F0;

  }
}

.reset-table-no-bottom-border {
  .ivu-table td {
    border-bottom: none;
  }

  .ivu-table:before {
    height: 0;
  }
}

.ivu-btn {
  height: 30px;
}

.ivu-btn-large {
  height: 40px !important;
}

.ivu-btn-primary {
  border: none;
}


.ivu-menu-submenu.ivu-menu-item-active.ivu-menu-child-item-active .ivu-menu-submenu-title {
  //  color: #2B5FD9 !important;
  background: #203d80 !important;

  span {
    background: #203d80 !important;
    // color: #2B5FD9 !important;
  }
}


.ivu-modal-body {
  // padding: 16px 16px 0 16px !important;
  padding: 16px 16px 0 16px;
}

.ivu-btn,
.DataGrid-BOX .button,
.DataGrid-BOX .button1 {
  font-family: MicrosoftYaHei, MicrosoftYaHei !important;
  font-weight: normal !important;
  font-size: 16px !important;
  border-radius: 4px !important;
}

// 表格样式
.ivu-table-header thead tr th {
  background: #E4EEFA;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #E9EDF5;
  font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
  font-weight: 700;
  font-size: 16px;
  color: #5F709A;
}

.ivu-table-tbody tr td {
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal;
  color: #5F709A;
}

.searchBox .ivu-form-item-label {
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal;
  font-size: 16px;
  color: #2B3346;
}

.bsp-base-form .user-wrap .ivu-form-item {
  margin-bottom: 10px !important;
}

.bsp-base-form .ivu-form-item {
  margin-bottom: 14px !important;
}

.bsp-base-form .ivu-form-item .ivu-form-item-content {
  line-height: 10px !important;
}

.searchBox .ivu-form-item {
  margin-bottom: 14px !important;
}



// 表单样式
.form-box {
  border: 1px solid #cee0f0;
  border-top: none;

  .fm-content-wrap {
    border: none;
  }
}

.bLeft {
  border-left: 1px solid #cee0f0;
}

.bg-title {
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN;
  font-weight: 700;
  font-size: 16px;
  color: #00244a;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #cee0f0;
  margin: 0px !important;
  border-right: 1px solid #cee0f0;
  text-indent: 0 !important;
}

.fm-content-box {
  border: 1px solid #cee0f0 !important;
  padding-top: 0px !important;
}

.fm-content-wrap {
  border: 1px solid #cee0f0;
  padding-top: 16px;
  border-top: none;
  // border-bottom: none;
  padding-right: 16px;
}

.fm-box {
  padding: 0px 8px 0 16px
}

.bg-titleA {
  color: #00244a;
  line-height: 40px;
  background: #eff6ff;
  padding: 0 10px;
  border-left: 4px solid #2d8cf0;
}

.TimelineItem {
  margin-top: 16px;
  padding: 0 16px;
}

.ivu-timeline-item-content {
  top: -8px;
}

.bsp-base-form .ivu-radio-group {
  line-height: 36px;
}

.user-wrap-left {
  border-left: 1px solid #cee0f0;
  border-bottom: 1px solid #cee0f0;
  height: 92.3%;
  margin-left: 16px;
}

.userImg {
  margin: auto 10px;
  width: 88px;
  height: 110px;
}

.dy {
  line-height: 36px;
}

.ivu-table-fixed-header {
  margin-top: 1px;
}

.ivu-table-fixed-header thead tr th {
  padding: 9px 0;
}

.sdInfo.line24 .sp-base.sp-input {
  line-height: 19px !important;
  padding: 6px 0px !important;
  color: #515a6e !important;
}

.sdInfo .sp-button,
.sp-clear {
  display: none !important;
}

.sdInfo .sp-base.sp-input {
  border: none !important;
  line-height: 36px !important;
  color: #1F2533 !important;
  background: transparent !important;
  padding: 6px 0px !important;
}

.ryInfo {
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出范围的内容 */
  text-overflow: ellipsis;
  /* 使用省略号 */
}

.textOverflow {
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出范围的内容 */
  text-overflow: ellipsis;
  /* 使用省略号 */
}

/deep/ .idContain {
  overflow: hidden !important;
}

.author-menu-user {
  width: 88px;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu.ivu-menu-opened ::v-deep .ivu-menu-submenu-title {
  background: #1f3466 !important;
}

.ivu-menu-submenu {
  background: #1f3466 !important;
}

.home-title {
  padding: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 26px;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  border-bottom: 1px solid #E9EDF5;
  ;
}

.home-com {
  border-radius: 6px;
  height: 100%;
}

.ivu-table-tbody .ivu-tag-text {
  font-size: 14px !important;
}


.bsp-table-layout {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;

  .bsp-table-wrapper {
    padding: 20px;
  }
}

.bsp-table-btn {
  font-size: 14px;
  cursor: pointer;
  color: #2D8cF0;

  &.btn-danger {
    color: #ed4014;
  }

  &.disabled {
    pointer-events: none;
    color: #C0C4CC;
  }

  &+.bsp-table-btn {
    margin-left: 6px;
  }
}

.bsp-table-hbtn {
  &+.bsp-table-hbtn {
    margin-left: 10px;
  }
}


.bsp-modal-container {
  .bsp-modal-form {
    position: relative;
  }
}

.bsp-modal-submit {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  button {
    margin: 0 8px;
  }
}


.bsp-card-box {
  .bsp-common-card {
    display: inline-block;
    margin: 16px 0 0 16px;
  }
}

.pointer {
  cursor: pointer;
}


@gridList: 2, 3, 4, 5, 6;

.bsp-grid-form-loop(@list, @i: 1, @val: extract(@list, @i)) when (length(@list)>=@i) {
  .bsp-grid-form-@{val} {
    display: grid;
    grid-template-columns: repeat(@val, 1fr);
    border-top: 1px solid #CEE0F0;
    border-left: 1px solid #CEE0F0;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      border-bottom: 1px solid #CEE0F0;
    }

    .ivu-form-item {
      margin-bottom: 0px !important;
      border-bottom: 1px solid #CEE0F0;
      border-right: 1px solid #CEE0F0;
    }

    .ivu-form-item-label {
      background: #f2f5fc;
      text-align: right;
      padding-right: 16px;
      color: #515a6e;
      font-size: 16px;
      border-right: 1px solid #CEE0F0;
    }

    .ivu-form-item-content {
      padding-left: 16px;
      line-height: 22px;
      color: #1F2533;
      font-size: 16px;
      background: transparent;
      padding: 6px 0px 6px 16px;
    }
  }

  .bsp-grid-form-loop(@list, (@i+1));
}

.bsp-grid-form-loop(@gridList);

@gridList: 2, 3, 4, 5, 6;

.bsp-grid-item-loop(@list, @i: 1, @val: extract(@list, @i)) when (length(@list)>=@i) {
  .bsp-grid-item-@{val} {
    grid-column-start: span @val,
  }

  .bsp-grid-item-loop(@list, (@i+1));
}

.bsp-grid-item-loop(@gridList);

.cancle_btn {
  min-width: 60px;
  height: 30px;
  background: #ffffff;
  border: 1px solid #2b5fd9;
  color: #2b5fd9;
  border-radius: 2px;
}

.sure_btn {
  min-width: 60px;
  height: 30px;
  background: #2b5fd9;
  border-radius: 2px;
}

.sys-sub-title {
  display: flex;
  align-items: center;
  color: #2B3646;
  font-weight: 600;
  font-size: 16px;
  margin: 20px 0;
}

.sys-sub-title:before {
  display: block;
  content: "";
  width: 6px;
  height: 22px;
  margin-right: 12px;
  background: #087eff;
}


.detail-title {
  border-left: 4px solid #2d8cf0;
  padding-left: 8px;
  font-size: 16px;
  font-weight: 700;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.bsp-imgminio-container .upload-list-container .upload-list-content .content-filecontent .filecontent-top .top-success .text-hidden {
  max-width: 75px !important;
}

.filecontent-top {
  margin-bottom: -10px;
}

.filecontent-top p span:nth-of-type(2) {
  display: none !important;
}


.jgrySelect-info {
  background: #FFFFFF;
  box-shadow: 0px 2px 6px 1px rgba(0, 34, 84, 0.12);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #E4EAF0;
  margin-right: 16px;
  margin: 0px 16px 0 16px !important;

}

.jgrySelect-flex {
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal;
  font-size: 14px;

  div p span:first-of-type {
    color: #8D99A5;
  }
}

.jgrySelect-flex:first-of-type {
  align-items: center;
}

.jgrySelect-flex .xm {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 18px;

}

.jgrySelect-flex .xm:first-child {
  color: #00244A !important;
}

.jgrySelect-flex .xm:last-of-type {
  position: relative;
  right: -30px;
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal !important;
  font-size: 16px;
  color: #415060;
}

.ml-16-title {
  margin-left: 16px;
}

/deep/ .ivu-layout {
  // background: #EBEEF5;
  background: transparent !important;
}


.DataGrid-BOX .ivu-table-tbody a {
  color: rgb(44, 44, 249) !important;
  text-decoration: none !important;
  border-bottom: 1px solid !important;
  cursor: pointer !important;
}

.idle-room-wrap {
  flex-wrap: wrap;

  .idle-room-wrap-child {
    margin-bottom: 8px;
    width: 18% !important;

    &:hover {
      border: 1px solid #2b5fda;
      cursor: pointer;
    }
  }
}

/** element description 组件样式重置 */
.el-descriptions {
  .el-descriptions-item__label.is-bordered-label {
    background: #f2f5fc;
    color: #515a6e;
  }

  .is-bordered .el-descriptions-item__cell {
    border-color: #CEE0F0
  }
}

.DataGrid-BOX .titleNav {
  display: flex;
  justify-content: space-around;
}

.DataGrid-BOX .ivu-table-tip,.DataGrid-BOX .ivu-table-tip tbody tr td{
  height: 50px !important;
}

// .DataGrid-BOX  .titleNav>.navleft{
//   width: 20% !important;
// }
.DataGrid-BOX .titleNav>.navright {
  width: 80% !important;
}

.DataGrid-BOX .navlist {
  text-align: right;
}

.form-header-title {
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  margin-bottom: 10px;
}


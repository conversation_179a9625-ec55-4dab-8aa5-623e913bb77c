<!-- 留所服刑 -->
<template>
  <div class="bsp-base-form">
		<!-- <div class="content-wrap"> -->
      <div class="bsp-base-tit" v-if="!showFormCompnent">
				{{ modalTitle }}
			</div>
      <div class="bsp-base-content" v-if="showFormCompnent" style="padding: 10px;">
        <s-DataGrid ref="grid" funcMark="zfgl-lsfxlb" :customFunc="true" :params="{}">
            <template slot="customHeadFunc" slot-scope="{ func }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':zfgl-lsfxlb:lsfx')" @click="openModal = true">留所服刑登记</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
                <Dropdown :placement="index>2?'top':'bottom'">
                  <a href="javascript:void(0)">
                    操作
                    <Icon type="ios-arrow-down"></Icon>
                  </a>
                  <DropdownMenu slot="list">
                    <DropdownItem
                      v-if="func.includes(globalAppCode + ':zfgl-lsfxlb:sp') && row.status == '10-1'"
                      @click.native="approve(row)">领导审批</DropdownItem>
                    <DropdownItem
                      v-if="func.includes(globalAppCode + ':zfgl-lsfxlb:info')"
                      @click.native="info(row)">详情</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
            </template>
        </s-DataGrid>
      </div>
      
      <div v-if="!showFormCompnent" style="height: calc(100% - 60px)">
        <component v-bind:is='component' @on_show_table="on_show_table" :modalTitle="modalTitle"
          :stepType="stepType" :jgrybm="jgrybm" :ryxxObj="ryxxObj" :id="id">
        </component>
      </div>
    <!-- </div> -->
    <!-- 选择被监管人员 -->
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
			title="人员列表">
			<div class="select-use">
				<prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple="false"
					:selectUseIds="selectUseIds" />
			</div>
			<div slot="footer">
				<Button type="primary" @click="useSelect" class="save">确 定</Button>
				<Button @click="openModal = false" class="save">关 闭</Button>
			</div>
		</Modal>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import addForm from './add.vue'
import bussiness from './bussiness.vue'
import { prisonSelect } from 'sd-prison-select'
export default {
  components: {
    sDataGrid,
    addForm,
    bussiness,
    prisonSelect
  },
  data() {
    return {
      showFormCompnent: true,
      component: null,
      jgrybm: '',
      ryxxObj: {},
      modalTitle: '',
      stepType: 'add',
      openModal: false,
      selectUseIds: '',
      id: ''
    }
  },
  methods: {
    approve(row) {
      console.log(row,'审批');
      this.stepType = 'approve'
      this.jgrybm = row.jgrybm
      this.id = row.id
      this.component = bussiness
      this.modalTitle = '留所服刑-领导审批'
      this.showFormCompnent = false
    },
    info(row) {
      console.log(row,'详情');
      this.stepType = 'info'
      this.jgrybm = row.jgrybm
      this.id = row.id
      this.component = bussiness
      this.modalTitle = '留所服刑-详情'
      this.showFormCompnent = false
    },
    useSelect() {
			this.openModal = false
			this.ryxxObj = this.$refs.prisonSelect.checkedUse[0]
			this.selectUseIds = this.ryxxObj.jgrybm
			// this.getGoodsId(this.selectUseIds)
      this.stepType = 'addTop'
      this.jgrybm = this.selectUseIds
      this.component = addForm
      this.modalTitle = '留所服刑-登记'
      this.showFormCompnent = false
		},
    editEvent(index,row,type) {

    },
    on_show_table() {
			this.component = null
			this.showFormCompnent = true
			this.on_refresh_table()
		},
		on_refresh_table() {
			this.$refs.grid.query_grid_data(1);
		}
  },
  created() {
    console.log(this.$route.query,'--------------------')
    if(this.$route.query && this.$route.query.curId) {
      if(this.$route.query.stepType == 'approve') {
        this.jgrybm = this.$route.query.jgrybm
        this.modalTitle='留所服刑-领导审批'
        this.stepType = 'approve'
        this.id = this.$route.query.curId
        this.component = bussiness
        this.showFormCompnent=false
      } else if(this.$route.query.stepType == 'info') {
        this.jgrybm = this.$route.query.jgrybm
        this.modalTitle='留所服刑-详情'
        this.stepType = 'info'
        this.id = this.$route.query.curId
        this.component = bussiness
        this.showFormCompnent=false
      }
    }
  }
}
</script>

<style>
/deep/.bsp-base-content{
  top: 60px !important;
}
</style>
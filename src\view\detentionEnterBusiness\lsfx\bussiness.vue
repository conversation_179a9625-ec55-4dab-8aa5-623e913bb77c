<template>
  <div class="personal-calss">
	  <div class="bsp-base-content" style="width: 100%; ">
		<div class="personal-left">
		  <jgryxx v-if="jgrybm" :jgrybm="jgrybm"></jgryxx>
		</div>
		<div class="personal-right">
            <div class="add-form" style="padding: 20px;">
                <div class="list-title">留所服刑登记信息</div>
                <el-descriptions class="margin-top" :column="2" size="small" border>
                    <el-descriptions-item label="留所原因" :span="2" :labelStyle="{ width: '8em' }">
                        {{ detail.detainReasonName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="经办民警" :labelStyle="{ width: '8em' }">
                        {{ detail.addUserName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="经办时间">
                        {{ detail.addTime }}
                    </el-descriptions-item>
                    <el-descriptions-item label="留所原因详情" :span="2">
                        {{ detail.detainReasonDetails }}
                    </el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">
                        {{ detail.remark }}
                    </el-descriptions-item>
                </el-descriptions>

                <div style="margin-top: 20px;">
                    <div class="list-title">审批信息</div>
                    <div v-if="stepType == 'approve'">
                        <Form
                            ref="jgxxFormData"
                            :model="approvalData"
                            :rules="ruleValidate"
                            :label-colon="true"
                            label-position="right"
                            :hide-required-mark="false"
                            inline
                            :label-width="130"
                            style="margin-left: 5%; margin-top: 2%;"
                            >
                            <Timeline v-for="(item, index) in approvalList" :key="index">
                                <TimelineItem
                                v-if="item.status == 1"
                                color="rgb(25,190,107)"
                                class="line-gray"
                                >
                                <template #dot>
                                    <Icon size="30" type="md-checkmark-circle" />
                                </template>
                                <p class="til">
                                    {{ item.taskName !== "民警提交" ? item.taskName : "留所服刑申请" }}
                                </p>

                                <Row>
                                    <Col span="8">
                                    <p class="time">申请人：{{ item.executeUserName }}</p>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                    <p class="time">所属单位：{{ item.executeUserName }}</p>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                    <p class="time">申请时间：{{ item.createTime }}</p>
                                    </Col>
                                </Row>
                                </TimelineItem>
                                <TimelineItem v-if="item.status == -1" color="#ccc" class="line-gray">
                                <template #dot>
                                    <Icon size="30" type="md-checkmark-circle" />
                                </template>
                                <p class="til">
                                    {{ item.taskName !== "民警提交" ? item.taskName : "临时取物申请" }}
                                </p>
                                <Row>
                                    <Col span="16">
                                    <FormItem prop="approvalResult" label="审批结果">
                                        <RadioGroup
                                        v-model="approvalData.approvalResult"
                                        size="large"
                                        @on-change="getDis"
                                        >
                                        <Radio label="1">同意</Radio>
                                        <Radio label="2">不同意</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="16">
                                    <FormItem prop="approvalComments" label="审批意见" style="width: 100%">
                                        <Input
                                        type="textarea"
                                        v-model="approvalData.approvalComments"
                                        :autosize="{ minRows: 2, maxRows: 5 }"
                                        placeholder="请输入审批意见"
                                        style="width: 100%"
                                        />
                                    </FormItem>
                                    </Col>
                                </Row>
                                </TimelineItem>
                            </Timeline>
                        </Form>
                    </div>
                    <div v-else style="padding-left: 50px;">
                        <s-general-history
                            v-if="actInstId"
                            :key="timer"
                            :revokeCallback="revokeCallback"
                            :modifyUserCallback="modifyUserCallback"
                            :showRevokeBtn="false"
                            :showModifyBtn="false"
                            :actInstId="actInstId">
                        </s-general-history>
                    </div>
                </div>
            </div>
		</div>
	  </div>
	  <div class="bsp-base-fotter">
		<Button @click="on_return_table(false)" >返 回</Button>
		<Button @click="submit" type="primary" v-if="stepType != 'info'">提 交</Button>
	  </div>
	</div>
</template>

<script>
import { mapActions } from "vuex";
import jgryxx from "./jgryxx.vue";
import { sGeneralHistory } from 'sd-general-history'
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js"
export default {
    components: {
        jgryxx,
        sGeneralHistory
    },
    props: {
      id: {
        type: String,
        default: ''
      },
	  stepType: {
		type: String,
		default: "",
	  },
	  jgrybm: {
		type: String,
		default: "",
	  },
      ryxxObj: {
        type: Object,
        default: {}
      }
	},
    data() {
        return {
            formData: {
                operatePolice: this.$store.state.common.userName,
                operatePoliceSfzh: this.$store.state.common.idCard,
                operateTime: formatDateparseTime(new Date())
            },
            detail: {},
            approvalData: {
                approvalComments: "同意",
                approvalResult: "1",
                approverSfzh: this.$store.state.common.idCard,
                approverXm: this.$store.state.common.userName,
                approverTime: formatDateparseTime(new Date()),
            },
            ruleValidate: {
                approvalComments: [
                { required: true, message: "请选择审批结果", trigger: "blur" },
                ],
                approvalResult: [
                { required: true, message: "请填写审批意见", trigger: "blur" },
                ],
            },
            approvalList: [],
            timer: '',
            actInstId: ''
        }
    },
    mounted() {
        console.log(this.$store.state.common)
    },
    created() {
        this.getDetailData()
    },
    watch: {
        actInstId () {
            if (this.actInstId) {
                this.timer = new Date().getTime()
            }
        },
    },
    methods: {
        submit() {
            this.approvalData.id = this.id
            this.$store.dispatch('authPostRequest',{
                url: this.$path.app_serveSentence_approve,
                params: this.approvalData
            }).then(res => {
                if(res.success) {
                    console.log(res,'res');
                    this.$nextTick(() => {
                        this.on_return_table()
                    })
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        getDetailData() {
            this.$store.dispatch('authGetRequest',{
                url: this.$path.app_serveSentence_get,
                params: {
                    id: this.id
                }
            }).then(res => {
                if(res.success) {
                    this.detail = res.data
                    this.actInstId = res.data.actInstId;
                    this.approval(this.actInstId);
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        approval(actInstId) {
            this.$store
            .dispatch("postRequest", {
                url: this.$path.app_bsp_approveTrack,
                params: {
                    actInstId,
                },
            })
            .then((res) => {
                if (res.success) {
                    console.log(res, "res");
                    this.approvalList = [...res.data].reverse();
                } else {
                    this.$Modal.error({
                    title: "温馨提示",
                    content: res.msg || "获取流程轨迹失败!",
                    });
                }
            });
        },
        getDis(data) {
            console.log(data,'dataRadio');
            if(data == '1'){
                this.approvalData.approvalResult = '同意'
            } else if(data == '2') {
                this.approvalData.approvalResult = '不同意'
            }
        },
        revokeCallback() {
            return new Promise((resolve, reject) => {
                let that = this;
                setTimeout(function () {
                that.$Modal.success({
                    title: "温馨提示",
                    content: "流程撤回成功!",
                });
                resolve({ success: true });
                }, 1000);
            });
        },
        modifyUserCallback() {
            return new Promise((resolve, reject) => {
                let that = this;
                setTimeout(function () {
                that.$Modal.success({
                    title: "温馨提示",
                    content: "修改审批人成功!",
                });
                resolve({ success: true });
                }, 1000);
            });
        },
        on_return_table() {
            this.$emit("on_show_table");
        }
    }
}
</script>

<style lang="less" scoped>
  .personal-calss {
	width: 100%;
	height: 100%;
	display: flex;
	.personal-left {
	  width: 20%;
	  height: 100%;
	  border-right: 1px solid #cee0f0;
	}
	.personal-right {
	  width: 80%;
	  overflow-y: auto;
	  height: 100%;
	}
  }
  .bsp-base-content {
	// right: 32px !important;
	bottom: 50px !important;
	display: flex;
  }
  .personal-right::-webkit-scrollbar,.main-content::-webkit-scrollbar,.tree-box::-webkit-scrollbar,.bsp-scroll::-webkit-scrollbar {/*滚动条整体样式*/
	width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
	height: 10px;
  
  }
  .personal-right::-webkit-scrollbar-thumb,.main-content::-webkit-scrollbar-thumb,.tree-box::-webkit-scrollbar-thumb,.bsp-scroll::-webkit-scrollbar-thumb  {/*滚动条里面小方块*/
	border-radius: 3px;
	background: #b7c7dd;
  
  }
  .personal-right::-webkit-scrollbar-track ,.main-content::-webkit-scrollbar-track,.tree-box::-webkit-scrollbar-track,.bsp-scroll::-webkit-scrollbar-track{/*滚动条里面轨道*/
	border-radius: 3px;
	background: #EDEDED;
  }
 .ivu-timeline-item-head{
	background-color: none !important;
  }
  .bsp-base-content{
    top: 60px !important;
  }
  /deep/.el-input--prefix .el-input__inner {
    height: 30px !important;
  }
  .list-title {
    line-height: 2.4;
    background: #f2f5fc;
    font-size: 14px;
    padding: 0 0.8em;
    font-weight: bold;
    border: 1px solid #CEE0F0;
    border-bottom: unset;
  }
  .line-gray > .ivu-timeline-item-tail {
    border-color: #dae1ec;
  }
  .til{
    // font-size: 14px;
    font-weight: bold;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-size: 16px !important;
  }
  .time{
    padding-left: 5;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
    font-size: 16px !important;
    color: #2b3646;
    padding: 10px 8px;
    }
    /deep/.ivu-timeline-item-head{
    background-color: none !important;
    }
  </style>
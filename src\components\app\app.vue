<template>
  <div class="base-app" style="min-width: 1440px" v-if="!loadding && isPower">
    <Layout>
      <Header style="display: flex;position: relative;">
        <div class="layout-logo">
          <img  v-if='appInfo.icon' :src="appInfo.icon"  />
          <span v-else class="app_icon_name">{{ getEmptyIcon() }}</span>
          <span :style="[{marginLeft:(appInfo.icon ? '64px':'0')},{letterSpacing:'2.5px'}]">{{ appInfo.APP_NAME }}</span>
          <span class="subtitle">{{ appInfo.isDisabled ? '已启用' : '未启用' }}</span>
        </div>
        <div class="layout-nav">
          <span 
            v-for="(item, index) in topMenus" :key="index + 'menus'" :class="[curActive===item.id?'active':'']"
            @click="selectMenu(item)" v-if="index<8" >{{ item.name }}</span>
          <span v-if="topMenus && topMenus.length>8" @click="cardSelect=!cardSelect" @mouseover="cardSelect=true" >更多功能</span>
          <Card style="width:200px;right: -60px;position: absolute;" class="cardSelect" v-if="topMenus && topMenus.length>8 && cardSelect" @mouseover="cardSelect=true" @mouseleave="cardSelect=false">
            <ul class="card-ul">
                <li v-if="i>7" v-for="(item, i) in topMenus" @click="selectMenu(item)" :class="[curActive===item.id?'active':'']">{{ item.name }}</li>
            </ul>
        </Card>
        </div>
        <!-- <div class="user-container">
          <div class="app-config-wrapper" style="position: absolute;right: 0px;">
            <Tooltip content="平台管理" style="position: absolute;right: 240px;top: 2cap;">
              <div class="app-config" @click="goToAppConfig"><img style="margin-top: 16px;" src='@/assets/images/common/option.png' /></div>
            </Tooltip>
          </div>
          <user :user-avatar="userAvatar" @on-close="handleCloseTag" :list="tagNavList"/>
        </div> -->
      </Header>
      <Layout>
        <Sider
          v-if="showLeftMenu" hide-trigger :width="openSide?'200':'40'" :collapsed-width="64"
          :style="{ height: 'calc(100vh - 60px)', overflow: 'auto' }" class="left-sider"
        >
        <div class="bsp-main" style="padding-top: 0px;height: 100%;" @mouseleave="changeOpen(false)"
        @mouseenter="changeOpen(true)">
          <side-menu
            accordion
            ref="sideMenu"
            :active-name="$route.path === '/app/system/frame' ? 'isTurnFrame_' + $route.query.menuId : $route.path"
            :collapsed="collapsed"
            @on-select="turnToPage"
            :menu-list="menus"
            :openSideMenu="openSide"
          >
          </side-menu>
          </div>
        </Sider>
        <Layout :style="{ height: 'calc(100vh - 60px)' }">
          <div style="padding: 0; height: 0px; background: #f0f0f0" class="app-tags" v-if="showLeftMenu">
            <!-- <tags-nav :value="$route" @input="handleClick" :list="tagNavList" @on-close="handleCloseTag"/> -->
            <div class="side-action" :style="[{left:(openSide ? '200px':'39px')},{top:'72px'}]" v-if="showLeftMenu" @click="changeSide"><i :class="openSide?'back':'forward'" /></div>
          </div>

          <Content :style="{ background: '#EBEEF5', position: 'relative', overflow: 'auto' }">
            <keep-alive :include="cacheList" v-show="doalogType === '0'">
              <router-view :app="app"/>
            </keep-alive>
            <div v-if="doalogType === '1'">
              <component v-bind:is="iframeComponent" :iframeUrl="iframeUrl"></component>
            </div>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  </div>
  <div v-else-if="!loadding && !isPower">
    <error :errMsg="errMsg"/>
  </div>
</template>

<script>
import SideMenu from './components/side-menu'
import TagsNav from './components/tags-nav'
import iframeComponent from './components/iframeComponent.vue'
import error from '@/view/error-page/noPermError.vue'
import {mapMutations} from 'vuex'
import {getNewAppTagList, routeEqualNew} from '@/libs/util'
import tem from 'template_js'
import {getToken} from '@/libs/util'
import _ from "lodash";
import User from "_c/main/components/user";

export default {
  name: 'app-zt',
  components: {
    User,
    SideMenu,
    TagsNav,
    error,
    iframeComponent
  },
  data() {
    return {
      cardSelect:false,
      appName: serverConfig.APP_NAME,
      appCode: serverConfig.APP_MARK,
      appId: serverConfig.APP_ID,
      appInfo:serverConfig || {},
      showSubtitle: false,
      showLeftMenu: true,
      subtitle: '',
      loadding: true,
      isPower: true,
      errMsg: '抱歉,您还没有权限访问该页面',
      appMark: '',
      collapsed: false,
      app: {},
      doalogType: '0',
      iframeUrl: '',
      meunMap: {},
      menus: [],
      topMenus: [],
      curActive: '',
      openSide:true,
      isMove:false
    }
  },
  props: {
    sortList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
    userAvatar() {
      return this.$store.state.common.avatarImgPath;
    },
    tagNavList() {
      return this.$store.state.app.appTagNavList[this.appMark]
    },
    cacheList() {
      const list = ['ParentView', ...(this.tagNavList.length ? this.tagNavList.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : [])]
      return list
    }
  },
  created() {
    // this.getAppData()
  },
  watch: {
    $route(newRoute) {
      const {path, name, query, params, meta} = newRoute
      if ('/app/system' === path) {
        return
      }
      let appMark = this.appMark
      if ('/app/system/frame' === path) {
        let item = this.meunMap[query.menuId]
        meta.title = item.name
        let frameRouter = {name, path: 'isTurnFrame_' + query.menuId, meta, query}
        this.addAppTag({
          route: {path: 'isTurnFrame_' + query.menuId, name, query, params, meta},
          appKey: appMark
        })
        this.setAppTagNavList({list: getNewAppTagList(this.tagNavList, frameRouter), key: this.appMark})
        this.$refs.sideMenu.updateOpenName(frameRouter.path)
      } else if ('/setting/grid' === path) {
        this.showLeftMenu = false
        return
      } else {
        this.addAppTag({
          route: {path, name, query, params, meta},
          appKey: appMark
        })
        this.setAppTagNavList({list: getNewAppTagList(this.tagNavList, newRoute), key: this.appMark})
        this.$refs.sideMenu.updateOpenName(newRoute.path)
      }
      this.showLeftMenu = true
    }
  },
  methods: {
    ...mapMutations(['setAppTagNavList', 'addAppTag', 'closeAppTag']),
    getEmptyIcon() {
      return this.appName.slice(0, 1)
    },
    changeSide(){
          this.openSide=!this.openSide
          this.isMove=!this.openSide
        },
    changeOpen(data){
      if(this.isMove){
        this.openSide=data
      }
    },
    handleClick(item) {
      this.showLeftMenu = true
      this.turnToPage(item)
    },
    goToAppConfig() {
      let routeUrl = this.$router.resolve({
        path: '/uac/user/area'
      })
      window.open(routeUrl.href, '_blank')
    },
    handleCloseTag(res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          //this.turnToPage(this.$config.homeName)
        } else {
          let that = this
          if (res.length === 0) {
            this.$Message.error('不能删除最后一个TAB页')
            return
          }
          if (route.path.indexOf('isTurnFrame_') > -1) {
            if (this.$route.query.menuId === route.path.split('_')[1]) {
              this.closeAppTag({
                route: route,
                key: this.appMark,
                callback: function (newRoute) {
                  that.turnToPage(newRoute)
                }
              })
            }
          } else if (routeEqualNew(this.$route, route)) {
            this.closeAppTag({
              route: route,
              key: this.appMark,
              callback: function (newRoute) {
                that.turnToPage(newRoute)
              }
            })
          }
        }
      }
      this.setAppTagNavList({list: res, key: this.appMark})
    },
    selectMenu(item) {
      this.cardSelect=false
      this.menus = item.children
      this.curActive = item.id
      if (item.name === 'bsp:page:mgr') {
        this.$router.push({path: '/app/public/page-manager', query: _.cloneDeep(this.$route.query)})
      } else if (this.topMenus && this.topMenus.length > 0) {
        let menu0 = item
        if (menu0.children && menu0.children.length > 0) {
          let first = menu0.children[0]
          // let req = Object.assign({}, {})
          if (first.type == '1') {
            this.$router.push({path: '/app/system/frame', query: {}})
          } else {
            this.$router.push({path: first.path, query: req})
          }
        } else {
          // let req = Object.assign({},{})
          if (first.type == '1') {
            this.$router.push({path: '/app/system/frame', query: {}})
          } else {
            this.$router.push({path: menu0.path, query: {}})
          }
        }
      }

    },
    turnToPage(route) {
      //console.log(route,'route121')
      let {path, params, query} = {}
      if (typeof route === 'string') {
        ;(path = route)
      } else {
        path = route.path
        params = route.params

      }
      query = route.query
      if (path.indexOf('isTurnByHref_') > -1) {
        let item = this.meunMap[path.split('_')[1]]
        if (item.type === '0') {
          let openUrl = '/#' + item.path
          for (let key in query) {
            if (openUrl.indexOf('?') > -1) {
              openUrl += '&' + key + '=' + query[key]
            } else {
              openUrl += '?' + key + '=' + query[key]
            }
          }
          window.open(openUrl)
        } else {
          let req = {menuId: item.id}  //Object.assign({},)
          let temValue = {user: this.$store.getters.sessionUser, req: req, token: getToken()}
          let openUrl = this.template(item.path, temValue)
          window.open(openUrl)
        }
        return
      } else if (path.indexOf('isTurnFrame_') > -1) {
        let item = this.meunMap[path.split('_')[1]]
        let req ={menuId: item.id} // Object.assign({}, {menuId: item.id})
        this.$router.push({path: '/app/system/frame', query: req})
        let temValue = {user: this.$store.getters.sessionUser, req: req, token: getToken()}
        this.doalogType = '0'
        this.$nextTick(() => {
          this.iframeUrl = this.template(item.path, temValue)
          this.iframeComponent = 'iframeComponent'
          this.doalogType = '1'
        })
        return
      } else {
        this.doalogType = '0'
        this.$router.push({
          path,
          params,
          query: {appId: query.appId}
        })
      }
    },
    getAppData() {
      let query = {appCode:this.appCode}
      this.$store.dispatch('authGetRequest', {url: this.$path.app_getMenu_url, params: query}).then(resp => {
        if (resp.success) {
          // this.appInfo = resp.data
          // localStorage.setItem("bsp_appname",this.appInfo.name)
          let menu = resp.data //this.transData(resp.menu)
          let usedMenu = []
          for (let i = 0; i < menu.length; i++) {
            if (menu[i].children && menu[i].children.length > 0) {
              usedMenu.push(menu[i])
            }
          }
          this.topMenus = _.cloneDeep(usedMenu)
          const currentMenu = menu.find(item => item.path === this.$route.path)
          if (currentMenu && !_.isEmpty(currentMenu.pid)) {
            this.curActive = currentMenu.pid
            this.menus = usedMenu.find(item => item.id === this.curActive).children
          } else {
            this.curActive = this.topMenus[0].id
            this.menus = usedMenu[0].children
          }

          if ('/app/system' === this.$route.path && usedMenu && usedMenu.length > 0) {
            let menu0 = usedMenu[0]
            if (menu0.children && menu0.children.length > 0) {
              let first = menu0.children[0]
              this.turnToPage(first.path)
            } else {
              this.turnToPage(menu0.path)
            }
          }
          // this.app = resp.data
          this.appMark = resp.data.code
          this.loadding = false
          // if (resp.data.code !== this.appCode) {
          //   this.showSubtitle = true
          //   this.subtitle = resp.data.name
          // }
          this.initTagNavCache(this.appMark)
        } else {
          this.loadding = false
          this.isPower = false
          this.errMsg = resp.msg
        }
      })
    },
    transData(datas) {
      var r = [],
        hash = {},
        id = 'id',
        pid = 'pid',
        children = 'children',
        i = 0,
        j = 0,
        len = datas.length
      let metas = []
      for (; i < len; i++) {
        let meta = {icon: datas[i].icon, title: datas[i].name}
        let m = {
          id: datas[i].id,
          meta: meta,
          name: datas[i].code,
          path: datas[i].path,
          icon: datas[i].icon,
          pid: datas[i].pid ? datas[i].pid : ''
        }
        m.type = datas[i].type
        m.open_mode = datas[i].openMode
        hash[datas[i][id]] = m
        metas.push(m)
      }
      this.meunMap = hash
      for (; j < len; j++) {
        var aVal = metas[j],
          hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },

    initTagNavCache(appKey) {
      const {path, name, query, params, meta} = this.$route
      //console.log(path, name, query, params, meta, 'path, name, query, params, meta')
      this.topMenus.forEach(item => {
        if (path.indexOf(item.path) > -1) {
          this.menus = item.children
          this.curActive = item.id
        }
      })
      if (path === '/app/system/frame') {
        let item = this.meunMap[query.menuId]
        let req = {menuId: item.id}
        let temValue = {user: this.$store.getters.sessionUser, req: req, token: getToken()}
        this.iframeUrl = this.template(item.path, temValue)
        this.iframeComponent = 'iframeComponent'
        this.doalogType = '1'
        meta.title = item.meta.title
        let frameRouter = {name, path: 'isTurnFrame_' + query.menuId, meta, query}

        let list = localStorage[appKey]
        let chcheNav = list ? JSON.parse(list) : []
        this.$set(this.$store.state.app.appTagNavList, appKey, chcheNav)
        this.setAppTagNavList({list: getNewAppTagList(this.tagNavList, frameRouter), key: appKey})
      } else {
        let list = localStorage[appKey]
        let chcheNav = list ? JSON.parse(list) : []
        this.$set(this.$store.state.app.appTagNavList, appKey, chcheNav)
        this.setAppTagNavList({list: getNewAppTagList(this.tagNavList, this.$route), key: appKey})
      }
      if (path === '/setting/grid') {
        this.showLeftMenu = false
      }
    },
    template(tpl, data) {
      tem.config({sTag: '{{', eTag: '}}', escape: true})
      return tem(tpl, data)
    }
  }
}
</script>

<style scoped lang="less">
.app_icon_name{
  min-width: 48px;
    max-width: 48px;
    height: 48px;
    margin-right: 10px;
    border: 1px solid #F0F4FF;
    background: #FFFFFF;
    border-radius: 8px;
    font-weight: bold;
    font-size: 32px;
    color: #2B5FD9;
    font-style: normal;
    text-transform: none;
}
/deep/ .ivu-card-body{
   padding:8px 0!important;
}
.cardSelect{
  // display: none;
}
.card-ul li{
  list-style:none;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #3E4E66;
  &:hover{
    background: #EBF1FF;
  }
}
.layout-logo {
  font-size: 30px;
  color: #fff;
  // background: url('~@/assets/images/BSP-48.png') 0px center;
  background-size: 40px 39px;
  background-repeat: no-repeat;
  // padding-left: 50px;
  margin-right:80px;
  position: relative;
  img{
    width: 48px;
    height: 48px;
    top: 5px;
    position: absolute;
    border-radius: 50%;
  }
}

.layout-logo .subtitle {
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-weight: normal;
  font-size: 14px;
  color: #13BA5A;
  margin: auto 10px;
  border-radius: 4px;
  letter-spacing: 1px;
  background: #E3FCEE;
  border-radius: 2px 2px 2px 2px;
  padding: 2px 6px;

}

.base-app /deep/ .ivu-layout-header {
  background: #0099FF;
  height: 60px;
  line-height: 60px;
  padding: 0 24px;
}

.base-app /deep/ .ivu-menu-item {
  padding: 12px 16px;
  font-size: 16px;
  border-left: 0px solid #333;
  display:flex;
  align-content: center;
  align-items: center;
  background: #1f3466;
}

.base-app /deep/ .ivu-menu-submenu {
  padding: 0 0px !important;

}

.base-app /deep/ .ivu-menu-submenu .ivu-menu-submenu-title > span {
  // margin-left: 8px !important;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
}

.base-app /deep/ .ivu-menu-item .ivu-icon {
  font-size: 18px;
}

.base-app /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  // background-color: #F3F4F5 !important;
  border-left-color: #F3F4F5 !important;
}

.base-app /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active{
  background: #F0F5FF;
  height: 48px;
}
.base-app /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  background-color: rgba(70, 148, 244, 0.4) !important;
  border-left-color: #6694ff !important;
}

.base-app /deep/ .ivu-menu-vertical .ivu-menu-item,
.base-app /deep/ .ivu-menu-vertical .ivu-menu-submenu-title {
  padding: 15px 18px 14px 14px;
  // margin: 15px 0 14px 0;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
  display:flex;
  align-content: center;
  align-items: center;
}

.base-app /deep/ .ivu-menu-submenu-title > i,
.base-app /deep/ .ivu-menu-submenu-title span > i {
  margin-right: 4px;
  font-size: 18px;
}

.base-app /deep/ .ivu-menu-vertical .ivu-menu-submenu-title-icon {
  right: 16px;
  font-size: 18px;
}

.base-app /deep/ .app-tags .ivu-tag .ivu-icon-ios-close {
  top: 0px;
}

.base-app /deep/ .app-tags .ivu-tag-dot {
  font-size: 14px;
  padding: 0px 12px;
}

.base-app /deep/ .app-tags .ivu-tag-dot .ivu-icon-ios-close {
  color: #333 !important;
  font-size: 16px !important;
  margin-left: 8px !important;
}


.base-app /deep/ .app-tags .ivu-menu-item-active {
  // background-color: #333;
}

.base-app /deep/ .app-tags .ivu-tag-closable.ivu-tag-checked {
  padding-right: 12px;
}

.base-app /deep/ .app-tags .ivu-tag-checked {
  padding-right: 20px;
}

.ivu-layout-sider::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.ivu-layout-sider::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 3px;
  background: #b7c7dd;
}

.ivu-layout-sider::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 3px;
  background: #ededed;
}

.layout-nav {
   z-index:99;
   position: relative;
  /deep/ .active {
    font-weight: 700;
    background: linear-gradient( 180deg, rgba(153, 235, 255, 0.6) 0%, rgba(2, 187, 230, 0.6) 100%);
  }
}

.layout-nav span {
  min-width: 80px;
  height: 60px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 20px;
  color: #ffffff;
  display: inline-block;
  padding: 0 24px;
  cursor: pointer;
  margin-right: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }

}

.left-sider {
  border-right:1px solid #E6E6E6;
  :deep(.ivu-layout-sider-children) {
    background-image: url('~@/assets/images/logo.jpg');
    background-repeat: no-repeat;
    background-position: bottom;
  }
}

.app-config-wrapper {

}

.app-config {
  height: 60px;
  width: 60px;
  // background-image: url("~@/assets/images/common/option.png");
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: center center;
  position: fixed;
  right:210px;
  top:0px;
  text-align: center;
  &:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.15);
  }
}

.app-config-active {
  // background-color: #244FB3;
  background: linear-gradient( 180deg, rgba(153, 235, 255, 0.6) 0%, rgba(2, 187, 230, 0.6) 100%);
}

.user-container {
  width: 700px;
  height: 60px;
  background: url("~@/assets/images/lightBg.png");
  position: absolute;
  right: 0;
  display: flex;
}
</style>

<!-- 提询 -->
<template>
	<div>
	  <div class="">
		<div class="bsp-base-content" v-if="!showData">
			  <s-DataGrid ref="grid" funcMark="ckywjlstxlb" :customFunc="true" :params="params" >
           <template v-slot:query:area_id="{ condi, row, index }">
                  <Row>
                    <Col span="24">
                      <s-dicgrid v-model="condi.area_id" @values="getArea" requestType="loopData" v-if="showJqArray" showField="areaName" keyField="areaCode" :loopData="jqArray" popper-class="DataGrid-BOX" style="width: 100%"   />
                    </Col>
                  </Row>
            </template>
            <template v-slot:query:jsh="{ condi, row, index }">
                  <Row>
                    <Col span="24">
                      <s-dicgrid v-model="condi.jsh" requestType="loopData" v-if="showJshArray" showField="roomName" keyField="roomCode" :loopData="jshArray" popper-class="DataGrid-BOX" style="width: 100%"   />
                    </Col>
                  </Row>
            </template>
				  <template slot="customHeadFunc" slot-scope="{ func }">
					  <Button type="primary" v-if="func.includes(globalAppCode + ':ckywjlstxlb:add')" @click.native="addEvent('add')">提讯登记</Button>
				  </template>
				  <template slot="customRowFunc" slot-scope="{ func, row, index }" class="btnList">
            <Button type="primary" v-if="func.includes(appCode+ ':ckywjlstxlb:sp') && row.status == '10-1'" @click.native="sp(index,row,'sp')" style="margin-right: 10px;">审批</Button>
            <Button type="primary" v-if="func.includes(appCode + ':ckywjlstxlb:supple') && row.status==99"  style="margin-right: 10px;"  @click.native="editEvent(index,row,'supplementary')" >补录</Button>
            <Button type="primary" v-if="func.includes(appCode + ':ckywjlstxlb:qd') && row.status== 0" style="margin-right: 10px;" @click.native="signIn(row)" >签到</Button>
            <Button type="primary" v-if="func.includes(appCode + ':ckywjlstxlb:Room') && row.status==1"  style="margin-right: 10px;" @click.native="openRoom(row)" >分配会见室</Button>
            <Button type="primary" v-if="func.includes(appCode+ ':ckywjlstxlb:Check') && row.status==2" style="margin-right: 10px;" @click.native="openCheck(row)" >带出安检</Button>
            <Button type="primary" v-if="func.includes(appCode+ ':ckywjlstxlb:security') && row.status==3" style="margin-right: 10px;" @click.native="securityCheck(row)" >会毕安检</Button>
            <Button type="primary" v-if="func.includes(appCode + ':ckywjlstxlb:info')"  @click.native="editEvent(index,row,'info')" >详情</Button>&nbsp;&nbsp;
            <Button type="primary" v-if="func.includes(appCode + ':ckywjlstxlb:wsyldy')"  @click.native="editEvent(index,row,'wsyl')" >文书预览打印</Button>&nbsp;&nbsp;
				  </template>
			  </s-DataGrid>
		</div>
    <!-- 提询登记 -->
    <div v-if='showData' class='InquiryTitle'>{{modalTitle}}</div>
    <addForm v-if='saveType=="add" && showData' @toback='toback' />
    <detail style="height: 100%;" v-if='(saveType=="info" || saveType == "sp" || saveType == "wsyl") && showData' @toback='toback' :curId='curData.id' :saveType="saveType" />
    <supplementaryRecording v-if='saveType=="supplementary" && showData' @toback='toback' :curId='curData.id' />
	  </div>
   
      <!-- 签到 -->
      <Modal
        v-model="openModalQd"
        :mask-closable="false"
        :closable="true"
        class-name="select-sy-modal"
        width="30%"
        title="签到"
        >
        <div style='text-align:center;font-family: Source Han Sans CN, Source Han Sans CN;font-weight: 400;font-size: 16px;padding-bottom:16px;'>
            <p style='line-height:60px'>办案单位提询人员已到达现场，确认签到</p>
            <div >签到时间： <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                v-model="curData.checkInTime"
                type="datetime" size='small'
                placeholder="选择日期时间">
                </el-date-picker>
            </div>
        </div>
        <div slot="footer">
          <Button @click="openModalQd=false">取消</Button>
          <Button type="primary" @click="submitQd" :loading="loadingsignIn">提交</Button>
        </div>
      </Modal>
     <!-- 空闲审讯室 -->
      <Modal
        v-model="openModalRoom"
        :mask-closable="false"
        :closable="true"
        class-name="select-room-modal"
        width="50%"
        title="空闲审讯室"
        >
        <interrogationRoom v-if='openModalRoom' :curId='curData.id' @selectRoom='selectRoom' />
        <div slot="footer">
          <Button @click="openModalRoom=false">取消</Button>
          <Button type="primary" @click="submitRoom" :loading="loadingRoom">提交</Button>
        </div>
      </Modal>
      <!-- 带出安检登记 -->
             <Modal
              v-model="openModalCheck"
              :mask-closable="false"
              :closable="true"
              class-name="select-room-modal"
              width="40%"
              title="带出安检登记"
              >
        <checkRegistration ref="checkRegistration" v-if='openModalCheck' :curId='curData.id' @escortingInspect='escortingInspect' />
        <div slot="footer">
          <Button @click="openModalCheck=false">取消</Button>
          <Button type="primary" @click="submitCheck" :loading="loadingCheck">提交</Button>
        </div>
      </Modal>
      <!--会毕安检登记 -->
             <Modal
              v-model="openModalSecurity"
              :mask-closable="false"
              :closable="true"
              class-name="select-room-modal"
              width="40%"
              title="会毕安检登记"
              >
        <!-- <securityCheck ref="securityCheck" v-if='openModalSecurity' :curId='curData.id' @returnInspect='returnInspect' /> -->
         <huibiCheck ref="huibiCheck" v-if='openModalSecurity' :curId='curData.id' @returnInspect='returnInspect' />

        <div slot="footer">
          <Button @click="openModalSecurity=false">取消</Button>
          <Button type="primary" @click="submitSecurity" :loading="loadingSecurity">提交</Button>
        </div>
      </Modal>
	  <Modal
		v-model="openModal"
		:mask-closable="false"
		:closable="true"
		class-name="select-sy-modal"
		width="40%"
		:title="modalTitle"
	  >
	  <Form ref="releaseForm" :model="formData" :label-width="100" :label-colon="true" style="margin-right: 16px;" v-if="saveType !='info'">
		  <Row>
			  <Col span="12">
				  <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入姓名', required: true }]" style="width: 100%;">
					  <Input v-model="formData.xm" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
			  <Col span="12">
				<FormItem label="办案单位" prop="badwdm" :rules="[{ trigger: 'blur,change', message: '请添加', required: true,}]" style="width: 100%;">
                
                </FormItem>
			  </Col>
		  </Row>
			  <Row>
			  <Col span="12">
				  <FormItem label="证件类型" prop="zjlx"  style="width: 100%;">
                    <s-dicgrid v-model="formData.zjlx"   dicName="ZD_ZJLX" />                
				  </FormItem>
			  </Col>
			  <Col span="12">
				<FormItem label="联系方式" prop="lxfs"  style="width: 100%;">
					  <Input v-model="formData.lxfs" placeholder="" maxlength="" style="width: 100%;"></Input>
                </FormItem>
			  </Col>
		  </Row>
          <Row>
			  <Col span="12">
				  <FormItem label="证件号码" prop="zjhm"  style="width: 100%;">
					  <Input v-model="formData.zjhm" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
			  <Col span="12">
				<FormItem label="性别" prop="xb"  style="width: 100%;">
                    <s-dicgrid v-model="formData.xb"   dicName="ZD_XB" />                
                </FormItem>
			  </Col>
		  </Row>
          <Row>
			  <Col span="12">
				  <FormItem label="警号" prop="jh"  style="width: 100%;">
					  <Input v-model="formData.jh" placeholder="" maxlength="" style="width: 100%;"></Input>
				  </FormItem>
			  </Col>
           </Row>
            <Row>
			  <Col span="24">
				<FormItem label="附件" prop="gzzjUrl"  style="width: 100%;">

                </FormItem>
			  </Col>
		  </Row>
	  </Form>
    <!-- <detail :formData="formData" v-if="saveType=='info'" style="margin-bottom: 16px;" /> -->
		<div slot="footer">
		  <Button @click="onCancel">取消</Button>
		  <Button type="primary" @click="submitClick" :loading="loading">提交</Button>
		</div>
	  </Modal>
	</div>
  </template>
  
  <script>
  import { sDataGrid } from 'sd-data-grid'
  import { mapActions } from 'vuex'
  import addForm from "./add.vue"
  import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";
  import interrogationRoom from "./interrogationRoom"
  import checkRegistration from './checkRegistration.vue';
  import securityCheck from './securityCheck.vue';
  import huibiCheck from './HuibiCheck.vue'
  import detail from './detail.vue';
  import supplementaryRecording from './supplementaryRecording.vue';
import { log } from 'video.js';
  export default {
	components: {
	  sDataGrid,addForm,interrogationRoom,checkRegistration,securityCheck,huibiCheck,detail,supplementaryRecording
	},
	data() {
	  return {
          appCode:serverConfig.APP_CODE,
          openModalQd:false,
          loadingsignIn:false,
          openModalRoom:false,
          loadingRoom:false,
          openModalCheck:false,
          loadingCheck:false,
          openModalSecurity:false,
          loadingSecurity:false,
          curData:{},
          showFile:false,
          showImg:false,
          serviceMark: serverConfig.OSS_SERVICE_MARK,
          bucketName: serverConfig.bucketName,
          importUrl: this.$path.upload_fj,
          custom_loading: false,
          defaultList: [],
          fileList:[],
          buttenloading:false,
          stream:null,
          uploadForm:{},
          params:{},
          showData: false,
          modalTitle: '提讯登记',
          openModal: false,
          formData: {
                isDisabled:0
              },
          loading: false,
          saveType: 'list',
          loadingStates: [],
          curRoomId:'',
          jqArray:[],
          showJqArray:false,
          jshArray:[],
          showJshArray:true,
	  }
	},
	methods: {
	  ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      toback(){
        console.log('toBack');
          this.showData=false
      },
      //签到
      signIn(row){
          this.curData=row
          if(!this.curData.checkInTime){
            this.$set(this.curData,'checkInTime',formatDateparseTime(new Date()))
          }
          this.openModalQd=true
      },
      submitQd(){
          this.loadingsignIn=true  
          let params={
           checkInTime: this.curData.checkInTime,
           id: this.curData.id
          }
         this.$store.dispatch('authGetRequest', {url: this.$path.acp_bringInterrogation_signIn, params: params}).then(resp => {
        if (resp.success) {
           this.loadingsignIn=false
           this.$Message.success('签到成功')
           this.on_refresh_table()
           this.openModalQd=false
        } else {
           this.loadingsignIn = false
           this.$Message.error(resp.msg||'签到失败')
        }
      })
      },
      //空闲审讯室
      openRoom(row){
          this.curData=row
          this.openModalRoom=true
      },
      selectRoom(roomId){
         this.curRoomId=roomId
      },
      submitRoom(){
          this.loadingRoom=true  
          let params={
           roomId: this.curRoomId,
           id: this.curData.id
          }
         this.$store.dispatch('authGetRequest', {url: this.$path.acp_bringInterrogation_allocationRoom, params: params}).then(resp => {
        if (resp.success) {
           this.loadingRoom=false
           this.$Message.success('分配成功')
           this.openModalRoom=false
           this.on_refresh_table()

        } else {
           this.loadingRoom = false
           this.$Message.error(resp.msg||'分配失败')
        }
      })
      },
      // 带出安检
      openCheck(row){
          this.curData=row
          this.openModalCheck=true
      },
      submitCheck(){
         this.$refs.checkRegistration.submitClick()
      },
      escortingInspect(tag){
            if(tag){
               this.on_refresh_table()
              this.openModalCheck=false
            }else{
            }
      },
      //会毕安检
      securityCheck(row){
          this.curData=row
          this.openModalSecurity=true
      },
      submitSecurity(){
         this.$refs.huibiCheck.submitClick()
      },
      returnInspect(tag){
            if(tag){
              this.on_refresh_table()
              this.openModalSecurity=false
            }else{
            }
      },
                       // 上传组件回调方法
    handleSuccessImg(res, index) {
        console.log(res,'1212')
      this.defaultList.push(res.data);
    //   this.formData.fileList=this.defaultList
      this.defaultList.forEach(ele=>{
        ele.src=ele.url
        ele.imgSrc=ele.url
      })
    //   this.imgArr=this.imgArr.concat(this.uploadList);
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.defaultList.splice(e.index, 1);
          this.imgArr.forEach((ele,i)=>{
            if(ele.url==e.url){
               this.defaultList.splice(i, 1);
            }
          })
        },
      });
    },
        onSelect(data){
           console.info(data,this.formData)
           let arr=[]
           data.forEach(item=>{
             arr.push(item.orgName)
           })
           this.$set(this.formData,'badwmc',arr.join(','))
          },
        onClear(data){
           console.info(data,this.formData,'onClear')
        //    this.$set(this.formData,'badwmc','')
        },
        beforeUpload(){},
        fileSuccessFile(){},
        fileRemoveFile(){},
        fileCompleteFile(data){
            console.log(data,'1212')
            this.fileList=data
        },
        removeFile(file) {
            console.log(file,'removeFile',file.length==0)
            if(file.length==0){
                this.$set(this.formData, 'zpUrl', '')
                this.defaultList=[]
                }
        },
        remove(){
            this.$set(this.formData, 'zpUrl', '')
            this.defaultList=[]
        },
        async uploadImg(imgSrc) {
        let blob = this.dataURLtoBlob(imgSrc);
        let file = this.blobToFile(blob, "拍照");
        let formData = new FormData();
        formData.append("file", file);
        let res = await this.$store.dispatch('authPostRequest', { url: this.importUrl, params: formData })
        let { status, data } = res;
        if (status === 200) {
            this.formData.zpUrl = data.url;
        } else {
            this.errorModal({ content: "上传失败!" })
        }
        },
        dataURLtoBlob(dataurl) {
        let arr = dataurl.split(",");
        let type = arr[0].match(/:(.*?);/)[1];
        let bstr = atob(arr[1]);
        let n = bstr.length;
        let u8str = new Uint8Array(n);
        while (n--) {
            u8str[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8str], { type });
        },
        blobToFile(blob, fileName) {
        blob.lastModifiedDate = new Date();
        blob.name = fileName;
        return blob;
        },
        getfile(file) {
        console.log(file,'getfile')
        this.$set(this.formData, 'zpUrl', file.url)
        this.defaultList=[file]
        this.$refs['releaseForm'].validateField('zpUrl')
        },
       getRadio(value){
        console.log(value,'getRadio')
        console.log(this.formData.isDisabled,this.defaultList,'this.formData.isDisabled')
        if(this.defaultList && this.defaultList.length==0){
          this.$set(this.formData,'zpUrl','')
        }
        if(value){
          // 获取摄像头视频流
          if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
              this.stream=stream
              // this.$refs.videoElement= this.stream;
              const video = document.getElementById('video');
              video.srcObject = this.stream;
              video.play();
            });
          }
        }else{
          this.defaultList=[]
          let faceImageUrl = this.formData.zpUrl
          if (faceImageUrl) {
            let urlObj = { url: faceImageUrl, name: '' }
            this.defaultList.push(urlObj)
          }
          this.stopCamera()          
        }
      },
      stopCamera() {
        // 关闭摄像头
      if (this.stream) {
        const tracks = this.stream.getTracks();
        tracks.forEach(track => track.stop());
        this.$refs.videoElement.srcObject = null;
      }
    },
    capture(){
      this.buttenloading = true;
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const context = canvas.getContext('2d');
      context.drawImage(video, 0, 0, 100, 100);
      // 如果有自定义文件名称，优先使用自定义的文件名称
      let filenames = ''
      if (this.uploadForm.filename != undefined && this.uploadForm.filename != '') {
        filenames = this.uploadForm.filename + '.png'
      } else {
        filenames = this.uploadForm.code + '_' + this.picnum + '.png'
      }
      const base64 = canvas.toDataURL('image/png');//canvas.toDataURL();
      console.log(filenames,'filenames',base64);
       this.uploadImg(base64);
      this.$set(this.formData,'zpUrl',base64)
      this.defaultList=[]
          let faceImageUrl = this.formData.zpUrl
          if (faceImageUrl) {
            let urlObj = { url: faceImageUrl, name: '' }
            this.defaultList.push(urlObj)
          }
      this.buttenloading = false;
    },
	  addEvent(row) {
          this.showFile=true
          this.showImg=true
          this.saveType = 'add'
          this.formData= {isDisabled:0,zpUrl:''},
          this.fileList=[]
		  this.showData = true
          this.modalTitle='提询登记'
          let faceImageUrl = this.formData.zpUrl
          if (faceImageUrl) {
                let urlObj = { url: faceImageUrl, name: '' }
                this.defaultList.push(urlObj)
                }
	  },
	  editEvent(index,row,tag) {
      if(tag=='supplementary'){
       this.modalTitle='提讯/询登记补录'
		   this.saveType = 'supplementary'
        this.showData=true
       this.curData=row
      } else if(tag == 'wsyl') {
        this.modalTitle='文书打印预览'
        this.saveType = 'wsyl'
        this.showData=true
        this.curData=row
      } else{
       this.modalTitle='提讯登记'
		   this.saveType = 'info'
       this.showData=true
       this.curData=row
      }

		  // this.openModal = false
		  // console.log(row);
		  // this.$store.dispatch("authGetRequest", {
			//   url: this.$path.acp_casePersonnel_get,
			//   params:{
			// 	  id: row.id
			//   }
		  // }).then(res => {
			//   console.log(res,'编辑');
			//   if(res.success && res.data) {
      //             this.$set(this.loadingStates, index, false);
      //             this.formData=res.data
      //             this.defaultList=[]
      //             this.fileList=[]
      //             if(this.formData.gzzjUrl){
      //                this.$set(this,'fileList',JSON.parse(this.formData.gzzjUrl))
      //             }else{
      //                this.fileList=[]
      //             }
      //             console.log(this.fileList,'fileList',this.formData.zpUrl)
      //             this.showFile=true
      //             // this.formData.isDisabled=0
      //             if(this.formData.zpUrl){
      //                let urlObj = { url: this.formData.zpUrl, name: '' }
      //               //  this.formData.isDisabled=0
      //                this.defaultList.push(urlObj)
      //             }
      //             this.showImg=true

			// 	  this.openModal = true
			//   } else {
      //     this.$set(this.loadingStates, index, false);
			// 	  this.$Modal.error({
			// 		  title: '温馨提示',
			// 		  content: res.msg || '操作失败'
			// 	  })
      //     this.showFile=true
      //     this.showImg=true
			//   }
		  // })
	  },
    sp(index,row,tag){
      if(tag == 'sp'){
        this.modalTitle='提讯登记审批'
        this.saveType = 'sp'
        this.showData=true
        this.curData=row
      }
    },
    getArea(data){
       this.showJshArray=false
       setTimeout(()=>{
        this.jshArray=data && data.length>0 && data[0].children?data[0].children:[]
        this.showJshArray=true
       },500)
    },
    getJqData () {
      let params = {
        orgCode: this.$store.state.common.orgCode // '110000113',//
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/area/getAreaListByOrgCode',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.jqArray = resp.data
          this.showJqArray=true
          // // let all = {
          // //   areaCode: 'all', areaName: '全部'
          // // }
          // this.jsData.unshift(all)
        } else {
          this.showJqArray=true
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
	  deleleChange(row) {
		  this.$Modal.confirm({
			  title: '温馨提示',
			  content: '请确认是否删除？',
			  onOk: () => {
				  this.$store.dispatch('authGetRequest',{
					  url: this.$path.acp_casePersonnel_delete,
					  params: {
						  ids: row.id
					  }
				  }).then(res => {
					  console.log(res);
					  if(res.success) {
						  this.on_refresh_table()
					  }
				  })
			  }
		  })
	  },
	  onCancel() {
      this.showFile=false
      this.showImg=false
		  this.openModal = false
          this.stopCamera()  
	  },
	  submitClick(){
		  this.$refs['releaseForm'].validate((valid) => {
			  if(valid) {
				  this.loading = true
				  this.saveForm()
			  } else {
				  this.loading = false
				  this.$Message.error('请填写完整!!')
			  }
		  })
	  },
	  saveForm() {
		  console.log(this.formData,'this.formData');
          if(this.fileList && this.fileList.length>0){
             this.$set(this.formData,'gzzjUrl',JSON.stringify(this.fileList))
            }else{
             this.$set(this.formData,'gzzjUrl','')
            }
        // return
		  let url = ''
		  if(this.formData.id) {
			  url = this.$path.acp_casePersonnel_update
		  } else{
			  url = this.$path.acp_casePersonnel_create
		  }
		  this.$store
		  .dispatch("authPostRequest", {
			  url: url, 
			  params:this.formData
		  }).then(res => {
			  console.log(res,'res');
			  if(res.success) {
				  this.loading = false
				  this.openModal = false
				  this.on_refresh_table()
			  }else{
				  this.$Message.error(res.msg || '保存失败！')
				  this.loading = false
			  }
		  })
	  },
	  changeStatus(row,val){
		  console.log(row,val);
		  this.$store.dispatch('postRequest',{
			  url: this.$path.bsp_pam_reportItem_updateStatus,
			  params: {
				  id: row.id,
                  status: row.status,
			  }
		  }).then(res => {
			  if(res.success) {
				this.$Message.success('修改成功!!')

				//   this.on_refresh_table()
			  }
		  })
	  },
	  handleOrgIChange(newVal, oldVal) {
		//   console.log(newVal, oldVal,'newVal, oldVal');
		  if(!newVal) {
			  this.params.orgCode = this.$store.state.common.orgCode
			  this.showData = false
		  } else {
			this.showData = false
			this.params.orgCode = newVal
		  }
	  },
	  on_refresh_table() {
		  this.$refs.grid.query_grid_data(1);
      this.showJshArray=true
	  },
	},
	 mounted() {
     this.getJqData()
	 },
   created(){
    console.log(this.$route.query,'--------------------')
    if(this.$route.query && this.$route.query.curId) {
      if(this.$route.query.saveType == 'sp') {
        this.modalTitle='提询登记审批'
        this.saveType = 'sp'
        this.curData.id = this.$route.query.curId
        this.showData=true
      } else if(this.$route.query.saveType == 'info') {
        this.modalTitle='提询登记详情'
        this.saveType = 'info'
        this.curData.id = this.$route.query.curId
        this.showData=true
      } else if(this.$route.query.saveType == 'supplementary') {
        this.modalTitle='提讯/询登记补录'
        this.saveType = 'supplementary'
        this.curData.id = this.$route.query.curId
        this.showData=true
      }
    }
   }
  }
  </script>
  
  <style  scoped lang="less">
  .ivu-form-item{
    margin-bottom: 10px !important;
  }
 .InquiryTitle{
    border-bottom:1px solid #dcdee2;
    padding:16px;
 }
 .ivu-table-cell-slot button{
  margin:5px 0;
 }

</style>

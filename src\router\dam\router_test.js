let menuMode=serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
    {
      path: "/test",
      name: "test",
      meta: {
        title: "所内就医",
      },
    //   redirect: "/snjy/snmz",
      component: menuMode=='side'?mainNew:main,//() => import("@/components/app-main/index.vue"),
      children: [
        {
          path: "protal",
          name: "protal",
          meta: {
            title: "所内门诊",
            menu: true,
            bread: true,
          },
          component: () => import("@/view/protal-components/index.vue"),
        },
      ],
    },
  ];
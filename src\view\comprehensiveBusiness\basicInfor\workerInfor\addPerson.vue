<template>
    <div>
         <p class="detail-title">新增人员</p>
        <div class="bsp-base-form">
            <!-- <div class="bsp-base-tit">
                {{ action === 'add' ? "新增" : "编辑" }}用户信息
            </div> -->
            <div class="bsp-base-content" style="top:35px !important;">
                <div class="form">
                    <Form :label-width="150" ref="formValidate" :model="formValidate" :rules="ruleValidate">
                        <Row>
                            <Col span="10">
                            <FormItem label="姓 名" prop="name">
                                <Input v-model="formValidate.name" maxlength="50" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="身份证号" prop="idCard"
                                :class="[action === 'edit' && formValidate.confirm === '1' ? '' : 'ivu-form-item-required']"
                                :rules="{ validator: validateCardNull, message: '请输入正确的证件号码' }">
                                <Tooltip content="该用户身份证已确认，身份证号禁止修改。" :disabled="formValidate.confirm !== '1'"
                                    max-width="500" style="width: 100%">
                                    <Input v-model="formValidate.idCard" :disabled="formValidate.confirm === '1'"
                                        maxlength="18" show-word-limit />
                                </Tooltip>
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="所属机构" prop="orgName">
                                <!--                                    <Input v-model="formValidate.orgName" readonly search enter-button="选择"  placeholder="" @on-search="searchOrg"/>-->
                                <org-selector ref="org" v-model="formValidate.orgId" :text="formValidate.orgName"
                                    @on-select="on_select" v-bind.sync="formValidate" :multiple="false">
                                </org-selector>
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="性别" prop="sex">
                                <RadioGroup v-model="formValidate.sex">
                                    <Radio label="1">男</Radio>
                                    <Radio label="2">女</Radio>
                                </RadioGroup>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="10">
                            <FormItem label="排 序" class="ivu-form-item-required" prop="orderId"
                                :rules="{ validator: this.validateNum, message: '请正确填写数字' }">
                                <Input v-model="formValidate.orderId" maxlength="6" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="是否禁用">
                                <RadioGroup v-model="formValidate.isDisabled" size="large">
                                    <Radio label="1">是</Radio>
                                    <Radio label="0">否</Radio>
                                </RadioGroup>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="10">
                            <FormItem label="职务">
                                <Input v-model="formValidate.position" maxlength="50" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="姓名简拼">
                                <Input v-model="formValidate.scode" maxlength="50" show-word-limit />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="10">
                            <FormItem label="移动电话">
                                <Input v-model="formValidate.mobile" maxlength="11" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="办公电话">
                                <Input v-model="formValidate.officeTel" maxlength="30" show-word-limit />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="10">
                            <FormItem label="邮箱">
                                <Input v-model="formValidate.email" maxlength="50" show-word-limit type="email" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="人员类型">
                                <s-dicgrid v-model="formValidate.rylx" dicName="ZD_GZRYXXRYLX" :disabled="true" />
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="10">
                            <FormItem label="出生日期">
                                <DatePicker v-model="formValidate.csrq" type="date" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="工作岗位类型">
                                <s-dicgrid v-model="formValidate.post" dicName="ZD_GQPOST" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="参加监管工作时间">
                                 <DatePicker v-model="formValidate.cjjggzsj" type="date" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="离开监管工作时间">
                                 <DatePicker v-model="formValidate.lkjggzsj" type="date" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="学历">
                                <s-dicgrid v-model="formValidate.education" dicName="ZD_GABBZ_XL" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="学位">
                                <s-dicgrid v-model="formValidate.xw" dicName="ZD_BASE_DEGREE" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="籍贯">
                                 <s-dicgrid v-model="formValidate.jg" dicName="ZD_GABBZ_JG" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="政治面貌">
                                <s-dicgrid v-model="formValidate.zzmm" dicName="ZD_C_ZZMM" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="婚姻情况">
                                <s-dicgrid v-model="formValidate.hyzk" dicName="ZD_GABBZ_HYZK" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="联系地址">
                                <Input v-model="formValidate.lxdz" maxlength="30" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="宗教信仰">
                                <s-dicgrid v-model="formValidate.zjxy" dicName="ZD_GABBZ_ZJXY" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="办公地址">
                                <Input v-model="formValidate.bgdz" maxlength="30" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="民族">
                                <s-dicgrid v-model="formValidate.mz" dicName="ZD_GABBZ_MZ" />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="户籍所在地详址">
                                <Input v-model="formValidate.hjdxz" maxlength="30" show-word-limit />
                            </FormItem>
                            </Col>
                            <Col span="10">
                            <FormItem label="备注">
                                <Input v-model="formValidate.remark" show-word-limit />
                            </FormItem>
                            </Col>
                            
                        </Row>
                    </Form>
                </div>
            </div>
        </div>
        <div class="bsp-base-fotter">
            <Button @click="on_show_table(false)">返 回</Button>
            <Button type="primary" :loading="custom_loading" @click="handleSubmit('formValidate')"
                style="margin-left: 25px">保 存</Button>
        </div>

    </div>
</template>
<script>
import { mapActions } from 'vuex'
// import orgChoice from '@/view/com/component/org-choice'
import { orgSelector} from 'sd-org-selector'
import vPinyin from '_c/common/vue-py.js'
// import { orgSelector } from 'sd-org-selector'
import { SM3, SM2 } from 'gm-crypto'
export default {
    props: {
        formDate: Object,
        action: String
    },
    components: {
        // 'org-choice': orgChoice,
        orgSelector
    },
    data() {
        const validatePassStrongCheck = (rule, value, callback) => {
            var regex = new RegExp('(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{8,30}') //包含大小字母、数字、特称字符，至少8个字符正则
            if (!regex.test(value)) {
                callback(new Error('密码复杂度太低（大小写字母与数字结合且不小于8位）'))
            } else {
                callback()
            }
        }
        return {
            showOrgChoice: false,
            component: null,
            changePwd: '123password',
            defaultRole: [],
            // 被选中角色ID
            ruleValidate: {
                name: [
                    { required: true, message: '姓名不能为空', trigger: 'blur' }
                ],
                loginId: [
                    { required: true, message: '警号不能为空', trigger: 'blur' }
                ],
                orgName: [
                    { required: true, message: '请选择所属机构' }
                ],
                pwd: [
                    { required: true, validator: validatePassStrongCheck, trigger: 'blur' }
                ],
                sex: [
                    { required: true, message: '请选择性别', trigger: 'change' }
                ],
                sname: [
                    { required: true, message: '简称不能为空', trigger: 'blur' }
                ],
                orderId: [
                    // { validator: this.validateNum, message: '请正确填写数字' }
                    { required: true, type: 'number', message: '排序不能为空', trigger: 'blur,change' }
                ]
            },
            formValidate:{
                orgName: '',
                orderId: '',
                isDisabled: '0',
                idCard: '',
                mobile: '',
                rylx:'04'
            },
            custom_loading: false,
            isMulti: false,
            checkAll: false,
            appId: this.$route.query.appId ? this.$route.query.appId : '',
            //  stopLogin:false
        }
    },
    mounted() {
        // 加密信息回显
        this.$set(this.formValidate, 'idCardUncode', this.formValidate.idCard)
        this.$set(this.formValidate, 'mobileUncode', this.formValidate.mobile)
        // 登录方式回显
    },
    watch: {
        'formValidate.name'() {
            if (this.action === 'add') {
                let pinyin = vPinyin.chineseToJp(this.formValidate.name)
                this.formValidate.scode = pinyin
            }
        }
    },
    created() {
        if (this.action == 'edit') {
            this.antiTamper()
        }
       
        // this.formValidate.validType = !this.formValidate.validType ? '1' : this.formValidate.validType
        // if (this.formValidate.validType == '2') {
        // }
        // if (this.action === 'add' && this.appId)
        //     this.getRolesByAppId();
    },
    methods: {
        ...mapActions(['postRequest']),
        validateCardNull(rule, value, callback) {
            if (this.action === 'edit' && this.formValidate.confirm === '1') callback()
            if (value && value.trim().length > 0) {
                let format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/
                if (!format.test(value)) {
                    callback(new Error('身份证号码不合规'))
                    return
                }
                // 区位码校验
                // 出生年月日校验  前正则限制起始年份为1900;
                let year = value.substr(6, 4), // 身份证年
                    month = value.substr(10, 2), // 身份证月
                    date = value.substr(12, 2), // 身份证日
                    time = Date.parse(month + '-' + date + '-' + year), // 身份证日期时间戳date
                    now_time = Date.parse(new Date()), // 当前时间戳
                    dates = (new Date(year, month, 0)).getDate()// 身份证当月天数
                if (time > now_time || date > dates) {
                    callback(new Error('出生日期不合规'))
                    return
                }
                let c = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2) // 系数
                let b = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2') // 校验码对照表
                let id_array = value.split('')
                let sum = 0
                for (let k = 0; k < 17; k++) {
                    sum += parseInt(id_array[k]) * parseInt(c[k])
                }
                if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
                    callback(new Error('身份证校验不合规'))
                    return
                }
                callback()
            } else {
                callback(new Error(rule.message))
            }
        },
        choiceCancel() {
            this.showOrgChoice = false
        },
        choiceOrgOk() {
            let data = this.$refs.orgChoice.selectedList
            if (data.length > 0) {
                this.formValidate.orgName = data[0].orgName
                this.formValidate.orgId = data[0].orgId
                this.formValidate.orgCode = data[0].orgId
            } else {
                this.$Notice.error({
                    title: '错误提示',
                    desc: '至少选择一个所属机构'
                })
                return
            }
            this.$nextTick(() => {
                this.$refs.formValidate.validateField('orgName')
            })
            this.showOrgChoice = false
        },
        searchOrg() {
            this.orgIds = this.formValidate.orgId
            this.showOrgChoice = true
        },
        validateNum(rule, value, callback) {
            if (isNaN(value) || parseInt(value) < 0) {
                callback(new Error('请输入正确的数字1'))
            } else {
                let num = parseInt(value)
                let n = Math.floor(Number(value))
                if (n != Infinity && String(n) === value + '' && n >= 0) {
                    callback()
                } else {
                    callback(new Error('请输入正确的数字'))
                }
            }
        },
        handleSubmit(name) {
            this.$refs[name].validate(valid => {
                if (valid) {
                    this.save_user_data()
                } else {
                    this.$Notice.error({
                        title: '错误提示',
                        desc: '表单验证不通过'
                    })
                }
            })
        },
        save_user_data() {
            // 重要信息加密
            let publicKey = '041ae8d67a6733e72f42221c3baeba183fcc96c055eb7674f4b3c81656198116255506871d2b364937629fef55c9331c3d0d866e4f1b2f7772cd95cb34fbf9ad80';
            // this.formValidate.idCard = SM2.encrypt(this.formValidate.idCardUncode, publicKey, {
            //     inputEncoding: 'utf8',
            //     outputEncoding: 'hex',
            //     mode: 'C1C2C3'
            // })
            // if (this.formValidate.mobileUncode) {
            //     this.formValidate.mobile = SM2.encrypt(this.formValidate.mobileUncode, publicKey, {
            //         inputEncoding: 'utf8',
            //         outputEncoding: 'hex',
            //         mode: 'C1C2C3'
            //     })
            // } else {
            //     this.formValidate.mobile = ''
            // }

            this.custom_loading = true
            this.$store.dispatch('authPostRequest', { url: this.$path.pm_gqSave, params: Object.assign(this.formValidate) }).then(data => {
                this.custom_loading = false
                if (data.success) {
                    this.$Notice.success({
                        title: '成功提示',
                        desc: data.msg
                    })
                    this.on_show_table(true)
                } else {
                    this.$Notice.error({
                        title: '错误提示',
                        desc: data.msg
                    })
                }
            })
        },
        on_show_table(isRefreshTable) {
            this.$refs.formValidate.resetFields()
            this.$emit('on_show_table', isRefreshTable)
        },
        on_select(data) {
            this.formValidate.orgName = data[0].orgName
            this.formValidate.orgId = data[0].orgId
            //新增时根据机构默认orderID,编辑用户时忽略
            // if (this.action === 'add')
            //     this.getUserOrderId();
        },
        // getUserOrderId() {
        //     this.$store.dispatch('postRequest', {
        //         url: this.$path.get_one_user_orgId_url, params: { orgId: this.formValidate.orgId }
        //     }).then(resp => {
        //         if (resp.success) {
        //             if (resp.data)
        //                 this.formValidate.orderId = resp.data.orderId + 1;
        //             else
        //                 this.formValidate.orderId = 1;
        //         }
        //     })
        // },
        antiTamper() {
            console.log('this.formValidate',this.formDate);
            
            this.$store.dispatch('authGetRequest', {url: this.$path.pm_getUser,params: {id: this.formDate.id}}).then(resp => {
                    if (resp.success) {
                        this.formValidate = resp.data
                    } else {
                        this.$Modal.warning({
                            title: "警告",
                            content: resp.data
                        })
                    }
                })
        },
    }
}
</script>

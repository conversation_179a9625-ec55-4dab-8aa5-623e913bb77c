let menuMode=serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/record',
    name: 'record',
    meta: {
      title: '一事一档'
    },
    redirect: '/record/event',
    component: menuMode=='side'?mainNew:main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'event',
        name: 'event',
        meta: {
          title: '一事一档',
          menu: true,
          bread: true
        },
        component: () => import('@/view/record/event/index.vue')
      },
      {
        path: "detail",
        name: "eventDetail",
        meta: {
          title: "查看详情",
          menu: true,
          bread: true,
        },
        component: () => import("@/view/record/event/detail.vue"),
      },
      {
        path: "apply",
        name: "eventApply",
        meta: {
          title: "创建事件",
          menu: true,
          bread: true,
        },
        component: () => import("@/view/record/event/apply.vue"),
      }
    ]
  }
]

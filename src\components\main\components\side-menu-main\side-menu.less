.side-menu-wrapper {
  user-select: none;
  background: #1f3466;

  .menu-collapsed {
    padding-top: 10px;

    .ivu-dropdown {
      width: 100%;

      .ivu-dropdown-rel a {
        width: 100%;
      }
    }

    .ivu-tooltip {
      width: 100%;

      .ivu-tooltip-rel {
        width: 100%;
      }

      .ivu-tooltip-popper .ivu-tooltip-content {
        .ivu-tooltip-arrow {
          border-right-color: #fff;
        }

        .ivu-tooltip-inner {
          background: #fff;
          color: #495060;
        }
      }
    }


  }

  a.drop-menu-a {
    display: inline-block;
    padding: 6px 15px;
    width: 100%;
    text-align: center;
    color: #495060;
  }
}

.menu-title {
  padding-left: 6px;
}

.ivu-menu {
  background: #1f3466;
}


.bsp-main .ivu-menu-item {
  width: 100% !important;
  position: relative;
}

.bsp-main .ivu-menu-submenu-title {
  position: relative;
}

.side-menu-wrapper .ivu-menu.ivu-menu-dark {
  padding-top: 0 !important;
}

.side-menu-wrapper .ivu-menu .ivu-menu-item {
  height: 70px !important;
}

.side-menu-wrapper .ivu-menu .ivu-menu-item>span {
  position: absolute;
  left: 16px;
}

.bsp-main .ivu-menu-submenu-title>span {
  position: absolute;
  left: 16px;
}

.side-menu-wrapper>ul {
  height: calc(~'100vh - 60px') !important;
  overflow-y: auto;
  background: #1f3466 !important;
}
.side-menu-wrapper .ivu-menu .ivu-menu-submenu{
  width: 96% !important;
}
.side-menu-wrapper .ivu-menu .ivu-menu-submenu.ivu-menu-opened .ivu-menu-item>span {
    left: 30px !important;
}
.side-menu-wrapper .ivu-menu /deep/ .ivu-menu-item:hover{
    background: #001529 !important;
}

import { acpCom } from './base'


export default {

  // 创建会议记录
  zh_MeetingCreate: acpCom + '/acp/zh/meetingRecords/create',
  // 更新会议记录
  zh_MeetingUpdate: acpCom + '/acp/zh/meetingRecords/update',
  // 获得会议记录
  zh_MeetingGet: acpCom + '/acp/zh/meetingRecords/get',



  // -----------------------------死亡登记----------------------

  // 根据id查询详情 
  zh_GetDeadDetail: acpCom + '/acp/zh/deathRegister/getDeathRegisterDetailById',
  // 死亡登记提交
  zh_DeathRegisterSubmit: acpCom + '/acp/zh/deathRegister/submit',

  // 死亡鉴定提交
  zh_deathAppraiseSubmit: acpCom + '/acp/zh/deathRegister/deathAppraiseSubmit',

  // 尸体处理保存
  zh_dcorpseHandleSave: acpCom + '/acp/zh/deathRegister/corpseHandleSave',

  // 死亡登记审批
  zh_deathRegisterApprove: acpCom + '/acp/zh/deathRegister/deathRegisterApprove',

  // ---------------------------民警值班管理------------------------
  // 新增值班模板
  zh_DutyTemplateAdd: acpCom + '/acp/zh/staffDutyTemplate/add',

  // 值班排版首页列表
  zh_indexListByDutyDate: acpCom + '/acp/zh/staffDutyIndex/indexListByDutyDate',

  // 实战平台-综合管理-值班安排首页列表
  zh_listByDutyDate: acpCom + '/acp/zh/staffDutyRecord/listByDutyDate',

  // 实战平台-综合管理-值班安排首页默认启用状态表头
  zh_defaultHeader: acpCom + '/acp/zh/staffDutyRecord/defaultHeader',

  //  值班管理-值班安排-新增值班
  zh_staffDutyRecordAdd: acpCom + '/acp/zh/staffDutyRecord/add',

  //  删除模板
  zh_TemplateDelete: acpCom + '/acp/zh/staffDutyTemplate/delete',

  // 查看模板
  zh_GetTemplateInfo: acpCom + '/acp/zh/staffDutyTemplate/info',

  // 查看模板
  zh_Templateupdate: acpCom + '/acp/zh/staffDutyTemplate/update',

  // 值班管理-模板管理-切换检查
  zh_TempOpenCheck: acpCom + '/acp/zh/staffDutyTemplate/tempOpenCheck',

  //  值班管理-模板管理-切换
  zh_TempOpen: acpCom + '/acp/zh/staffDutyTemplate/tempOpen',

  //  值班导出
  zh_exportByDutyDate: acpCom + '/acp/zh/staffDutyIndex/exportByDutyDate',


  // 创建值班模板班次
  zh_staffDutyShiftCreate: acpCom + '/acp/zh/staffDutyShift/create',

  // 获取值班模板班次列表
  zh_getListByOrgCode: acpCom + '/acp/zh/staffDutyShift/getListByOrgCode',

  // 创建值班模板班组信息
  zh_staffDutyTeamCreate: acpCom + '/acp/zh/staffDutyTeam/create',

  // 获得模板班次
  zh_getTeamList: acpCom + '/acp/zh/staffDutyTeam/getListByOrgCode',

  //  获取值班模板班组及人员信息
  zh_getTeamAndPerson: acpCom + '/acp/zh/staffDutyTeam/getTeamAndPerson',

  //  值班管理-复制排班-日期过滤
  zh_checkCopyDate: acpCom + '/acp/zh/staffDutyIndex/checkCopyDate',

  //  复制排班-日期校验(下一步校验)
  zh_checkCopyDateNext: acpCom + '/acp/zh/staffDutyIndex/checkCopyDateNext',

  //  值班管理-复制排班-提交覆盖
  zh_checkHasData: acpCom + '/acp/zh/staffDutyIndex/checkHasData',

  // 复制排班-确认提交排班
  zh_copyData: acpCom + '/acp/zh/staffDutyIndex/copyData',
  // 保存值班模板班组及人员信息

  zh_saveTeamAndPerson: acpCom + '/acp/zh/staffDutyTeam/saveTeamAndPerson',

  // 班组班次首页

  zh_shiftTeamIndex: acpCom + '/acp/zh/staffDutyTeam/shiftTeamIndex',

  // 保存人员信息

  zh_createOrUpdateBatch: acpCom + '/acp/zh/staffDutyTeamPerson/createOrUpdateBatch',


  // 获取班组信息
  zh_getTeamAndPersonByShiftId: acpCom + '/acp/zh/staffDutyTeam/getTeamAndPersonByShiftId',


  // -------------------------------值班督导---------------------------------------

  //  新增或者更新综合管理-值班管理-值班督导规则配置
  zh_createOrUpdate: acpCom + '/acp/zh/dutySuperviseRule/createOrUpdate',


  // 获得综合管理-值班管理-值班督导规则配置列表

  zh_dutySuperviseRuleList: acpCom + '/acp/zh/dutySuperviseRule/list',

  // 更新综合管理-值班管理-值班督导记录
  zh_dutySuperviseUpdate: acpCom + '/acp/zh/dutySuperviseRecord/update',

  // 更新综合管理-值班管理-值班督导记录
  zh_captchaGenerate: acpCom + '/api/captcha/generate',


  // -------------------------------------数据固化------------------------------
  // 获得实战平台-数据固化-每日数据报送(看守所)
  ds_getBySolidificationDate: acpCom + '/acp/ds/dailyDataSubmitKss/getBySolidificationDate',

  // 获得实战平台-数据固化-每日数据报送(看守所)-id
  ds_getBySolidificationDateId: acpCom + '/acp/ds/dailyDataSubmitKss/get',


  //获得实战平台-数据固化-每周数据报送-律师
  ds_getweeklyLawyerTop: acpCom + '/acp/ds/weeklyLawyerTop/getByWeeklyDataSubmitId',

  //获得实战平台-数据固化-每周数据报送-监管人员
  ds_getweeklyPrisonerTop: acpCom + '/acp/ds/weeklyPrisonerTop/getByWeeklyDataSubmitId',


  // 获得实战平台-数据固化-每周数据报送(看守所)
  ds_weeklgetByDate: acpCom + '/acp/ds/weeklyDataSubmitKss/getByDate',

  // 获得实战平台-数据固化-每周数据报送(看守所)-id
  ds_weeklgetByDateId: acpCom + '/acp/ds/weeklyDataSubmitKss/get',

  // 获得实战平台-数据固化-每周数据报送(拘留所)

  ds_JlsgetByDate: acpCom + '/acp/ds/weeklyDataSubmitJls/getByDate',

  // 获得实战平台-数据固化-每周数据报送(拘留所)-id
  ds_JlsgetByDateId: acpCom + '/acp/ds/weeklyDataSubmitJls/get',

  // 获得实战平台-数据固化-每日数据报送(拘留所)
  ds_JlsgetBySolidificationDate: acpCom + '/acp/ds/dailyDataSubmitJls/getBySolidificationDate',
  // 获得实战平台-数据固化-每日数据报送(拘留所)-id
  ds_JlsgetBySolidificationDateId: acpCom + '/acp/ds/dailyDataSubmitJls/get',


  // ----------------------------------定屏监控----------------------------------
  // 查询手环告警信息
  pm_getAlarmByParam: acpCom + '/acp/pm/localsenseTagPerson/getAlarmByParam',

  // 智能腕带模块绑定被监管人员
  pm_znwdCreate: acpCom + '/acp/pm/localsenseTagPerson/znwdCreate',
  // 解绑标签
  pm_znwdUnbind: acpCom + '/acp/pm/localsenseTagPerson/unbind',
  // 查询标签电量,体征信息
  pm_getBatteryByPersonId: acpCom + '/acp/pm/localsenseTagPerson/getBatteryByPersonId',
  // 根据标签ID查询绑定信息
  pm_getByTagId: acpCom + '/acp/pm/localsenseTagPerson/getByTagId',
  // 入所被监管人员绑定标签(手环)
  pm_rsCreate: acpCom + '/acp/pm/localsenseTagPerson/rsCreate',

  // 民警绑定工牌
   pm_mjCreate: acpCom + '/acp/pm/localsenseTagPerson/mjCreate',

  // ----------------------------------门禁控制----------------------------------


  // 同步门禁点信息
  pm_deviceDoorUpdate: acpCom + '/acp/pm/deviceDoor/sync',

  // 门禁绑定监室
  pm_bindRoom: acpCom + '/acp/pm/deviceDoor/bindRoom',

  // 获取门禁控制事件列表

  pm_getDoorControlEventList: acpCom + '/acp/pm/deviceDoor/getDoorControlEventList',

  


  // ----------------------------------基础信息管理----------------------------------
  // 监室起居条件信息保存
  api_qjtjSave: acpCom + '/api/jcxxqjtjxx/save',

  // 监室起居条件信息保存批量

  api_qjtjBatchSave: acpCom + '/api/jcxxqjtjxx/batchSave',

  // 实战平台-监管管理-工勤信息保存
  pm_gqSave: acpCom + '/acp/pm/user/gqSave',
  // 实战平台-监管管理-民警信息保存
  pm_mjSave: acpCom + '/acp/pm/user/mjSave',
  //  实战平台-监管管理-辅警信息保存
  pm_fjSave: acpCom + '/acp/pm/user/fjSave',
  //实战平台-监管管理-医务信息保存
  pm_ywSave: acpCom + '/acp/pm/user/ywSave',

  // 获得实战平台-监管管理-用户
  pm_getUser: acpCom + '/acp/pm/user/get',

  // 删除实战平台-监管管理-工勤工作人员
  pm_deleteGq: acpCom + '/acp/pm/user/deleteGq',

  get_all_app_roles_by_appIds: '/bsp-uac/uac/role/getAppRolesByAppIds',
  uac_app_list_url: '/bsp-uac/uac/app/getAppList',
  uac_get_roles_by_user_id: '/bsp-uacuac/user/role/getRoleByUserId',
  uac_save_roles_by_user_id: '/bsp-uacuac/user/role/saveUserRoles',
  get_user_job_url: '/bsp-uac/uac/job/user/getJobByUser',
  job_role_all_url: '/bsp-uac/uac/role/job/getRoleByJob',
  user_org_role_all_url: '/bsp-uac/uac/user/role/getUserRoleByOrgId',
  job_user_save_url: '/bsp-uac/uac/job/user/saveJobUser'



}
<!--
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-07-17 23:56:31
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-07-23 14:56:23
 * @FilePath: \rs-acp-web\src\components\bsp-upload\FileUpload.vue
 * @Description: A robust file upload component with picture wall layout.
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="file-upload" v-viewer>
    <div
      class="upload-list-item"
      v-for="(item, index) in uploadList"
      :key="index"
      :style="{ width: width + 'px' }"
    >
      <div
        class="upload-list-item-content"
        :style="{ width: width + 'px', height: height + 'px' }"
      >
        <template
          v-if="
            item.status === 'finished' ||
            item.status === 'ready' ||
            item.status === 'uploading'
          "
        >
          <img :src="item.url" v-if="isImage(item)" class="upload-image" />
          <div v-else class="file-icon">
            <i :class="getFileIcon(item.name)" class="file-icon-fa"></i>
          </div>
          <div class="upload-list-cover">
            <Icon
              type="ios-eye-outline"
              @click.native="handleView(item)"
            ></Icon>
            <Icon
              v-if="!isViewMode && item.status !== 'uploading'"
              type="ios-trash-outline"
              @click.native="handleRemove(item)"
            ></Icon>
          </div>
          <Progress
            v-if="item.status === 'uploading'"
            :percent="item.percentage"
            :stroke-width="2"
            hide-info
          ></Progress>
        </template>
      </div>
      <div class="file-name" :title="item.name">{{ item.name }}</div>
    </div>

    <Upload
      v-if="!isViewMode"
      v-show="uploadList.length < maxCount"
      ref="upload"
      :show-upload-list="false"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :format="format"
      :max-size="maxSize"
      :on-exceeded-size="handleMaxSize"
      :on-format-error="handleFormatError"
      :accept="acceptFormat"
      multiple
      type="drag"
      :action="uploadUrl"
      :style="{ width: width + 'px', height: height + 'px' }"
    >
      <div
        :style="{
          width: width + 'px',
          height: height + 'px',
          lineHeight: height + 'px',
        }"
      >
        <Icon type="ios-add" :size="width / 2"></Icon>
      </div>
    </Upload>

    <div class="upload-button-container" v-if="!autoUpload && !isViewMode">
      <Button
        icon="ios-cloud-upload-outline"
        @click="handleUpload"
        :loading="loadingStatus"
        :disabled="!hasReadyFiles"
      >
        <span v-if="!loadingStatus">{{ buttonText }}</span>
        <span v-else>上传中...</span>
      </Button>
    </div>
  </div>
</template>

<script>
export default {
  name: "FileUpload",
  props: {
    defaultList: {
      type: Array,
      default: () => [],
    },
    uploadUrl: {
      type: String,
      default() {
        if (this.$path && this.$path.upload_fj) {
          return this.$path.upload_fj;
        }
        console.warn(
          "FileUpload: this.$path.upload_fj is not defined. Please configure the upload path."
        );
        return "";
      },
    },
    mode: {
      type: String,
      default: "edit",
      validator: (value) => ["edit", "view"].includes(value),
    },
    width: {
      type: Number,
      default: 60,
    },
    height: {
      type: Number,
      default: 60,
    },
    autoUpload: {
      type: Boolean,
      default: false,
    },
    maxSize: {
      type: Number,
      default: null,
    },
    maxCount: {
      type: Number,
      default: Infinity,
    },
    buttonText: {
      type: String,
      default: "上传",
    },
    format: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      uploadList: [],
      loadingStatus: false,
    };
  },
  computed: {
    isViewMode() {
      return this.mode === "view";
    },
    hasReadyFiles() {
      return this.uploadList.some((item) => item.status === "ready");
    },
    acceptFormat() {
      if (this.format.length === 0) {
        return null;
      }
      return this.format.map((ext) => `.${ext}`).join(",");
    },
  },
  watch: {
    defaultList: {
      handler(list) {
        const listCopy = JSON.parse(JSON.stringify(list));
        this.uploadList = listCopy.map((item) => {
          item.status = "finished";
          item.percentage = 100;
          item.uid = Date.now() + Math.random();
          return item;
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleView(file) {
      if (this.isImage(file)) {
        const imageList = this.uploadList
          .filter((item) => this.isImage(item))
          .map((item) => item.url);
        const index = imageList.findIndex((url) => url === file.url);
        if (index > -1) {
          this.$viewerApi({
            images: imageList,
            options: {
              initialViewIndex: index,
            },
          });
        }
      } else {
        this.handleDownload(file);
      }
    },
    isImage(file) {
      const imageFormats = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      // 对于 blob url, file.name 可能不存在，但 file.type 存在
      const fileType = file.type || "";
      const fileName = file.name || "";
      if (fileType.startsWith("image/")) {
        return true;
      }
      const fileExtension = fileName.split(".").pop().toLowerCase();
      return imageFormats.includes(fileExtension);
    },
    getFileIcon(fileName = "") {
      const ext = fileName.split(".").pop().toLowerCase();
      const fileIcons = {
        // 文档类
        pdf: "fas fa-file-pdf text-red",
        doc: "fas fa-file-word text-blue",
        docx: "fas fa-file-word text-blue",
        // 表格类
        xls: "fas fa-file-excel text-green",
        xlsx: "fas fa-file-excel text-green",
        csv: "fas fa-file-csv text-green",
        // 演示文稿类
        ppt: "fas fa-file-powerpoint text-orange",
        pptx: "fas fa-file-powerpoint text-orange",
        // 压缩文件类
        zip: "fas fa-file-archive text-purple",
        rar: "fas fa-file-archive text-purple",
        "7z": "fas fa-file-archive text-purple",
        tar: "fas fa-file-archive text-purple",
        gz: "fas fa-file-archive text-purple",
        // 文本类
        txt: "fas fa-file-alt text-gray",
        md: "fas fa-file-alt text-gray",
        rtf: "fas fa-file-alt text-gray",
        // 图片类
        jpg: "fas fa-file-image text-pink",
        jpeg: "fas fa-file-image text-pink",
        png: "fas fa-file-image text-pink",
        gif: "fas fa-file-image text-pink",
        bmp: "fas fa-file-image text-pink",
        svg: "fas fa-file-image text-pink",
        webp: "fas fa-file-image text-pink",
        // 音频类
        mp3: "fas fa-file-audio text-teal",
        wav: "fas fa-file-audio text-teal",
        flac: "fas fa-file-audio text-teal",
        aac: "fas fa-file-audio text-teal",
        // 视频类
        mp4: "fas fa-file-video text-indigo",
        avi: "fas fa-file-video text-indigo",
        mov: "fas fa-file-video text-indigo",
        wmv: "fas fa-file-video text-indigo",
        flv: "fas fa-file-video text-indigo",
        mkv: "fas fa-file-video text-indigo",
        // 代码类
        js: "fas fa-file-code text-yellow",
        html: "fas fa-file-code text-yellow",
        css: "fas fa-file-code text-yellow",
        json: "fas fa-file-code text-yellow",
        xml: "fas fa-file-code text-yellow",
        sql: "fas fa-file-code text-yellow",
        py: "fas fa-file-code text-yellow",
        java: "fas fa-file-code text-yellow",
        php: "fas fa-file-code text-yellow",
        vue: "fas fa-file-code text-yellow",
      };
      return fileIcons[ext] || "fas fa-file text-gray";
    },
    handleDownload(file) {
      window.open(file.url);
    },
    handleRemove(file) {
      // 释放由 URL.createObjectURL 创建的 URL
      if (file.url && file.url.startsWith("blob:")) {
        URL.revokeObjectURL(file.url);
      }
      const index = this.uploadList.findIndex((item) => item.uid === file.uid);
      if (index > -1) {
        const removedFile = this.uploadList.splice(index, 1)[0];
        this.$emit("on-remove", removedFile, this.uploadList);
      }
    },
    handleProgress(event, file) {
      const targetFile = this.uploadList.find(
        (item) =>
          item.name === file.name &&
          item.size === file.size &&
          item.status === "uploading"
      );
      if (targetFile) {
        this.$set(targetFile, "percentage", event.percent);
      }
    },
    handleSuccess(res, file) {
      // 使用更可靠的匹配方式，例如在 before-upload 中存储的唯一标识
      const targetFile = this.uploadList.find(
        (item) =>
          item.name === file.name &&
          item.size === file.size &&
          item.status === "uploading"
      );

      if (res.code === 0 && res.data) {
        if (targetFile) {
          // 更新文件状态和信息
          this.$set(targetFile, "status", "finished");
          this.$set(targetFile, "url", res.data.url);
          this.$set(targetFile, "name", res.data.originalFilename || file.name);
          this.$set(targetFile, "response", res.data); // 附加服务器响应
          this.$set(targetFile, "percentage", 100);
          this.$set(targetFile, "id", res.data.id);

          // 确保事件发出的是更新后的完整列表
          this.$emit("on-success", res, targetFile, this.uploadList);
        } else {
          console.error(
            "FileUpload Success: Could not find target file in uploadList.",
            file
          );
        }
      } else {
        if (targetFile) {
          this.$set(targetFile, "status", "fail");
          this.handleRemove(targetFile);
        }
        this.$Message.error(res.msg || `文件 ${file.name} 上传失败.`);
        // 即使失败，也发出事件，让外部知道
        this.$emit("on-error", res, targetFile, this.uploadList);
      }
    },
    handleError(error, file) {
      const targetFile = this.uploadList.find(
        (item) =>
          item.name === file.name &&
          item.size === file.size &&
          item.status === "uploading"
      );
      if (targetFile) {
        this.$set(targetFile, "status", "fail");
        this.$Message.error(`文件 ${file.name} 上传失败: ${error.toString()}`);
        this.$emit("on-error", error, targetFile, this.uploadList);
      }
    },
    handleMaxSize(file) {
      this.$Notice.warning({
        title: "超出文件大小限制",
        desc: `文件 ${file.name} 太大，不能超过 ${this.maxSize}KB。`,
      });
    },
    handleFormatError(file) {
      this.$Notice.warning({
        title: "文件格式不正确",
        desc: `文件 ${file.name} 格式不正确，请选择 ${this.format.join(
          ", "
        )} 格式的文件。`,
      });
    },
    handleBeforeUpload(file) {
      const check = this.uploadList.length < this.maxCount;
      if (!check) {
        this.$Notice.warning({
          title: `最多只能上传 ${this.maxCount} 个文件。`,
        });
        return false;
      }

      const newFile = {
        raw: file,
        uid: file.uid || Date.now() + Math.random(), // uid is kept for the :key prop
        name: file.name,
        size: file.size, // CRITICAL: Store file size for reliable matching
        type: file.type,
        url: URL.createObjectURL(file),
        status: this.autoUpload ? "uploading" : "ready",
        percentage: 0,
      };
      this.uploadList.push(newFile);
      return this.autoUpload;
    },
    handleUpload() {
      const toUploadFiles = this.uploadList.filter(
        (item) => item.status === "ready"
      );
      if (toUploadFiles.length === 0) {
        this.$Message.info("没有需要上传的文件。");
        return;
      }
      this.loadingStatus = true;
      toUploadFiles.forEach((file) => {
        file.status = "uploading";
        // 使用存储在 newFile 对象中的原始 File 对象进行上传
        this.$refs.upload.post(file.raw);
      });
      // 这是一个简化的处理，实际可能需要等待所有文件上传完成
      setTimeout(() => {
        this.loadingStatus = false;
      }, 2000); // 假设2秒后上传状态结束
    },
  },
};
</script>

<style lang="less">
.file-upload {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;

  .upload-list-item {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin: 0 8px 8px 0;
    width: auto;
  }

  .upload-list-item-content {
    text-align: center;
    line-height: 60px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);

    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .file-icon {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .file-icon-fa {
        font-size: 32px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .upload-list-cover {
      display: none;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;
      pointer-events: none;

      i {
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        margin: 0 5px;
        pointer-events: auto;
      }
    }

    &:hover .upload-list-cover {
      display: flex;
    }
  }

  .file-name {
    margin-top: 4px;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #515a6e;
    white-space: normal;
    word-break: break-all;
  }

  .ivu-upload {
    margin-bottom: 8px;
  }

  .upload-button-container {
    margin-left: 10px;
    align-self: center;
    margin-bottom: 8px;
  }
}

// Font Awesome 文件图标颜色类
.text-red { color: #e74c3c !important; }      // PDF - 红色
.text-blue { color: #3498db !important; }     // Word - 蓝色
.text-green { color: #27ae60 !important; }    // Excel - 绿色
.text-orange { color: #f39c12 !important; }   // PowerPoint - 橙色
.text-purple { color: #9b59b6 !important; }   // 压缩文件 - 紫色
.text-gray { color: #7f8c8d !important; }     // 文本文件 - 灰色
.text-pink { color: #e91e63 !important; }     // 图片 - 粉色
.text-teal { color: #1abc9c !important; }     // 音频 - 青色
.text-indigo { color: #6c5ce7 !important; }   // 视频 - 靛蓝色
.text-yellow { color: #f1c40f !important; }   // 代码 - 黄色
</style>

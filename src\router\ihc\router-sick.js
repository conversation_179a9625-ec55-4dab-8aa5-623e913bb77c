let menuMode = localStorage.getItem('menuMode')
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/sick',
    name: 'sick',
    meta: {
      title: '病情管理'
    },
    component: menuMode=='side'?mainNew:main,//() => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'manage',
        name: 'sickManage',
        meta: {
          title: '重(特)病号登记',
          menu: true,
          bread: true
        },
        component: () => import('@/view/sick/manage/index.vue')
      }
    ]
  }
]

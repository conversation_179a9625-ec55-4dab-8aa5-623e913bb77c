<style lang="less">
@import './login.less';
</style>
<template>
  <div class="login">
    <div class="login-con">
      <Card icon="log-in" :bordered="false">
        <div class="header-container">
          <img src="../../assets/images/jh.png" style="width: 120px; height: 120px;" alt="">
          <p class="header-title">{{ $config.loginTitle }}</p>
        </div>
        <ul class="login-type">
          <li :class="{ active: component === 'pwd' }" @click="changeType('pwd')">账号登录</li>
          <li :class="{ active: component === 'pki' }" @click="changeType('pki')">PKI登录</li>
        </ul>
        <div class="form-con">
          <transition mode="out-in">
            <component :is="component" @init="init"></component>
          </transition>
        </div>
        <!-- <div class="form-con">
          <Form ref="loginForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
            <FormItem  prop="userName">
              <Input size="large" v-model="form.userName" :autofocus="true" placeholder="请输入用户名"></Input>
            </FormItem>
            <FormItem prop="password">
              <Input size="large" type="password" v-model="form.password" placeholder="请输入密码"></Input>
            </FormItem>
            <div style="line-height:20px;height:20px;color:#ff6600;font-size:14px;"><span v-if="show_error"> <Icon type="md-close" size="18"></Icon>&nbsp;{{error}}</span></div>
            <FormItem>
              <Button @click="handleSubmit" size="large" type="primary" :loading="loading" long>登&nbsp;&nbsp;&nbsp;&nbsp;录</Button>
            </FormItem>
          </Form>
        </div> -->
      </Card>
    </div>
    <div
      style="font-size:10px;text-align:center;margin-top:30px; position: absolute; bottom: 18px; width: 100%; display: flex; justify-content: center;">
      <p style="font-weight: 400;font-size: 16px;color: #FFFFFF; font-family: Microsoft YaHei, Microsoft YaHei;">
        {{ $config.copyRight }}</p>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { SM3 } from 'gm-crypto'
import { Modal } from 'view-design'
import pwd from './components/pwd.vue'
import pki from './components/pki.vue'
import navGjb from './components/login-nav.vue'

export default {
  components: {
    pwd,
    pki,
    navGjb
  },
  data() {
    return {
      form: {
        userName: '',
        password: ''
      },
      loading: false,
      show_error: false,
      error: '',
      rules: {},
      appName: serverConfig.APP_MARK,
      component: '',
    }
  },
  methods: {
    ...mapActions([
      'access_token',
      'setting_session',
      'postRequest',
      'clear_session',
      'authGetRequest'
    ]),
    handleSubmit() {
      this.show_error = false
      if (!this.form.userName || !this.form.password) {
        this.show_error = true
        this.error = '请输入用户名和密码'
        return
      }
      this.login_processing()
    },
    login_processing() {
      this.loading = true
      this.access_token({ userName: this.form.userName, password: SM3.digest(this.form.password, 'utf8', 'base64'), path: this.$path.login_url }).then(res => {
        if (res.access_token) {
          this.loading = false
          localStorage.removeItem('tagNaveList')
          let hasQx = res.roleLevels.hasOwnProperty(this.appName)
          if (hasQx) {
            this.$store.state.common.userName = res.name
            localStorage.setItem('usreInfo', JSON.stringify(res))
            // this.$store.state.common.orgType = res.orgType
            localStorage.setItem('orgType', res.orgType)
            this.show_error = false
            this.setting_session(res).then(data => {
              // this.$router.push('/#/conflict/registration')
              this.$router.push({
                name: 'homePage'
              })
            })
          } else {
            this.$Notice.warning({
              title: '温馨提示',
              desc: res.msg || '未授权用户，请联系管理员'
            })

            // Modal.warning({
            //       title: '温馨提示',
            //       content: res.msg  || '未授权用户，请联系管理员'
            //     })
          }
        } else {
          this.show_error = true
          this.error = res.msg
          this.loading = false
        }
      })
    },
    get_menu() {
      this.$store.dispatch('get_permission').then((asyncRouter) => {
        if (asyncRouter[0] == null || asyncRouter[0].length < 1) {
          this.clear_session().then(res => {
            this.show_error = true
            this.error = '用户未授权菜单'
            this.loading = false
          })
        } else {
          this.$router.push({
            path: this.$config.homeName
          })
        }
      })
    },
    init() {
      // localStorage.getItem('appComp') ? '' : this.getMark()
      // 根据门户ID查询该门户下的组件默认布局信息
      let url = '/bsp-com/com/portalcust/finduserportallayouts'
      let params = { appId: this.appId, portalCatId: this.portalCatId } // appId: 应用ID  portalCatId: 门户分类ID
      this.authGetRequest({ url, params }).then(res => {
        if (res.success) {
          localStorage.setItem('isCustom', res.data.isCustom == 1)
          this.setManageMhPage(res.data.isCustom == 1)
          /* this.$router.push({
              path: '/'
          })*/
          this.$router.push({
            path: this.$router.currentRoute.query.redirect ? this.$router.currentRoute.query.redirect : '/'
          })
        }
      })
    },
    changeType(type) {
      this.component = type
    },
  },
  mounted() {
    this.component = 'pwd'
  }
}
</script>

<style scoped>
.login-con /deep/ .ivu-form-item-error-tip {
  margin: 10px 0;
  padding: 0px;
}

/* .ivu-form-item{margin-bottom: 30px;} */
.login-con /deep/ .ivu-card-head p {
  height: auto;
  line-height: initial;
}

/* .logoText{position: absolute;top: -45%;font-size: 50px;color: #fff;width: 1000px;text-align: center;left: 50%;margin-left: -500px;} */

/deep/.login-con .ivu-input {
  color: #000 !important;
}
</style>

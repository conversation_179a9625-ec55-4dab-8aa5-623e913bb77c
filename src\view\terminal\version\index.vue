<template>
  <div class="content-defaulet">
    <div class="content-defaulet-main">
      <tabs ref="tabs" v-show="!showFormCompnent" mark="zdgxupdate" :params="{}">

        <template slot="customHeadFunc" slot-scope="{ func,resetMethod,appendEqualFuncMark}">
          <Button type="primary" @click="changeAction('AddWebForm',null,resetMethod)"
                  v-if="appendEqualFuncMark('zdgxdweb')">
            上传
          </Button>
          <Button type="primary" @click="changeAction('AddAppForm',null,resetMethod)"
                  v-if="appendEqualFuncMark('zdgxlbapp')">
            上传
          </Button>
        </template>

        <template slot="customRowFunc"
                  slot-scope="{ func,hasPermission, row, index, resetMethod, funcMark,appendEqualFuncMark }">
          <Button v-if="appendEqualFuncMark('zdgxdweb')" type="primary"
                  @click="changeAction('download',row,resetMethod)">
            下载
          </Button>
          <Button v-if="appendEqualFuncMark('zdgxlbapp')" type="primary"
                  @click="changeAction('download',row,resetMethod)">
            下载
          </Button>
        </template>

      </tabs>
      <div v-if="showFormCompnent">
        <component v-bind:is="component" :pk="pk" @toback="onBack"></component>
      </div>
    </div>
  </div>
</template>

<script>
import tabs from "@/components/tabs/index.vue";
import AddWebForm from "./AddWebForm.vue";
import AddAppForm from "./AddAppForm.vue";
import {mapActions} from "vuex";


export default {
  components: {
    tabs,
    AddWebForm,
    AddAppForm
  },
  data() {
    return {
      pk: "",
      showFormCompnent: false,
      component: null,
      resetMethod: null,
    };
  },
  methods: {
    ...mapActions(["authGetRequest"]),
    onBack() {
      this.showFormCompnent = false;
      this.component = null;
      this.resetMethod();
    },
    changeAction(type, row, resetMethod) {
      if ('download' === type) {
        window.location.href = row.url
      } else {
        this.resetMethod = resetMethod;
        this.showFormCompnent = true;
        this.component = type;
        if (row) {
          this.pk = row.id;
        } else {
          this.pk = "";
        }
      }

    },
  },
};
</script>

<style lang="less" scoped>
.modal-content {
  font-size: 16px;
  text-align: center;

  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .modal-bottom {
    margin-top: 10px;

    .modal-bottom-dr {
      color: rgb(63, 118, 219);
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.modal-contents {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-contents-title {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>

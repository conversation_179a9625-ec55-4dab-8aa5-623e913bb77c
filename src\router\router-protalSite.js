import main from '@/components/main/main.vue'
// let menuMode = serverConfig.menuMode
let menuMode = localStorage.getItem('menuMode')
console.log('serverConfig', serverConfig)
console.log(menuMode, 'menuMode')
import mainNew from '@/components/main-menu-new/main.vue'
export default [

  {
    path: '/common',
    name: 'common',
    meta: {
      title: '实战系统'
    },
    sider: false,
    bread: true,
    component: menuMode == 'side' ? mainNew : main,
    children: [{
        meta: {
          title: '首页',
          hideInMenu: true
        },
        path: '/homePage',
        name: 'homePage',
        sider: false,
        bread: true,
        component: () => import('@/components/portalSite/index.vue')
      },
      {
        path: '/agencyNews',
        name: 'agencyNews',
        meta: {
          title: '更多待办消息',
          hideInMenu: true
        },
        component: () => import('@/view/agencyNews/index.vue')
      },
      {
        path: '/messageNotification',
        name: 'messageNotification',
        meta: {
          title: '消息通知',
          hideInMenu: true
        },
        component: () => import('@/components/mhPageComponents/messageNotification/index.vue')

      }
    ]
  }
]

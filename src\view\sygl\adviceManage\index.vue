<template>
  <div class="content-defaulet">
    <div class="content-defaulet-main">
      <tabs ref="tabs" v-show="showFormCompnent" mark="sxyz" @changeTabsMark="changeTabsMark" @saveForm="saveForm"
            @cancal="cancal" :params="{}">
        <template slot="customRowFunc" slot-scope="{ func,hasPermission, row, index, resetMethod, funcMark }">
          <!-- <Button v-if="hasPermission('sxyzcq:detail')" type="primary" @click="toDetail(row)">查看</Button> -->
          <Button v-if="hasPermission('sxyzcq:reg')" type="primary" @click="regInfo(row, resetMethod)">检查登记</Button>
          <!-- <Button v-if="hasPermission('sxyzdq:detail')" type="primary" @click="toDetail(row)">查看</Button> -->
          <Button v-if="hasPermission('sxyzdq:reg')" type="primary" @click="regInfo(row, resetMethod)">检查登记</Button>
        </template>
        <template slot="slot_checkup_num" slot-scope="{ row }">
            <span style="color: #2c2cf9; text-decoration: none; border-bottom: 1px solid; cursor: pointer"  @click="showDetailModalMethod(row)">{{
                row.checkup_num
              }}
            </span>

        </template>
      </tabs>
      <div v-if="!showFormCompnent">
        <component v-bind:is='component' @on_show_table="on_show_table" :modalTitle="modalTitle" :ryId="ryId"
                   :cfId="cfId" :type="lxType">
        </component>
      </div>
      <div v-if="modalShow">
        <Modal v-model="modalShow" title="检查登记" :mask-closable="false" width="850" :closable="true">
          <Form ref="formData" :model="formData" :label-width="160" label-colon>
            <Row>
              <Col span="12">
                <FormItem label="血压（mmHg）">
                  <Input v-model="formData.bloodPressureHeight" type="number" :min=0 placeholder="收缩压"
                         style="width:110px"></Input>&nbsp;&nbsp;/&nbsp;&nbsp;<Input v-model="formData.bloodPressureLow"
                                                                                     type="number" placeholder="舒张压"
                                                                                     :min=0 style="width:110px"></Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="体温（℃）" prop="temperature" style="width: 100%;">
                  <Input v-model="formData.temperature" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>

              <Col span="12">
                <FormItem label="心率（次/分）" prop="heartRate" style="width: 100%;">
                  <Input v-model="formData.heartRate" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="呼吸频率（次/分）" prop="breatheFrequency" style="width: 100%;">
                  <Input v-model="formData.breatheFrequency" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>

              <Col span="12">
                <FormItem label="脉搏（次/分）" prop="pulse" style="width: 100%;">
                  <Input v-model="formData.pulse" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="血氧（%）" prop="bloodOxygen" style="width: 100%;">
                  <Input v-model="formData.bloodOxygen" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="血糖（mmol/L）" prop="bloodSugar" style="width: 100%;">
                  <Input v-model="formData.bloodSugar" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="胰岛素（IU）" prop="insulin" style="width: 100%;">
                  <Input v-model="formData.insulin" type="number" :min=0 placeholder="请填写"></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24">
                <FormItem label="备注" prop="remark" style="width: 100%;">
                  <Input v-model="formData.remark" maxlength="500" type="textarea" show-word-limit
                         :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写"></Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
          <div slot="footer">
            <Button @click="modalShow = false" class="save">取消</Button>
            <Button type="primary" @click="sureSbmit" class="save">提交</Button>
          </div>
        </Modal>
      </div>

      <div v-if="showDetailModal">
        <Modal v-model="showDetailModal" :title="showDetailModalTitle" :mask-closable="false" :closable="true"
               width="1000">
          <rs-DataGrid ref="grids" :params="xqParams" funcMark="jcdjjl"></rs-DataGrid>
        </Modal>
      </div>

    </div>

  </div>
</template>

<script>
import tabs from '@/components/tabs/index.vue'
import detail from './detail.vue'
import {mapActions} from 'vuex'
import {getToken} from '@/libs/util'

export default {
  components: {
    tabs,
    detail,
  },
  data() {
    return {
      lxType: '',
      type: null,
      tabsMark: null,
      tabsName: null,
      showSave: true,
      showCancel: false,
      showFormCompnent: true,
      modalTitle: '',
      component: null,
      ryId: '',
      cfId: '',
      resetMethod: null,
      modalShow: false,
      formData: {},
      curId: "",
      showDetailModal: false,
      showDetailModalTitle:"",
      xqParams:{}
    }
  },
  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest']),
    regInfo(row, resetMethod) {
      this.modalShow = true;
      this.curId = row.id;
      this.resetMethod = resetMethod;
      this.$set(this, "formData", {
        prescribeId: row.id
      })
      console.log(row);
      console.log("dsdsds00");
    },
    on_show_table() {
      this.showFormCompnent = true
      this.component = null
      this.resetMethod()
    },
    changeTabsMark(e, name) {
      this.tabsMark = e
      this.tabsName = name
    },
    // 表单保存的回调
    saveForm(params, data) {
      console.log(111, params, data)
    },
    // 关闭的回调   返回接口参数   formData数据
    cancal(params, data) {
      this.type = null
      this.showFormCompnent = true
      this.resetMethod()
    },
    getResetMethod(resetMethod) {
      this.resetMethod = resetMethod
    },
    // 药品更新
    handleUpload() {
      let that = this
      that.importLoading = true
      return new Promise((resolve, reject) => {
        that.$Modal.confirm({
          title: '温馨提示',
          content: '是否确认导入药品数据？',
          loading: true,
          onOk: async () => {
            that.importParam.access_token = getToken()
            resolve(true)
          },
          onCancel: async () => {
            that.importLoading = false
            reject(false)
          }
        })
      })
    },
    // 回调方法
    handleSuccess(res) {
      this.$Modal.remove()
      if (res.code == 0) {
        this.$Notice.success({
          title: '成功提示',
          desc: res.data
        })
      } else {
        this.$Notice.error({
          title: '错误提示',
          desc: '数据导入失败'
        })
        window.location.href = res.data.url
      }
      this.importLoading = false
      this.resetMethod()
    },

    // 标准模版下载
    dowmLoadModal() {
      window.location.href = '/word/药库入库模版.xlsx'
    },

    showPlrk(resetMethod) {
      this.importPLStatus = 1
      this.showPldr = true
      this.getResetMethod(resetMethod)
    },

    // 批量导入上传
    handlePLUpload() {
      let that = this
      that.importPLLoading = true
      return new Promise((resolve, reject) => {
        that.$Modal.confirm({
          title: '温馨提示',
          content: '是否确认导入数据？',
          onOk: async () => {
            that.importPLStatus = 2
            that.importPLParam.access_token = getToken()
            resolve(true)
          },
          onCancel: async () => {
            that.importPLLoading = false
            reject(false)
          }
        })
      })
    },
    // 批量导入上传回调
    handlePLSuccess(res) {
      this.showPldr = false
      if (res.code == 0) {
        this.$Notice.success({
          title: '成功提示',
          desc: '导入成功'
        })
      } else if (res.code == 501) {
        this.yclbData = res.data
        this.showYclb = true
      } else {
        this.$Notice.error({
          title: '错误提示',
          desc: '数据导入失败'
        })
      }
      this.importPLLoading = false
    },

    cancalYclb() {
      this.showYclb = false
      this.resetMethod()
    },
    successOper() {
      this.resetMethod();
      this.modalShow = false;
    },
    toDetail(row) {
      this.modalTitle = "巡诊详情"
      this.ryId = row.id
      this.cfId = row.id;
      this.showFormCompnent = false
      this.component = "detail"
    },
    sureSbmit() {
      this.formData["prescribeId"] = this.curId;
      if (Object.values(this.formData).filter((item) => item).length === 1) {
        return this.$Notice.error({
          title: '错误提示',
          desc: '至少填写一项'
        })
      }
      this.authPostRequest({
        url: this.$path.internalMedicalPrescribeCheckupReg,
        params: this.formData
      }).then(res => {
        if (res.success) {
          this.$Notice.success({
            title: '提示',
            desc: res.msg || '保存成功'
          })
          this.successOper();
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: res.msg || '保存失败'
          })
        }
      })
    },
    showDetailModalMethod(row) {
      this.showDetailModalTitle = '登记记录'
      this.xqParams = {
        prescribeId: "",
      }
      this.xqParams.prescribeId = row.id
      this.showDetailModal = true
    }
  }
}
</script>

<style lang="less" scoped>
.modal-content {
  font-size: 16px;
  text-align: center;

  .modal-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px
  }

  .modal-bottom {
    margin-top: 10px;

    .modal-bottom-dr {
      color: rgb(63, 118, 219);
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.modal-contents {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-contents-title {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>

<template>
  <div>
    <Form ref="loginForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
      <FormItem prop="userName">
        <Input size="large" v-model="form.userName" :autofocus="true" placeholder="请输入用户名">
        <template #prefix>
          <!-- <Icon type="ios-contact"/> -->
          <img src="../../../assets/images/login/组 14868.png" style="margin-top: 8px;" alt="" />
        </template>
        </Input>
      </FormItem>
      <FormItem prop="password">
        <Input size="large" :type="showPassword ? 'text' : 'password'" v-model="form.password" placeholder="请输入密码">
        <template #prefix>
          <!-- <Icon type="ios-contact"/> -->
          <img src="../../../assets/images/login/组 14869.png" style="margin-top: 8px;" alt="" />
        </template>
        <template #suffix>
          <img v-if="showPassword" src="../../../assets/images/login/组 14877.png" alt=""
            style="margin-top: 8px; cursor: pointer;" @click="checkPassType">
          <img v-if="!showPassword" src="../../../assets/images/login/组 14878.png" alt=""
            style="margin-top: 8px; cursor: pointer;" @click="checkPassType">
          <!-- <img :src="showPassword ? '../../../assets/images/login/组 14877.png' : '../../../assets/images/login/组 14878.png'" style="margin-top: 8px; cursor: pointer;" @click="checkPassType" alt="" /> -->
        </template>
        </Input>
      </FormItem>
      <div style="line-height: 20px; height: 20px; color: #ff6600; font-size: 14px">
        <span v-if="show_error">
          <Icon type="md-close" size="18"></Icon>&nbsp;{{ error }}
        </span>
      </div>
      <FormItem>
        <Button @click="handleSubmit" size="large" type="primary" :loading="loading"
          long>登&nbsp;&nbsp;&nbsp;&nbsp;录</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { SM3 } from "gm-crypto";
import { getBspSystemParam } from '@/libs/bsp-system-param'

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        userName: "",
        password: "",
      },
      showPassword: false, // 控制密码明文/密文
      error: "",
      loading: false,
      show_error: false,
      rules: {},
      appName: serverConfig.APP_MARK,
    };
  },
  created() { },
  mounted() { },
  methods: {
    ...mapActions(["access_token", "setting_session", "clear_session"]),
    handleSubmit() {
      this.show_error = false
      if (!this.form.userName || !this.form.password) {
        this.show_error = true
        this.error = '请输入用户名和密码'
        return
      }
      localStorage.removeItem('menuMode')
      this.login_processing()
    },
    login_processing() {
      this.loading = true;
      this.access_token({
        userName: this.form.userName,
        password: SM3.digest(this.form.password, "utf8", "base64"),
        path: this.$path.login_url,
      }).then((res) => {
        if (res.access_token) {
          this.loading = false;
          console.log(res.roleLevels, "res.roleLevels", this.appName);
          let hasQx = res.roleLevels.hasOwnProperty(this.appName);
          if (hasQx) {
            this.$store.state.common.userName = res.name;
            localStorage.setItem("usreInfo", JSON.stringify(res));
            // this.$store.state.common.orgType = res.orgType
            localStorage.setItem("orgType", res.orgType);
            this.show_error = false;
            this.setting_session(res).then((data) => {
              // this.$router.push('/#/conflict/registration')
              this.getParams(res.roleCodeList)
            });
          } else {
            this.$Notice.warning({
              title: "温馨提示",
              desc: res.msg || "未授权用户，请联系管理员",
            });

            // Modal.warning({
            //       title: '温馨提示',
            //       content: res.msg  || '未授权用户，请联系管理员'
            //     })
          }
        } else {
          this.show_error = true;
          this.error = res.msg;
          this.loading = false;
        }
      });
    },
    async getParams(roleCodeList) {
      getBspSystemParam('medicalMenuConfiguration').then(res => {
        if (res) {
          let roleCodeListArr = res ? res.split(',') : []
          let tag = roleCodeList.some(item => roleCodeListArr.includes(item));
          if (tag) {
            localStorage.setItem('menuMode', 'top')
          } else {
            localStorage.setItem('menuMode', 'side')
          }
          // console.log(tag, 'tag')
          // window.location.reload(true); // true 表示从服务器加载而非缓存
          // this.$router.push({
          //   name: "homePage",
          // });
          // 替代方案（现代浏览器推荐）
          location.reload();
          // location.href = '/#/homePage';  // 重新导航到当前URL
          window.open('/#/homePage', '_self')
        } else {
          // this.$router.push({
          //   name: "homePage",
          // });
          // 替代方案（现代浏览器推荐）
          location.reload();
          window.open('/#/homePage', '_self')
        }
      }).catch(error => {
        // this.$router.push({
        //   name: "homePage",
        // });
        // 替代方案（现代浏览器推荐）
        window.open('/#/homePage', '_self')
      })
    },
    checkPassType() {
      this.showPassword = !this.showPassword;
    },
  },
};
</script>
